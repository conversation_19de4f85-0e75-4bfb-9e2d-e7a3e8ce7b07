{"version": "2.0.0", "tasks": [{"label": "Build Solution", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/hermetic/hermetic.sln", "/p:Configuration=Debug", "/p:Platform=Win32", "/m"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "Build Release", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/hermetic/hermetic.sln", "/p:Configuration=Release", "/p:Platform=Win32", "/m"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "Clean Solution", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/hermetic/hermetic.sln", "/t:Clean", "/p:Configuration=Debug", "/p:Platform=Win32"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "Build hermeticFrame", "type": "shell", "command": "msbuild", "args": ["${workspaceFolder}/hermetic/hermeticFrame/hermeticFrame.vcxproj", "/p:Configuration=Debug", "/p:Platform=Win32"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}]}