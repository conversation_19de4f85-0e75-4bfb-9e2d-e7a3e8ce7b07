﻿生成启动时间为 2025/7/19 15:13:34。
     1>项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\InfomationDll\InfomationDll.vcxproj”在节点 5 上(rebuild 个目标)。
     1>_PrepareForClean:
         正在删除文件“.\..\..\temp\InfomationDll\Debug\InfomationDll.lastbuildstate”。
       InitializeBuildStatus:
         正在创建“.\..\..\temp\InfomationDll\Debug\InfomationDll.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _USRDLL /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yc"stdafx.h" /Fp".\..\..\temp\InfomationDll\Debug\InfomationDll.pch" /Fo".\..\..\temp\InfomationDll\Debug\\" /Fd".\..\..\temp\InfomationDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
         _WIN32_WINNT not defined. Defaulting to _WIN32_WINNT_MAXVER (see WinSDKVer.h)
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _USRDLL /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yu"stdafx.h" /Fp".\..\..\temp\InfomationDll\Debug\InfomationDll.pch" /Fo".\..\..\temp\InfomationDll\Debug\\" /Fd".\..\..\temp\InfomationDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\CommonClass\ListCtrl\_ListCtrl.cpp _InfoDefination.cpp _InfoReadWrite.cpp _Information.cpp InfomationDll.cpp
         InfomationDll.cpp
         _Information.cpp
         _InfoReadWrite.cpp
         _InfoDefination.cpp
         _ListCtrl.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\listctrl\_listctrl.cpp(1123): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(105) : 参见“strcpy”的声明
         正在生成代码...
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D _DEBUG /D _VC80_UPGRADE=0x0600 /D _AFXDLL /l"0x0804" /nologo /fo".\..\..\temp\InfomationDll\Debug\InfomationDll.res" InfomationDll.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"../EXE/InfomationDll.dll" /INCREMENTAL /NOLOGO /DEF:".\InfomationDll.def" /MANIFEST /ManifestFile:".\..\..\temp\InfomationDll\Debug\InfomationDll.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:".\..\..\temp\InfomationDll.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"../EXE/InfomationDll.lib" /MACHINE:X86 /DLL .\..\..\temp\InfomationDll\Debug\InfomationDll.res
         .\..\..\temp\InfomationDll\Debug\_ListCtrl.obj
         .\..\..\temp\InfomationDll\Debug\_InfoDefination.obj
         .\..\..\temp\InfomationDll\Debug\_InfoReadWrite.obj
         .\..\..\temp\InfomationDll\Debug\_Information.obj
         .\..\..\temp\InfomationDll\Debug\InfomationDll.obj
         .\..\..\temp\InfomationDll\Debug\StdAfx.obj
     1>.\InfomationDll.def(4): warning LNK4017: DESCRIPTION 语句不支持目标平台；已忽略
            正在创建库 ../EXE/InfomationDll.lib 和对象 ../EXE/InfomationDll.exp
         InfomationDll.vcxproj -> D:\ASUS\Desktop\科瑞2\hermetic\hermetic\InfomationDll\.\..\exe\InfomationDll.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"D:\ASUS\Desktop\科瑞2\hermetic\hermetic\InfomationDll\.\..\exe\InfomationDll.dll.manifest" /manifest .\..\..\temp\InfomationDll\Debug\InfomationDll.dll.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         正在删除文件“.\..\..\temp\InfomationDll\Debug\InfomationDll.unsuccessfulbuild”。
         正在对“.\..\..\temp\InfomationDll\Debug\InfomationDll.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\InfomationDll\InfomationDll.vcxproj”(rebuild 个目标)的操作。

生成成功。

已用时间 00:00:03.86
