#if !defined(AFX_STATICPRO_H__F3636172_8038_4675_A09C_85DE4A3374C1__INCLUDED_)
#define AFX_STATICPRO_H__F3636172_8038_4675_A09C_85DE4A3374C1__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// StaticPro.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CStaticPro window

class CStaticPro : public CStatic
{
// Construction
public:
	CStaticPro();
    void SetBackColor(COLORREF clrBack);
    void SetTextColor(COLORREF clrText, int nFontSize = -1);
	CFont* m_newFont;
	void NewFont(int nFontHeight);
	void NewFont1(int nFontHeight);

// Attributes
public:

// Operations
public:

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CStaticPro)
	protected:
	virtual LRESULT WindowProc(UINT message, WPARAM wParam, LPARAM lParam);
	//}}AFX_VIRTUAL

// Implementation
public:
	virtual ~CStaticPro();

	// Generated message map functions
protected:
	//{{AFX_MSG(CStaticPro)
	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	afx_msg HBRUSH CtlColor(CDC* pDC, UINT nCtlColor);
	//}}AFX_MSG

	DECLARE_MESSAGE_MAP()

	UINT m_nbrushColor;//2013.7.7
	CBrush m_brushBack;
    COLORREF m_clrBack;
    COLORREF m_clrText; 
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_STATICPRO_H__F3636172_8038_4675_A09C_85DE4A3374C1__INCLUDED_)
