// _Progress.h: interface for the C_Progress class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX__PROGRESS_H__40C36127_9A49_44A0_942D_32126877152F__INCLUDED_)
#define AFX__PROGRESS_H__40C36127_9A49_44A0_942D_32126877152F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ProgressBar.h"

class C_Progress  
{
public:
	virtual ~C_Progress();
	C_Progress(CStatusBar *wndStatusBar = NULL);
	

	void InitProgress(CString strPromptInfo, int imaxValue, int nPane=0);
	void SetOtherInfo(CString strOtherInfo);
	void SetPercentPos(int PercentValue);
	void SetStep();
	CString GetProgressInfo();
	void FreeBar();
		
private:
	CProgressBar *pBar;
	CStatusBar  *m_wndStatusBar;
	int nPercentValue;
	int maxValue;
	int curValue;
	CString strInfo;
	CString strInfoOther;
	int nLastPercent;
};

#endif // !defined(AFX__PROGRESS_H__40C36127_9A49_44A0_942D_32126877152F__INCLUDED_)
