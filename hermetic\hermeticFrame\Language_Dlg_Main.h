#pragma once

#include "Language_Universal.h"

enum e_Ctrl_Main
{
	eCtrl_Stc_Result_UL,
	eCtrl_Stc_Result_UR,
	eCtrl_Stc_Result_DL,
	eCtrl_Stc_Result_DR,
	eCtrl_Stc_Pressure_UL,
	eCtrl_Stc_Pressure_UR,
	eCtrl_Stc_Pressure_DL,
	eCtrl_Stc_Pressure_DR,
	eCtrl_Stc_Statistic,
	eCtrl_Stc_Num_OK,
	eCtrl_Stc_Num_NG,
	eCtrl_Stc_Num_Total,
	eCtrl_Stc_FPY,
	eCtrl_Stc_Num_SN_Total,
	eCtrl_Stc_SN_FPY,
	//eCtrl_Check_Scan,
	//eCtrl_Check_Scan_Judge,
	//eCtrl_Check_Run_Loop,
	eCtrl_Btn_Run_UL,
	eCtrl_Btn_Run_UR,
	eCtrl_Btn_Run_DL,
	eCtrl_Btn_Run_DR,
	eCtrl_Btn_Clean,
	eCtrl_Stc_Num_UP_LEFT,
	eCtrl_Stc_Num_UP_RIGHT,
	eCtrl_Stc_Num_DOWN_LEFT,
	eCtrl_Stc_Num_DOWN_RIGHT,
	eCtrl_Stc_Safe_UP_Distance,
	eCtrl_Stc_CODE_LENGTH_MAX,
	eCtrl_Stc_CODE_LENGTH_MIN,
	eCtrl_Stc_Mes_Info,
	eCtrl_Stc_Line,
	eCtrl_Stc_Machine,
	eCtrl_Stc_User,
	eCtrl_STC_CPU_UL,
	eCtrl_STC_CPU_UR,
	eCtrl_STC_CPU_DL,
	eCtrl_STC_CPU_DR,
	eCtrl_STC_LEAK_UL,
	eCtrl_STC_LEAK_UR,
	eCtrl_STC_LEAK_DL,
	eCtrl_STC_LEAK_DR,
	eCtrl_STC_CPK_UL,
	eCtrl_STC_CPK_UR,
	eCtrl_STC_CPK_DL,
	eCtrl_STC_CPK_DR,
	eCtrl_Stc_TESTRESULT,
	eCtrl_Stc_CODE_PARAM,
	eCtrl_Stc_CPU_VALUE,
	eCtrl_Stc_LEAK_AVG,
	eCtrl_Stc_CPK_VALUE,
	eCtrl_Stc_MES_LOGIN,		
	eCtrl_Stc_UPLOAD_PARAM,
	eCtrl_Main_Count,
};
enum e_Menu_Main
{
	eMenu_Set_Para,
	eMenu_Leak_Det,
	eMenu_Count,
	eMenu_Login,
	eMenu_Logout,
	eMenu_Lang,
	eMenu_Lang_CH,
	eMenu_Lang_EN,
	eMenu_Main_Count,
};

class CLanguage_Dlg_Main /*: public CDialogEx*/
{
public:
	CLanguage_Dlg_Main(void);
	~CLanguage_Dlg_Main(void);

	HWND		m_hWnd;
	CMenu		*m_pMenu;
	int			m_nIDC[eCtrl_Main_Count];
	CString		m_strCtrl[eLang_Count][eCtrl_Main_Count];
	CString		m_strMenu[eLang_Count][eMenu_Main_Count];

	bool Set_Hwnd(HWND hWnd);
	bool Set_Menu(CMenu *pMenu);
	bool Set_Control(e_Lang_Type eLang_Type);
	bool Set_Menu_Lang(e_Lang_Type eLang_Type);
	
};

