#pragma once


// CEditPro

class CEditPro : public CEdit
{
	DECLARE_DYNAMIC(CEditPro)

public:
	CEditPro();
	virtual ~CEditPro();

	void SetBackColor(COLORREF clrBack);
	void SetTextColor(COLORREF clrText, int nFontSize = -1);
	CFont* m_newFont;
	void NewFont(int nFontHeight);
	void NewFont1(int nFontHeight);

protected:
	virtual LRESULT WindowProc(UINT message, WPARAM wParam, LPARAM lParam);

	afx_msg HBRUSH OnCtlColor(CDC* pDC, CWnd* pWnd, UINT nCtlColor);
	afx_msg HBRUSH CtlColor(CDC* pDC, UINT nCtlColor);

	DECLARE_MESSAGE_MAP()
	UINT m_nbrushColor;//2013.7.7
	CBrush m_brushBack;
	COLORREF m_clrBack;
	COLORREF m_clrText; 
};


