#pragma once

#include "FlowUniversalDefine.h"
#include "..\\F28LightControl_Eth\\Include\\F28LightControl_ETH.h"
#pragma comment(lib, "..\\exe\\F28LightControl_ETH.lib")
#pragma comment(lib, "..\\exe\\F28LightControl_ETHD.lib")
#include "WS2tcpip.h"

struct tag_Ini_Info
{
	CString strSection_Station[eStation_Count];
	CString strSection_Item[eItem_Count];
	CString strSection[eStation_Count][eItem_Count];
	CString strKey_Station[eStation_Count];
	CString strKey_Item[eItem_Count];
	CString strKey[eStation_Count][eItem_Count];
	tag_Ini_Info()
	{
		strSection_Station[eStation_Up_Left]		= "UP-LEFT-";
		strSection_Station[eStation_Up_Right]		= "UP-RIGHT-";
		strSection_Station[eStation_Down_Left]		= "DOWN-LEFT-";
		strSection_Station[eStation_Down_Right]		= "DOWN-RIGHT-";
		strSection_Item[eItem_1]					= "ITEM-1";
		strSection_Item[eItem_2]					= "ITEM-2";
		strSection_Item[eItem_3]					= "ITEM-3";
		strSection_Item[eItem_4]					= "ITEM-4";
		strKey_Station[eStation_Up_Left]			= "Up-Left-";
		strKey_Station[eStation_Up_Right]			= "Up-Right-";
		strKey_Station[eStation_Down_Left]			= "Down-Left-";
		strKey_Station[eStation_Down_Right]			= "Down-Right-";
		strKey_Item[eItem_1]						= "Item-1";
		strKey_Item[eItem_2]						= "Item-2";
		strKey_Item[eItem_3]						= "Item-3";
		strKey_Item[eItem_4]						= "Item-4";
		for(int i=0; i<eStation_Count; i++)
		{
			for(int j=0; j<eItem_Count; j++)
			{
				strSection[i][j]	= strSection_Station[i] + strSection_Item[j];
				strKey[i][j]		= strKey_Station[i] + strKey_Item[j];
			}
		}
	}
};
class CFlowLeakDetector
{
public:
	CFlowLeakDetector(void);
	~CFlowLeakDetector(void);

	bool						m_bInit;
	BOOL						m_bEnable[eStation_Count][eItem_Count];
	BOOL						m_nFuya[eStation_Count][eItem_Count];
	CString						m_strName_Item[eStation_Count][eItem_Count];
	CString						m_strIP[eStation_Count][eItem_Count];
	BYTE						m_ucAddr[eStation_Count][eItem_Count];
	short						m_sID[eStation_Count][eItem_Count];
	F28_PARAMETERS				m_tF28_Para[eStation_Count][eItem_Count];
	tag_Ini_Info				m_tIni;
	bool						m_bTest_Exception[eStation_Count][eItem_Count];
	bool						m_bTest_Finish[eStation_Count][eItem_Count];

	bool Para_Load_MES();
	bool Para_Load_Local();
	bool Para_Load();
	bool Para_Save_MES();
	bool Para_Save_Local();
	bool Para_Save();
	short GetModuleInfo(short sModuleID);
	short GetEthernetInformation(short sModuleID);
	bool AddModule(LPCTSTR szIPAddr, BYTE  ucGroup, BYTE ucModule, short* psModule);
	bool Open();
	bool Init();
	
public:
	int nTestType;
	int nFillTimeMax;
	int nFillTimeMin;
	int nStabilisationTimeMax;
	int nStabilisationTimeMin;
	int nTestTimeMax;
	int nTestTimeMin;
	int nDumpTimeMax;
	int nDumpTimeMin;
	int nFillVolueTime;
	int nTransferTime;
	int nMaxPressureP1Max;
	int nMaxPressureP1Min;
	int nUnitPressureP1;
	int nMinPressureP1Max;
	int nMinPressureP1Min;
	int nFillType;
	int nSetFillPressureMax;
	int nSetFillPressureMin;
	int nEndRatioMax;
	int nEndRatioMin;
	int nLeakUnit;
	double dbRejectTestMax;
	double dbRejectTestMin;
	double dbRejectRefMax;
	double dbRejectRefMin;
	double dbFilterTime;
	double dbLeakOffsetMax;
	double dbLeakOffsetMin;
	int nVolumeUnit;
	int nStanddardATMPressure;
	int nStanddardTemperature;
	int nVolumeCalcUnit;
	int nSafeDistanceMax;
	int nSafeDistanceMin;
	int nCodeLimitMax;
	int nCodeLimitMin;
	int nCodeLowMax;
	int nCodeLowMin;

	CString GetExePath();
	bool LoadRangePara(CString strModulePath);
	bool CompareRange();
	bool CompareRangeSingle(e_Station eStation);
};

