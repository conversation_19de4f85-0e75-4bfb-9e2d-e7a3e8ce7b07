﻿生成启动时间为 2025/7/19 15:13:34。
     1>项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\ControlDll\ControlDll.vcxproj”在节点 2 上(rebuild 个目标)。
       项目文件包含 ToolsVersion="12.0"。此工具集可能未知或缺失。在这种情况下，通过安装 MSBuild 的正确版本，或许可以解决此问题。否则，该 build 可能已因策略原因而被强制为某个特定 ToolsVersion。将该项目视为其已具有 ToolsVersion="4.0"。有关详细信息，请参阅 http://go.microsoft.com/fwlink/?LinkId=291333。
     1>_PrepareForClean:
         正在删除文件“.\..\..\temp\ControlDll\Debug\ControlDll.lastbuildstate”。
       InitializeBuildStatus:
         正在创建“.\..\..\temp\ControlDll\Debug\ControlDll.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _USRDLL /D _CRT_SECURE_NO_WARNINGS /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yc"stdafx.h" /Fp".\..\..\temp\ControlDll\Debug\ControlDll.pch" /Fo".\..\..\temp\ControlDll\Debug\\" /Fd".\..\..\temp\ControlDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
         _WIN32_WINNT not defined. Defaulting to _WIN32_WINNT_MAXVER (see WinSDKVer.h)
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _USRDLL /D _CRT_SECURE_NO_WARNINGS /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yu"stdafx.h" /Fp".\..\..\temp\ControlDll\Debug\ControlDll.pch" /Fo".\..\..\temp\ControlDll\Debug\\" /Fd".\..\..\temp\ControlDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ModbusRTUHelper.cpp _Control.cpp _ControlMultiThread.cpp _DMC2210.cpp _DMC2410.cpp _DMC5000.cpp _GtS.cpp ControlDll.cpp _GTS_IoExtend.cpp _IOC0640.cpp _LTDMC.cpp
         _LTDMC.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_ltdmc.cpp(114): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_ltdmc.cpp(126): warning C4244: “初始化”: 从“double”转换到“int”，可能丢失数据
         _IOC0640.cpp
         _GTS_IoExtend.cpp
         ControlDll.cpp
         _GtS.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_gts.cpp(277): warning C4101: “actl_pos”: 未引用的局部变量
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_gts.cpp(451): warning C4101: “Pos”: 未引用的局部变量
         _DMC5000.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc5000.cpp(79): warning C4244: “参数”: 从“DWORD”转换到“WORD”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc5000.cpp(411): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc5000.cpp(580): warning C4244: “参数”: 从“double”转换到“long”，可能丢失数据
         _DMC2410.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(394): warning C4244: “参数”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(394): warning C4244: “参数”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(425): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(429): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(440): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(441): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(457): warning C4244: “参数”: 从“DWORD”转换到“WORD”，可能丢失数据
         _DMC2210.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(381): warning C4244: “参数”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(381): warning C4244: “参数”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(412): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(416): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(427): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(428): warning C4244: “=”: 从“double”转换到“long”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2210.cpp(444): warning C4244: “参数”: 从“DWORD”转换到“WORD”，可能丢失数据
         _ControlMultiThread.cpp
         _Control.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_control.cpp(48): warning C4067: 预处理器指令后有意外标记 - 应输入换行符
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_control.cpp(488): warning C4101: “TempV”: 未引用的局部变量
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_control.cpp(668): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
         ModbusRTUHelper.cpp
         正在生成代码...
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(364): warning C4700: 使用了未初始化的局部变量“dTDec”
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_dmc2410.cpp(364): warning C4700: 使用了未初始化的局部变量“dTAcc”
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\controldll\_ioc0640.cpp(44): warning C4700: 使用了未初始化的局部变量“rtn”
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D _DEBUG /D _VC80_UPGRADE=0x0600 /D _AFXDLL /l"0x0804" /nologo /fo".\..\..\temp\ControlDll\Debug\ControlDll.res" ControlDll.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"../EXE/ControlDll.dll" /INCREMENTAL /NOLOGO /DEF:".\ControlDll.def" /MANIFEST /ManifestFile:".\..\..\temp\ControlDll\Debug\ControlDll.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:".\..\..\temp\ControlDll.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"../EXE/ControlDll.lib" /MACHINE:X86 /DLL .\..\..\temp\ControlDll\Debug\ControlDll.res
         .\..\..\temp\ControlDll\Debug\ModbusRTUHelper.obj
         .\..\..\temp\ControlDll\Debug\_Control.obj
         .\..\..\temp\ControlDll\Debug\_ControlMultiThread.obj
         .\..\..\temp\ControlDll\Debug\_DMC2210.obj
         .\..\..\temp\ControlDll\Debug\_DMC2410.obj
         .\..\..\temp\ControlDll\Debug\_DMC5000.obj
         .\..\..\temp\ControlDll\Debug\_GtS.obj
         .\..\..\temp\ControlDll\Debug\ControlDll.obj
         .\..\..\temp\ControlDll\Debug\StdAfx.obj
         .\..\..\temp\ControlDll\Debug\_GTS_IoExtend.obj
         .\..\..\temp\ControlDll\Debug\_IOC0640.obj
         .\..\..\temp\ControlDll\Debug\_LTDMC.obj
     1>.\ControlDll.def(4): warning LNK4017: DESCRIPTION 语句不支持目标平台；已忽略
            正在创建库 ../EXE/ControlDll.lib 和对象 ../EXE/ControlDll.exp
         ControlDll.vcxproj -> D:\ASUS\Desktop\科瑞2\hermetic\hermetic\ControlDll\.\..\exe\ControlDll.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"D:\ASUS\Desktop\科瑞2\hermetic\hermetic\ControlDll\.\..\exe\ControlDll.dll.manifest" /manifest .\..\..\temp\ControlDll\Debug\ControlDll.dll.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         正在删除文件“.\..\..\temp\ControlDll\Debug\ControlDll.unsuccessfulbuild”。
         正在对“.\..\..\temp\ControlDll\Debug\ControlDll.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\ControlDll\ControlDll.vcxproj”(rebuild 个目标)的操作。

生成成功。

已用时间 00:00:06.23
