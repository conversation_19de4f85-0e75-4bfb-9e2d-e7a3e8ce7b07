# 事务性混线切换实现说明

## 功能概述

事务性混线切换是为了解决多工位共用配置系统中数据一致性问题而设计的解决方案。该功能确保混线切换操作的原子性，避免在切换过程中影响其他工位的正常运行。

## 核心原理

### 1. 事务性原理
- **原子性**：混线切换要么完全成功，要么完全失败，不存在中间状态
- **一致性**：所有工位在切换前后都使用相同的配置参数
- **隔离性**：切换过程中系统处于完全空闲状态，无其他操作干扰
- **持久性**：切换完成后，新的配置参数立即生效

### 2. 系统空闲检查
```cpp
bool CheckAllStationsIdle()
{
    // 检查所有启用工位的状态
    for (int i = 0; i < eStation_Count; i++)
    {
        if (m_Flow_Pro.m_bTest[i])          // 工位正在测试
            return false;
        if (!m_Flow_Pro.m_bTest_Done[i])    // 工位测试未完成
            return false;
    }
    return true;
}
```

### 3. 等待机制
- **超时设置**：默认30秒超时，防止无限等待
- **轮询检查**：每100毫秒检查一次系统状态
- **消息处理**：避免界面卡死，保持用户体验

## 实现流程

### 1. 混线请求处理流程

```
开始混线请求
    ↓
检查混线功能是否启用
    ↓
检查是否已有混线在进行
    ↓
获取PCBA批次信息
    ↓
验证系列机型匹配
    ↓
检查是否需要切换机型
    ↓
显示人工确认对话框
    ↓
等待系统空闲
    ↓
执行配置切换
    ↓
完成混线切换
```

### 2. 系统空闲等待流程

```
开始等待系统空闲
    ↓
检查所有工位状态
    ↓
所有工位都空闲？
    ↓ 是
返回成功
    ↓ 否
等待100毫秒
    ↓
处理界面消息
    ↓
检查是否超时
    ↓ 未超时
回到"检查所有工位状态"
    ↓ 已超时
返回失败
```

## 关键函数说明

### 1. CheckAllStationsIdle()
**功能**：检查所有工位是否都处于空闲状态
**返回值**：
- `true` - 所有工位都空闲
- `false` - 至少有一个工位正在工作

**检查项目**：
- `m_bTest[i]` - 工位是否正在测试
- `m_bTest_Done[i]` - 工位测试是否完成

### 2. WaitForSystemIdle(int nTimeoutMs)
**功能**：等待系统空闲，带超时机制
**参数**：
- `nTimeoutMs` - 超时时间（毫秒）

**特点**：
- 非阻塞等待，处理界面消息
- 详细的日志记录
- 超时保护机制

### 3. ProcessTransactionalMixedLine()
**功能**：事务性混线切换主函数
**流程**：
1. 进入临界区保护
2. 检查前置条件
3. 获取批次信息
4. 人工确认
5. 等待系统空闲
6. 执行配置切换
7. 清理状态

## 数据一致性保证

### 1. 临界区保护
```cpp
CRITICAL_SECTION m_MixedLineCriticalSection;
```
- 保护混线操作的原子性
- 防止多个混线请求同时进行

### 2. 状态管理
```cpp
bool m_bMixedLineInProgress;        // 混线切换是否正在进行
DWORD m_dwMixedLineStartTime;       // 混线切换开始时间
CString m_strPendingPcba;           // 等待处理的PCBA号
e_Station m_ePendingStation;        // 等待处理的工位
tag_MixedLineInfo m_PendingModelInfo; // 等待处理的机型信息
```

### 3. 错误恢复
- 任何步骤失败都会重置状态
- 超时机制防止死锁
- 详细的错误日志记录

## 用户体验优化

### 1. 确认对话框增强
- 显示当前系统状态
- 实时更新等待信息
- 明确的操作提示

### 2. 日志记录
- 详细的操作日志
- 时间戳记录
- 错误信息追踪

### 3. 超时处理
- 合理的超时设置
- 用户友好的错误提示
- 自动状态恢复

## 性能优化

### 1. 轮询频率
- 100毫秒检查间隔
- 平衡响应速度和系统负载

### 2. 消息处理
- 避免界面卡死
- 保持用户交互响应

### 3. 内存管理
- 及时清理临时数据
- 避免内存泄漏

## 测试验证

### 1. 功能测试
- 正常混线切换流程
- 用户取消操作
- 超时处理
- 错误恢复

### 2. 并发测试
- 多工位同时触发混线
- 系统负载测试
- 长时间运行稳定性

### 3. 异常测试
- 网络中断
- MES服务异常
- 配置文件错误

## 维护说明

### 1. 日志位置
- 混线操作日志记录在系统日志中
- 关键操作都有时间戳

### 2. 配置参数
- 超时时间可调整（默认30秒）
- 检查间隔可调整（默认100毫秒）

### 3. 监控指标
- 混线切换成功率
- 平均等待时间
- 超时发生频率

## 总结

事务性混线切换通过等待系统空闲来确保数据一致性，虽然可能增加切换时间，但完全避免了数据竞争和状态不一致的问题。这是一个安全可靠的解决方案，特别适合多工位共用配置的生产环境。 