// WorksheetDlg.cpp : implementation file
//

#include "stdafx.h"
#include "KFGG.h"
#include "WorksheetDlg.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CWorksheetDlg dialog


CWorksheetDlg::CWorksheetDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CWorksheetDlg::IDD, pParent)
{
	//{{AFX_DATA_INIT(CWorksheetDlg)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT
}


void CWorksheetDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(CWorksheetDlg)
	DDX_Control(pDX, IDC_SELECT_LT, m_lbSel);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(CWorksheetDlg, CDialog)
	//{{AFX_MSG_MAP(CWorksheetDlg)
	ON_BN_CLICKED(IDC_OK_BN, OnOkBn)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CWorksheetDlg message handlers

void CWorksheetDlg::OnOkBn() 
{

	m_lbSel.GetText(m_lbSel.GetCurSel(),SheetName);
	CDialog::OnOK();
}

BOOL CWorksheetDlg::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	for(int n=0; n<m_sa.GetSize(); n++)
		m_lbSel.AddString(m_sa.GetAt(n));
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}
