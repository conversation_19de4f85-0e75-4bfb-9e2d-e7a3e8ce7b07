#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GB2312 to UTF-8 BOM Converter
自动扫描项目中的GB2312编码文件并转换为UTF-8 BOM格式

功能特性：
- 自动扫描整个项目目录
- 智能识别GB2312/GBK编码文件
- 安全转换为UTF-8 BOM格式
- 支持备份和回滚
- 详细的转换日志
- 转换前后验证

使用方法：
python convert_gb2312_to_utf8_bom.py [--backup] [--dry-run] [--verbose]

参数说明：
--backup    : 转换前创建备份文件
--dry-run   : 只扫描不转换，预览要转换的文件
--verbose   : 显示详细信息
"""

import os
import sys
import chardet
import shutil
import argparse
from datetime import datetime
from pathlib import Path

class GB2312Converter:
    def __init__(self, backup=False, dry_run=False, verbose=False):
        self.backup = backup
        self.dry_run = dry_run
        self.verbose = verbose
        self.log_file = f"conversion_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        self.backup_dir = "conversion_backup"

        # 排除的目录
        self.exclude_dirs = [
            'venv', '.git', 'build', 'Debug', 'Release',
            'install_test', 'install_final_test', 'install_test_debug',
            'install_test_release', 'ipch', '.vs', '__pycache__',
            'conversion_backup'
        ]

        # 排除的文件扩展名（二进制文件）
        self.binary_extensions = [
            '.exe', '.dll', '.lib', '.obj', '.pdb', '.ilk', '.res',
            '.suo', '.sdf', '.opensdf', '.user', '.filters',
            '.vcxproj', '.sln', '.png', '.jpg', '.jpeg', '.gif',
            '.bmp', '.ico', '.zip', '.rar', '.7z', '.tar', '.gz'
        ]

        self.stats = {
            'total_scanned': 0,
            'gb2312_found': 0,
            'converted': 0,
            'failed': 0,
            'skipped': 0
        }

    def log(self, message, level='INFO'):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] [{level}] {message}"

        if self.verbose or level in ['ERROR', 'WARNING']:
            print(log_message)

        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

    def detect_file_encoding(self, file_path):
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            if len(raw_data) == 0:
                return None, 'Empty file'

            # 检查BOM
            if raw_data.startswith(b'\xef\xbb\xbf'):
                return 'utf-8-sig', 'UTF-8 with BOM'
            elif raw_data.startswith(b'\xff\xfe'):
                return 'utf-16-le', 'UTF-16 LE with BOM'
            elif raw_data.startswith(b'\xfe\xff'):
                return 'utf-16-be', 'UTF-16 BE with BOM'

            # 使用chardet检测编码
            result = chardet.detect(raw_data)
            encoding = result.get('encoding', '').lower()
            confidence = result.get('confidence', 0)

            # 检查GB2312相关编码
            if encoding in ['gb2312', 'gbk', 'gb18030', 'cp936']:
                # 验证是否真的是GB2312编码
                try:
                    decoded_gb = raw_data.decode('gb2312')
                    try:
                        # 尝试用UTF-8解码，如果成功且内容相同，可能是UTF-8被误识别
                        decoded_utf8 = raw_data.decode('utf-8')
                        if decoded_gb == decoded_utf8:
                            return 'utf-8', f'UTF-8 (misidentified as {encoding})'
                        else:
                            return 'gb2312', f'GB2312 (confidence: {confidence:.2f})'
                    except:
                        return 'gb2312', f'GB2312 (confidence: {confidence:.2f})'
                except:
                    # 如果GB2312解码失败，尝试GBK
                    try:
                        decoded_gbk = raw_data.decode('gbk')
                        return 'gbk', f'GBK (confidence: {confidence:.2f})'
                    except:
                        pass

            return encoding, f'{encoding} (confidence: {confidence:.2f})'

        except Exception as e:
            return None, f'Error: {str(e)}'

    def should_skip_file(self, file_path):
        """判断是否应该跳过文件"""
        file_ext = os.path.splitext(file_path)[1].lower()

        # 跳过二进制文件
        if file_ext in self.binary_extensions:
            return True, "Binary file"

        # 跳过太大的文件（可能是二进制）
        try:
            file_size = os.path.getsize(file_path)
            if file_size > 10 * 1024 * 1024:  # 10MB
                return True, "File too large (>10MB)"
        except:
            return True, "Cannot access file"

        return False, ""

    def create_backup(self, file_path):
        """创建文件备份"""
        if not self.backup:
            return True

        try:
            # 创建备份目录
            if not os.path.exists(self.backup_dir):
                os.makedirs(self.backup_dir)

            # 保持目录结构
            rel_path = os.path.relpath(file_path, '.')
            backup_path = os.path.join(self.backup_dir, rel_path)
            backup_dir = os.path.dirname(backup_path)

            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            shutil.copy2(file_path, backup_path)
            self.log(f"Backup created: {backup_path}", 'DEBUG')
            return True

        except Exception as e:
            self.log(f"Failed to create backup for {file_path}: {str(e)}", 'ERROR')
            return False

    def convert_file(self, file_path):
        """转换单个文件"""
        try:
            # 创建备份
            if not self.create_backup(file_path):
                return False, "Backup failed"

            # 读取原文件内容
            with open(file_path, 'rb') as f:
                raw_data = f.read()

            # 用GB2312解码
            try:
                content = raw_data.decode('gb2312')
            except UnicodeDecodeError:
                try:
                    content = raw_data.decode('gbk')
                except UnicodeDecodeError:
                    return False, "Cannot decode with GB2312/GBK"

            # 规范化换行符为Windows格式（CRLF）
            # 先统一为LF，再转换为CRLF
            content = content.replace('\r\n', '\n').replace('\r', '\n').replace('\n', '\r\n')

            # 写入UTF-8 BOM格式
            with open(file_path, 'w', encoding='utf-8-sig', newline='') as f:
                f.write(content)

            # 验证转换结果
            with open(file_path, 'rb') as f:
                new_data = f.read()

            if not new_data.startswith(b'\xef\xbb\xbf'):
                return False, "BOM not added correctly"

            # 验证内容完整性
            try:
                new_content = new_data[3:].decode('utf-8')  # 跳过BOM
                # GB2312到UTF-8转换时，中文字符字节数会变化，所以不比较长度
                # 只验证关键特征：中文字符的存在和基本结构
                original_has_chinese = any('\u4e00' <= char <= '\u9fff' for char in content)
                new_has_chinese = any('\u4e00' <= char <= '\u9fff' for char in new_content)
                if original_has_chinese != new_has_chinese:
                    return False, "Chinese character presence mismatch"

                # 检查文件是否为空或过短
                if len(new_content) < 10 and len(content) > 100:
                    return False, "Converted content too short"

                # 简单的完整性检查：检查一些常见的C++关键字
                if '.cpp' in file_path or '.h' in file_path:
                    # 如果原文件包含这些关键字，转换后也应该包含
                    cpp_keywords = ['#include', 'class', 'void', 'int', 'return']
                    for keyword in cpp_keywords:
                        if keyword in content and keyword not in new_content:
                            return False, f"Missing keyword '{keyword}' after conversion"

            except Exception as e:
                return False, f"Cannot verify converted content: {str(e)}"

            return True, "Conversion successful"

        except Exception as e:
            return False, f"Conversion error: {str(e)}"

    def scan_files(self):
        """扫描所有文件"""
        gb2312_files = []

        self.log("Starting file scan...")

        for root, dirs, files in os.walk('.'):
            # 过滤掉不需要的目录
            dirs[:] = [d for d in dirs if not any(exclude in d for exclude in self.exclude_dirs)]

            for file in files:
                file_path = os.path.join(root, file)
                self.stats['total_scanned'] += 1

                if self.stats['total_scanned'] % 100 == 0:
                    self.log(f"Scanned {self.stats['total_scanned']} files...")

                # 检查是否应该跳过
                should_skip, skip_reason = self.should_skip_file(file_path)
                if should_skip:
                    if self.verbose:
                        self.log(f"Skipped {file_path}: {skip_reason}", 'DEBUG')
                    continue

                # 检测编码
                encoding, description = self.detect_file_encoding(file_path)
                if encoding in ['gb2312', 'gbk']:
                    gb2312_files.append((file_path, description))
                    self.stats['gb2312_found'] += 1
                    self.log(f"GB2312/GBK found: {file_path} - {description}")

        self.log(f"Scan complete. Total files: {self.stats['total_scanned']}, GB2312/GBK files: {self.stats['gb2312_found']}")
        return gb2312_files

    def convert_files(self, gb2312_files):
        """批量转换文件"""
        if not gb2312_files:
            self.log("No GB2312/GBK files found to convert.")
            return

        if self.dry_run:
            self.log("DRY RUN MODE - No files will be converted")
            self.log("Files that would be converted:")
            for i, (file_path, description) in enumerate(gb2312_files, 1):
                self.log(f"{i:3d}. {file_path} - {description}")
            return

        self.log(f"Starting conversion of {len(gb2312_files)} files...")

        for i, (file_path, description) in enumerate(gb2312_files, 1):
            self.log(f"Converting {i}/{len(gb2312_files)}: {file_path}")

            success, message = self.convert_file(file_path)

            if success:
                self.stats['converted'] += 1
                self.log(f"✓ {file_path}: {message}")
            else:
                self.stats['failed'] += 1
                self.log(f"✗ {file_path}: {message}", 'ERROR')

    def print_summary(self):
        """打印转换摘要"""
        print("\n" + "="*60)
        print("CONVERSION SUMMARY")
        print("="*60)
        print(f"Total files scanned: {self.stats['total_scanned']}")
        print(f"GB2312/GBK files found: {self.stats['gb2312_found']}")
        print(f"Files converted: {self.stats['converted']}")
        print(f"Files failed: {self.stats['failed']}")
        print(f"Files skipped: {self.stats['skipped']}")
        print(f"Log file: {self.log_file}")

        if self.backup and self.stats['converted'] > 0:
            print(f"Backup directory: {self.backup_dir}")

        print("="*60)

    def run(self):
        """运行转换程序"""
        self.log("GB2312 to UTF-8 BOM Converter started")
        self.log(f"Options: backup={self.backup}, dry_run={self.dry_run}, verbose={self.verbose}")

        # 扫描文件
        gb2312_files = self.scan_files()

        # 转换文件
        self.convert_files(gb2312_files)

        # 打印摘要
        self.print_summary()

        self.log("Conversion completed")

def main():
    parser = argparse.ArgumentParser(
        description='Convert GB2312 encoded files to UTF-8 BOM format',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python convert_gb2312_to_utf8_bom.py                    # 扫描并转换
  python convert_gb2312_to_utf8_bom.py --dry-run          # 只扫描，不转换
  python convert_gb2312_to_utf8_bom.py --backup           # 转换前创建备份
  python convert_gb2312_to_utf8_bom.py --backup --verbose # 备份并显示详细信息
        """
    )

    parser.add_argument('--backup', action='store_true',
                       help='Create backup files before conversion')
    parser.add_argument('--dry-run', action='store_true',
                       help='Scan only, do not convert files')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Show verbose output')

    args = parser.parse_args()

    # 创建转换器并运行
    converter = GB2312Converter(
        backup=args.backup,
        dry_run=args.dry_run,
        verbose=args.verbose
    )

    try:
        converter.run()
    except KeyboardInterrupt:
        print("\nConversion interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
