#pragma once 
// 
//#if _USRDLL
//#define _SERIAL_PORT_DLL __declspec(dllexport) 
//#else
//#define _SERIAL_PORT_DLL __declspec(dllimport) 
//#endif
 
class /*_SERIAL_PORT_DLL*/ SerialCom
{
public:
	SerialCom(void);
	~SerialCom(void); 

	HANDLE				m_hCom;
	COMMTIMEOUTS		m_CommTimeouts;
	UINT				m_nPortNr;
	BOOL				m_bIsConnected;

	BOOL OpenCom();
	BOOL OpenCom(int ComNo);
	BOOL OpenCom(int ComNo,DWORD Baud,int Data,int Stop,char Parity);
	BOOL OpenCom(int ComNo,DWORD Baud,int Data,int Stop,char Parity,DWORD cbInBuf,DWORD cbOutBuf);
	void SetParam(int ComNo,DWORD Baud,int Data,int Stop,char Parity);
	void SetParam(int ComNo,DWORD Baud,int Data,int Stop,char Parity,DWORD cbInBuf,DWORD cbOutBuf);
	int GetDataLen();
    BOOL GetConnectState();
	DWORD ReadCom(/*BYTE*/char *pBuff,int nCount);
	CString ReadCom();
	BOOL WriteCom(/*BYTE*/char *pBuff, int nCount);
	BOOL ClearCom();
	BOOL CloseCom();
	void ProcessErrorMessage(char* ErrorText);

private:
	unsigned int		m_PortNo;
	unsigned int		m_Baud;
	char				m_Parity;
	unsigned int		m_StopBits;
	unsigned int		m_DataBits;
	unsigned int		m_cbInBuf;
	unsigned int		m_cbOutBuf;
	CRITICAL_SECTION	cs;
};

