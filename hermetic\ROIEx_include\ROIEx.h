// ROIEx.h : main header file for the ROIEX DLL
//

#if !defined(AFX_ROIEX_H__6E74C476_B18D_42C6_9E33_06159BF9DA49__INCLUDED_)
#define AFX_ROIEX_H__6E74C476_B18D_42C6_9E33_06159BF9DA49__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// CROIExApp
// See ROIEx.cpp for the implementation of this class
//
enum enROI_MOUSE
{
	ROI_MOUSE_ON_RECT_L   =  0,
	ROI_MOUSE_ON_RECT_T,
	ROI_MOUSE_ON_RECT_R,
	ROI_MOUSE_ON_RECT_B,
	ROI_MOUSE_ON_RECT_LT,
	ROI_MOUSE_ON_RECT_RT,
	ROI_MOUSE_ON_RECT_LB,
	ROI_MOUSE_ON_RECT_RB,
	ROI_MOUSE_ON_RECT_C,
};
class CROIExApp : public CWinApp
{
public:
	CROIExApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CROIExApp)
	//}}AFX_VIRTUAL

	//{{AFX_MSG(CROIExApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

#if _USRDLL
#define _WROI_DLL __declspec(dllexport) 
#else
#define _WROI_DLL __declspec(dllimport) 
#endif

//basic class

class _WROI_DLL CROIEx  
{
public:
	CROIEx();
	virtual ~CROIEx();
	BOOL m_bRoi;
	enROI_MOUSE GetMouseStatus();
protected:
	int m_nSize;
	HDC hDC;
	HPEN Pen;
	HPEN pOldPen;
	HBRUSH Brush;
	HBRUSH pOldBrush;
	int nOldDrawMode;
	//	HWND m_handle;
	
	HCURSOR m_Hsizeall,m_Hsizens,m_Hsizenwse,m_Hsizewe,m_Hsizenesw,m_Harrow;
	//	long m_TransparentColor;
	
	int nMouseFlg;
	enROI_MOUSE m_enRoiMouse;
	int nDaltX;
	int nDaltY;
	
public:
	void CreateDrawMode(HWND handle);
	void ReleaseDrawMode(HWND handle);
};

//----------------------------------------------------------------------------
//rectangle class
class _WROI_DLL CRectROI: public CROIEx
{
public:
	CRectROI();
	virtual ~CRectROI();
	
protected:
	//    CRect m_rect;
	CRect* m_pRect;
private:
	
public:
	void GetRect(CRect* pRect);
	void SetRect(CRect* rect);
	void virtual EraseRect(CPoint ptScroll);
	void virtual DrawRect(CPoint ptScroll);
	void MouseMove(UINT nFlags, CPoint point, CPoint ptScroll);
	void MouseDown(UINT nFlags, CPoint point, CPoint ptScroll);	
};
class _WROI_DLL CRCrossROI:public CRectROI
{
public:
	CRCrossROI();
	
private:
	
public:
	void EraseRect(CPoint ptScroll);
	void DrawRect(CPoint ptScroll);
	
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_ROIEX_H__6E74C476_B18D_42C6_9E33_06159BF9DA49__INCLUDED_)
