#pragma once

#include "Language_Universal.h"

enum e_Ctrl_Para
{
	eCtrl_Btn_Open,
	eCtrl_Btn_Connect,
	eCtrl_Btn_Para_Read,
	eCtrl_Btn_Para_Write,
	eCtrl_Btn_Close,
	eCtrl_Btn_Save,
	eCtrl_Btn_Cancel,
	eCtrl_Para_Count,
};
enum e_Col_Para
{
	eCol_Para_Station,
	eCol_Para_Item,
	eCol_Para_IP,
	eCol_Para_Enable,
	eCol_Para_Fuya,
	eCol_Para_Count,
};
class CLanguage_Dlg_Para
{
public:
	CLanguage_Dlg_Para(void);
	~CLanguage_Dlg_Para(void);

	HWND			m_hWnd;
	int				m_nIDC[eCtrl_Para_Count];
	CString			m_strCtrl[eLang_Count][eCtrl_Para_Count];
	CString			m_strCol[eLang_Count][eCol_Para_Count];
	CString			m_strFuyaOption[eLang_Count][8];

	bool Set_Hwnd(HWND hWnd);
	bool Set_Control(e_Lang_Type eLang_Type);
	void Set_Col(e_Lang_Type eLang_Type);
};

