// SdkExt.h: interface for the CSdkExt class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_SDKEXT_H__87990F66_2FDA_4AE8_8375_662A4B698F53__INCLUDED_)
#define AFX_SDKEXT_H__87990F66_2FDA_4AE8_8375_662A4B698F53__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

//#define SDK_WITH_VISION
//#define SDK_WITH_HDREGESTER



#ifdef SDK_WITH_HDREGESTER
	#include "..\Include\HDRegester.h"
	#include "..\Include\CreateRegesterNo.h"
#endif

#ifdef SDK_WITH_VISION
	#include "..\\VisionDll\\_MILApp.h"
	#include "..\\VisionDll\\_Video.h"
#endif
#include "..\Include\UserManager.h"

#include "..\\InfomationDll_Include\\_Information.h"
#include "..\\InfomationDll_Include\\_InfoReadWrite.h"
#include "..\\ControlDll_Include\\_ControlMultiThread.h"

class AFX_EXT_CLASS CSdkExt  
{
public:
	CSdkExt();
	virtual ~CSdkExt();

	void Destroy();
	void initSDK(HWND hWndParent, C_Information *pInfo, C_InfoReadWrite *pInfoRW, C_ControlMultiThread *pCtrl,
#ifdef SDK_WITH_VISION
		C_Video *pVdo, C_MILApp *pMil, 
#endif
		CUserManager *pUser);
	void DisplayDebug(BOOL bShow);
	void DisplayDebugDlg();
};

#endif // !defined(AFX_SDKEXT_H__87990F66_2FDA_4AE8_8375_662A4B698F53__INCLUDED_)
