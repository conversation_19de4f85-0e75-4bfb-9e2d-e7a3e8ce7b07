#ifndef _OPPO_OMTP_CLIENT_DLL_H_H__
#define _OPPO_OMTP_CLIENT_DLL_H_H__

#ifdef __cplusplus
extern "C" {
#endif

#ifdef OPPO_DLL_EXPORT
#define OPPO_STDCALL __declspec(dllexport) int __stdcall
#else
#define OPPO_STDCALL __declspec(dllimport) int __stdcall
#endif


/************************************************************************************************
This API is used to communicate With OMTP Server

pszRequestJson:Input Parameter, use json format. Refer to "OPPO OMTP Client API.xlsx"
pszResponseJson:Output Parameter, user json format. Refer to "OPPO OMTP Client API.xlsx"
************************************************************************************************/
OPPO_STDCALL OPPO_OMTP_CLIENT_SendRequest(char* pszRequestJson, char* pszResponseJson);

/************************************************************************************************
This Following OPPO_OMTP_JSON_* API is used to format parameter to json format, and then convert
json to string which used in API OPPO_OMTP_CLIENT_SendRequest.

For example, if you need to call api "AdbGetDeviceIdList". You need to call the function like:
char szRequest[4096] = {0x00}, szResponse[4096] = {0x00};

memset(szRequest, 0x00, sizeof(szRequest));
memset(szResponse, 0x00, sizeof(szResponse));

OPPO_OMTP_JSON_Init(NULL);
OPPO_OMTP_JSON_AppendJsonItem("api", "AdbGetDeviceIdList");
OPPO_OMTP_JSON_ConvertToStr(szRequest);

OPPO_OMTP_CLIENT_SendRequest(szRequest, szResponse);
************************************************************************************************/
OPPO_STDCALL OPPO_OMTP_JSON_Init(char* pszNull);
OPPO_STDCALL OPPO_OMTP_JSON_AppendJsonItem(char* pszKeyName, char* pszValue);
OPPO_STDCALL OPPO_OMTP_JSON_AppendJsonArrayItem(char* pszArrayName, char** ppszElementKeyList,  
	char*** pppszElementValueList, int nElementCnt, int nArrayCnt);
OPPO_STDCALL OPPO_OMTP_JSON_ConvertToStr(char* pszJsonStr);


OPPO_STDCALL OPPO_OMTP_JSON_GetJsonItem(char* pszJson, char* pszKeyName, char* pszValue);
OPPO_STDCALL OPPO_OMTP_JSON_GetJsonItemArray(char* pszJson, char* pszKeyName, char** ppszValueList, int* pnValueListItemCnt);

#ifdef __cplusplus
}
#endif

#endif