# Microsoft Developer Studio Project File - Name="SdkExtDll" - Package Owner=<4>
# Microsoft Developer Studio Generated Build File, Format Version 6.00
# ** DO NOT EDIT **

# TARGTYPE "Win32 (x86) Dynamic-Link Library" 0x0102

CFG=SdkExtDll - Win32 Debug
!MESSAGE This is not a valid makefile. To build this project using NMAKE,
!MESSAG<PERSON> use the Export Makefile command and run
!MESSAGE 
!MESSAGE NMAKE /f "SdkExtDll.mak".
!MESSAGE 
!MESSAGE You can specify a configuration when running NMAKE
!MESSAGE by defining the macro CFG on the command line. For example:
!MESSAGE 
!MESSAGE NMAKE /f "SdkExtDll.mak" CFG="SdkExtDll - Win32 Debug"
!MESSAGE 
!MESSAGE Possible choices for configuration are:
!MESSAGE 
!MESSAGE "SdkExtDll - Win32 Release" (based on "Win32 (x86) Dynamic-Link Library")
!MESSAGE "SdkExtDll - Win32 Debug" (based on "Win32 (x86) Dynamic-Link Library")
!MESSAGE 

# Begin Project
# PROP AllowPerConfigDependencies 0
# PROP Scc_ProjName ""
# PROP Scc_LocalPath ""
CPP=cl.exe
MTL=midl.exe
RSC=rc.exe

!IF  "$(CFG)" == "SdkExtDll - Win32 Release"

# PROP BASE Use_MFC 6
# PROP BASE Use_Debug_Libraries 0
# PROP BASE Output_Dir "Release"
# PROP BASE Intermediate_Dir "Release"
# PROP BASE Target_Dir ""
# PROP Use_MFC 6
# PROP Use_Debug_Libraries 0
# PROP Output_Dir "..\..\temp\sdkExtDll\Release"
# PROP Intermediate_Dir "..\..\temp\sdkExtDll\Release"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
LIB32=link.exe -lib
# ADD BASE CPP /nologo /MD /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_WINDLL" /D "_AFXDLL" /Yu"stdafx.h" /FD /c
# ADD CPP /nologo /MD /W3 /GX /O2 /D "WIN32" /D "NDEBUG" /D "_WINDOWS" /D "_WINDLL" /D "_AFXDLL" /D "_MBCS" /D "_AFXEXT" /Yu"stdafx.h" /FD /c
# ADD BASE MTL /nologo /D "NDEBUG" /mktyplib203 /win32
# ADD MTL /nologo /D "NDEBUG" /mktyplib203 /win32
# ADD BASE RSC /l 0x804 /d "NDEBUG" /d "_AFXDLL"
# ADD RSC /l 0x804 /d "NDEBUG" /d "_AFXDLL"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 /nologo /subsystem:windows /dll /machine:I386
# ADD LINK32 /nologo /subsystem:windows /dll /machine:I386 /out:"../EXE/SdkExtDll.dll" /implib:"../EXE/SdkExtDll.lib"
# SUBTRACT LINK32 /pdb:none

!ELSEIF  "$(CFG)" == "SdkExtDll - Win32 Debug"

# PROP BASE Use_MFC 6
# PROP BASE Use_Debug_Libraries 1
# PROP BASE Output_Dir "Debug"
# PROP BASE Intermediate_Dir "Debug"
# PROP BASE Target_Dir ""
# PROP Use_MFC 6
# PROP Use_Debug_Libraries 1
# PROP Output_Dir "..\..\temp\sdkExtDll\Debug"
# PROP Intermediate_Dir "..\..\temp\sdkExtDll\Debug"
# PROP Ignore_Export_Lib 0
# PROP Target_Dir ""
LIB32=link.exe -lib
# ADD BASE CPP /nologo /MDd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_WINDLL" /D "_AFXDLL" /Yu"stdafx.h" /FD /GZ /c
# ADD CPP /nologo /MDd /W3 /Gm /GX /ZI /Od /D "WIN32" /D "_DEBUG" /D "_WINDOWS" /D "_WINDLL" /D "_AFXDLL" /D "_MBCS" /D "_AFXEXT" /Yu"stdafx.h" /FD /GZ /c
# ADD BASE MTL /nologo /D "_DEBUG" /mktyplib203 /win32
# ADD MTL /nologo /D "_DEBUG" /mktyplib203 /win32
# ADD BASE RSC /l 0x804 /d "_DEBUG" /d "_AFXDLL"
# ADD RSC /l 0x804 /d "_DEBUG" /d "_AFXDLL"
BSC32=bscmake.exe
# ADD BASE BSC32 /nologo
# ADD BSC32 /nologo
LINK32=link.exe
# ADD BASE LINK32 /nologo /subsystem:windows /dll /debug /machine:I386 /pdbtype:sept
# ADD LINK32 /nologo /subsystem:windows /dll /debug /machine:I386 /out:"../EXE/SdkExtDll.dll" /implib:"../EXE/SdkExtDll.lib" /pdbtype:sept
# SUBTRACT LINK32 /pdb:none

!ENDIF 

# Begin Target

# Name "SdkExtDll - Win32 Release"
# Name "SdkExtDll - Win32 Debug"
# Begin Group "Source Files"

# PROP Default_Filter "cpp;c;cxx;rc;def;r;odl;idl;hpj;bat"
# Begin Source File

SOURCE=.\_a_rect30.cpp
# End Source File
# Begin Source File

SOURCE=.\_Dlg2ImageArith.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgBlobSetting.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgDebug.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgImage.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgInputBox.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgSetting.cpp
# End Source File
# Begin Source File

SOURCE=.\_DlgThresholdSet.cpp
# End Source File
# Begin Source File

SOURCE=.\_ImageView.cpp
# End Source File
# Begin Source File

SOURCE=..\CommonClass\General\AppGeneral.cpp
# End Source File
# Begin Source File

SOURCE=.\SdkExt.cpp
# End Source File
# Begin Source File

SOURCE=.\SdkExtDll.cpp
# End Source File
# Begin Source File

SOURCE=.\SdkExtDll.def
# End Source File
# Begin Source File

SOURCE=.\SdkExtDll.rc
# End Source File
# Begin Source File

SOURCE=.\StdAfx.cpp
# ADD CPP /Yc"stdafx.h"
# End Source File
# End Group
# Begin Group "Header Files"

# PROP Default_Filter "h;hpp;hxx;hm;inl"
# Begin Source File

SOURCE=.\_a_rect30.h
# End Source File
# Begin Source File

SOURCE=.\_Dlg2ImageArith.h
# End Source File
# Begin Source File

SOURCE=.\_DlgBlobSetting.h
# End Source File
# Begin Source File

SOURCE=.\_DlgDebug.h
# End Source File
# Begin Source File

SOURCE=.\_DlgImage.h
# End Source File
# Begin Source File

SOURCE=.\_DlgInputBox.h
# End Source File
# Begin Source File

SOURCE=.\_DlgSetting.h
# End Source File
# Begin Source File

SOURCE=.\_DlgThresholdSet.h
# End Source File
# Begin Source File

SOURCE=.\_ImageView.h
# End Source File
# Begin Source File

SOURCE=.\Resource.h
# End Source File
# Begin Source File

SOURCE=..\SdkExtDll_include\SdkExt.h
# End Source File
# Begin Source File

SOURCE=.\StdAfx.h
# End Source File
# End Group
# Begin Group "Resource Files"

# PROP Default_Filter "ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe"
# Begin Source File

SOURCE=".\res\folder open.ico"
# End Source File
# Begin Source File

SOURCE=.\res\folder.ico
# End Source File
# Begin Source File

SOURCE=.\res\Hand.cur
# End Source File
# Begin Source File

SOURCE=.\res\icon_mcl.ico
# End Source File
# Begin Source File

SOURCE=.\res\icon_mop.ico
# End Source File
# Begin Source File

SOURCE=.\res\LedOff.ico
# End Source File
# Begin Source File

SOURCE=.\res\LedOn.ico
# End Source File
# Begin Source File

SOURCE=.\res\SdkExtDll.rc2
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarEdit.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarEditDisabled.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarEditHot.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarSys.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarSysDisabled.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarSysHot.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarUser.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarUserDisabled.bmp
# End Source File
# Begin Source File

SOURCE=.\res\ToolbarUserHot.bmp
# End Source File
# End Group
# Begin Group "Lib"

# PROP Default_Filter ""
# End Group
# Begin Source File

SOURCE=.\ReadMe.txt
# End Source File
# End Target
# End Project
