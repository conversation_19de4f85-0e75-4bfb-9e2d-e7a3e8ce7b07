﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Drawing.Drawing2D;
using csLTDMC;
using System.Threading;

namespace 连续插补运动_脉冲当量_
{
    public partial class Form1 : Form
    {
        private ushort _CardID = 0;
        public Form1()
        {
            InitializeComponent();
        }

        private void Form1_Load(object sender, EventArgs e)
        {
            short num = LTDMC.dmc_board_init();//获取卡数量
            if (num <= 0 || num > 8)
            {
                MessageBox.Show("初始卡失败!", "出错");
            }
            ushort _num = 0;
            ushort[] cardids = new ushort[8];
            uint[] cardtypes = new uint[8];
            short res = LTDMC.dmc_get_CardInfList(ref _num, cardtypes, cardids);
            if (res != 0)
            {
                MessageBox.Show("获取卡信息失败!");
            }
            _CardID = cardids[0];
            //
            ushort axisx = GetAxisX();
            ushort axisy = GetAxisY();
            ushort axisz = GetAxisZ();
            ushort crd = GetCrd();
            //设置脉冲当量
            LTDMC.dmc_set_equiv(_CardID, axisx, decimal.ToDouble(numericUpDown3.Value));
            LTDMC.dmc_set_equiv(_CardID, axisy, decimal.ToDouble(numericUpDown5.Value));
            LTDMC.dmc_set_equiv(_CardID, axisz, decimal.ToDouble(numericUpDown7.Value));
            //位置清零
            LTDMC.dmc_set_position_unit(_CardID, axisx, 0);
            LTDMC.dmc_set_position_unit(_CardID, axisy, 0);
            LTDMC.dmc_set_position_unit(_CardID, axisz, 0);
            //
            timer1.Start();
            DrawPosImage();
            DrawVTImage();
        }
        private ushort GetAxisX()
        {
            return decimal.ToUInt16(numericUpDown2.Value);
        }
        private ushort GetAxisY()
        {
            return decimal.ToUInt16(numericUpDown4.Value);
        }
        private ushort GetAxisZ()
        {
            return decimal.ToUInt16(numericUpDown6.Value);
        }
        private ushort GetCrd()
        {
            return decimal.ToUInt16(numericUpDown1.Value);
        }

        private List<double> _PointsX = new List<double>();
        private List<double> _PointsY = new List<double>();
        private List<double> _PointsZ = new List<double>();
        private void DrawPosImage()
        {
            int width = pictureBox1.Width;
            int height = pictureBox1.Height;
            //
            Bitmap bm = new Bitmap(width, height);
            //
            Graphics g = Graphics.FromImage(bm);
            Matrix matrix = new Matrix(1, 0, 0, -1, 0, height);
            g.Transform = matrix;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            //网格绘制
            Pen pen = new Pen(Color.Green);
            g.DrawLine(pen, new Point(width / 3, height / 2), new Point(0, height / 2 - width / 3));
            g.DrawLine(pen, new Point(width / 3, height / 2), new Point(width, height / 2));
            g.DrawLine(pen, new Point(width / 3, height / 2), new Point(width / 3, height));
            //
            SolidBrush brush = new SolidBrush(Color.Black);
            g.ResetTransform();
            g.DrawString("0", this.Font, brush, width / 3 - 15, height / 2 - 15);
            g.Transform = matrix;
            //
            DrawLine(g, 1200, 800, 0, 1200, 2800, 0, 0);
            DrawLine(g, 1200, 800, 0, 1200, 800, -800, 0);
            DrawLine(g, 1200, 800, 0, -800, 800, 1000, 0);
            DrawLine(g, 1200, 800, -800, 1200, 2800, -800, 0);
            DrawLine(g, -800, 2800, -800, 1200, 2800, -800, 0);
            DrawLine(g, 1200, 2800, 0, -800, 2800, 1000, 0);
            DrawLine(g, 1200, 2800, 0, 1200, 2800, -800, 0);
            DrawLine(g, -800, 800, 1000, -800, 800, -800, 0);
            DrawLine(g, -800, 800, -800, 1200, 800, -800, 0);
            DrawLine(g, -800, 2800, -800, -800, 2800, 1000, 0);
            DrawLine(g, -1390, 2748, 1300, -1390, 2748, -800, 0);
            DrawElli(g, -2000, 0, 1000);
            //
            DrawMarkPoint(g, 1200, 800, 0, "A");
            DrawMarkPoint(g, -800, 800, 1000, "B");
            DrawMarkPoint(g, -1800, 1800, 2000, "C");
            DrawMarkPoint(g, -800, 2800, 1000, "D");
            DrawMarkPoint(g, 1200, 2800, 0, "E");

            //轨迹绘制
            pen = new Pen(Color.Black);
            for (int i = 0; i < _PointsX.Count - 1; i++)
            {
                PointF p1 = ToPointF(_PointsX[i], _PointsY[i], _PointsZ[i]);
                PointF p2 = ToPointF(_PointsX[i + 1], _PointsY[i + 1], _PointsZ[i + 1]);
                g.DrawLine(pen, p1, p2);
            }

            g.Dispose();
            //
            pictureBox1.Image = bm;
        }
        private PointF ToPointF(double x, double y, double z)
        {
            int width = pictureBox1.Width;
            int height = pictureBox1.Height;
            //
            double _x = y - x * Math.Cos(Math.PI / 4);
            double _y = z - x * Math.Cos(Math.PI / 4);
            //
            double n = 1000 / 60;
            _x = _x / n + width / 3;
            _y = _y / n + height / 2;
            //
            return new PointF((float)_x, (float)_y);
        }
        private void DrawMarkPoint(Graphics g, double x, double y, double z, string txt)
        {
            PointF pt = ToPointF(x, y, z);
            g.FillEllipse(new SolidBrush(Color.Blue), new RectangleF(pt.X - 2f, pt.Y - 2f, 4f, 4f));
            //
            Matrix matrix = g.Transform;
            g.ResetTransform();
            int height = pictureBox1.Height;
            float _x = pt.X;
            float _y = height - pt.Y - 15;

            g.DrawString(txt, this.Font, new SolidBrush(Color.Blue), new PointF(_x, _y));
            g.Transform = matrix;
        }
        private void DrawLine(Graphics g, double sx, double sy, double sz, double ex, double ey, double ez, int style)
        {
            Pen pen = new Pen(Color.Red);
            if (style == 0)
            {
                pen.DashStyle = DashStyle.Solid;
            }
            else
            {
                pen.DashPattern = new float[] { 3f, 5f };
            }
            PointF p1 = ToPointF(sx, sy, sz);
            PointF p2 = ToPointF(ex, ey, ez);
            g.DrawLine(pen, p1, p2);
        }
        private void DrawElli(Graphics g, double x, double y, double z)
        {
            double[] _x = new double[22];
            double[] _y = new double[22];
            double[] _z = new double[22];
            //
            _x[0] = -800; _y[0] = 800; _z[0] = 1000;
            _x[1] = 1199.88 + x; _y[1] = 799.94 + y; _z[1] = 0.14 + z;
            _x[2] = 1110.58 + x; _y[2] = 763.94 + y; _z[2] = 89.43 + z;
            _x[3] = 961.23 + x; _y[3] = 739.46 + y; _z[3] = 238.78 + z;
            _x[4] = 803.45 + x; _y[4] = 759.78 + y; _z[4] = 396.56 + z;
            _x[5] = 662.3 + x; _y[5] = 820.48 + y; _z[5] = 537.71 + z;
            _x[6] = 525.55 + x; _y[6] = 925.54 + y; _z[6] = 674.75 + z;
            _x[7] = 407.8 + x; _y[7] = 1067.16 + y; _z[7] = 792.2 + z;
            _x[8] = 312.53 + x; _y[8] = 1241.18 + y; _z[8] = 887.66 + z;
            _x[9] = 246.04 + x; _y[9] = 1434.1 + y; _z[9] = 953.96 + z;
            _x[10] = 208 + x; _y[10] = 1645.95 + y; _z[10] = 992.05 + z;
            _x[11] = 201.45 + x; _y[11] = 1865.85 + y; _z[11] = 998.55 + z;
            _x[12] = 226.42 + x; _y[12] = 2079.53 + y; _z[12] = 973.48 + z;
            _x[13] = 285.32 + x; _y[13] = 2291.77 + y; _z[13] = 914.51 + z;
            _x[14] = 367.08 + x; _y[14] = 2467.39 + y; _z[14] = 832.69 + z;
            _x[15] = 475.11 + x; _y[15] = 2620.94 + y; _z[15] = 724.61 + z;
            _x[16] = 610.16 + x; _y[16] = 2745.52 + y; _z[16] = 589.83 + z;
            _x[17] = 754.1 + x; _y[17] = 2823.96 + y; _z[17] = 445.55 + z;
            _x[18] = 906.66 + x; _y[18] = 2858.88 + y; _z[18] = 292.97 + z;
            _x[19] = 1056.88 + x; _y[19] = 2849.84 + y; _z[19] = 142.77 + z;
            _x[20] = 1188.12 + x; _y[20] = 2805.79 + y; _z[20] = 11.88 + z;
            _x[21] = -800; _y[21] = 2800; _z[21] = 1000;
            //
            for (int i = 0; i < _x.Length - 1; i++)//绘制顶部圆弧
            {
                DrawLine(g, _x[i], _y[i], _z[i], _x[i + 1], _y[i + 1], _z[i + 1], 0);
            }
            for (int i = 0; i < _x.Length; i++)
            {
                _z[i] = -800;
            }
            for (int i = 0; i < 16; i++)//绘制底部虚线
            {
                DrawLine(g, _x[i], _y[i], _z[i], _x[i + 1], _y[i + 1], _z[i + 1], 1);
            }
            for (int i = 16; i < _x.Length - 1; i++)//绘制底部实线部分
            {
                DrawLine(g, _x[i], _y[i], _z[i], _x[i + 1], _y[i + 1], _z[i + 1], 0);
            }

        }
        //
        private DateTime _StartTime = DateTime.Now;
        private List<PointF> _VTPoints = new List<PointF>();
        private void DrawVTImage()
        {
            int width = pictureBox2.Width;
            int height = pictureBox2.Height;
            //
            Bitmap bm = new Bitmap(width, height);
            //
            Graphics g = Graphics.FromImage(bm);
            Matrix matrix = new Matrix(1, 0, 0, -1, 0, height);
            g.Transform = matrix;
            g.SmoothingMode = SmoothingMode.AntiAlias;
            //网格绘制
            Pen pen = new Pen(Color.Black);
            g.DrawLine(pen, new Point(0, 20), new Point(width, 20));
            g.DrawLine(pen, new Point(20, 0), new Point(20, height));
            //
            SolidBrush brush = new SolidBrush(Color.Black);
            g.ResetTransform();
            g.DrawString("0", this.Font, brush, 5, height - 15);
            g.Transform = matrix;
            //
            pen = new Pen(Color.Green);
            pen.DashPattern = new float[] { 3f, 5f };
            int n = (int)((width - 20) / 30);
            for (int i = 0; i < n; i++)
            {
                int _x = (i + 1) * 30 + 20;
                g.DrawLine(pen, new Point(_x, 20), new Point(_x, height));
                //
                g.ResetTransform();
                string txt = (i + 1).ToString();
                g.DrawString(txt, this.Font, brush, _x - 5, height-15);
                g.Transform = matrix;
            }
            n = (int)((height -20) / 30);
            for (int i = 0; i < n; i++)
            {
                int _y = (i + 1) * 30 + 20;
                g.DrawLine(pen, new Point(20, _y), new Point(width, _y));
                //
                g.ResetTransform();
                string txt = ((i + 1) * 2000).ToString();
                g.DrawString(txt, this.Font, brush, 25, height - _y - 5);
                g.Transform = matrix;
            }

            //轨迹绘制
            pen = new Pen(Color.Red);
            float nx = 1f / 30f;
            float ny = 2000f / 30f;
            for (int i = 0; i < _VTPoints.Count; i++)
            {
                if (i > 0)
                {
                    g.DrawLine(pen, new PointF(_VTPoints[i].X / nx + 20, _VTPoints[i].Y / ny + 20), new PointF(_VTPoints[i - 1].X / nx + 20, _VTPoints[i - 1].Y / ny + 20));
                }
            }
            g.Dispose();
            //
            pictureBox2.Image = bm;
        }

        private void button1_Click(object sender, EventArgs e)
        {
            //
            ushort axisx = GetAxisX();
            ushort axisy = GetAxisY();
            ushort axisz = GetAxisZ();
            ushort crd=GetCrd();
            //设置脉冲当量
            LTDMC.dmc_set_equiv(_CardID, axisx, decimal.ToDouble(numericUpDown3.Value));
            LTDMC.dmc_set_equiv(_CardID, axisy, decimal.ToDouble(numericUpDown5.Value));
            LTDMC.dmc_set_equiv(_CardID, axisz, decimal.ToDouble(numericUpDown7.Value));
            //位置清零
            LTDMC.dmc_set_position_unit(_CardID, axisx, 1200);
            LTDMC.dmc_set_position_unit(_CardID, axisy, 800);
            LTDMC.dmc_set_position_unit(_CardID, axisz, 0);
            //
            _PointsX.Clear();
            _PointsY.Clear();
            _PointsZ.Clear();
            DrawPosImage();
            _StartTime = DateTime.Now;
            _VTPoints.Clear();
            DrawVTImage();
            //
            ushort axisnum=3;
            ushort[] axises = new ushort[] { axisx, axisy,axisz };
            LTDMC.dmc_conti_open_list(_CardID, crd, axisnum, axises);
            //设置插补速度
            LTDMC.dmc_conti_set_speed_unit(_CardID, crd, decimal.ToDouble(numericUpDown8.Value));
            LTDMC.dmc_conti_set_taccdec(_CardID, crd, decimal.ToDouble(numericUpDown9.Value));
            LTDMC.dmc_conti_set_s_profile(_CardID, crd, 0, decimal.ToDouble(numericUpDown10.Value));
            LTDMC.dmc_conti_set_blend(_CardID, crd, (ushort)(radioButton1.Checked ? 0 : 1));
            //
            LTDMC.dmc_conti_line_unit(_CardID, crd, axisnum, axises, new double[] { -2000, 0, 1000 }, 0, 0);
            LTDMC.dmc_conti_arc_move_3points_unit(_CardID, crd, axisnum, axises, new double[] { 0, 2000, 0 }, new double[] { -1000, 1000, 1000 }, -1, 0, 0);
            LTDMC.dmc_conti_line_unit(_CardID, crd, axisnum, axises, new double[] { 2000, 0, -1000}, 0, 0);
            LTDMC.dmc_conti_line_unit(_CardID, crd, axisnum, axises, new double[] { 0, -2000, 0 },0,0);
            //
            LTDMC.dmc_conti_start_list(_CardID, crd);
            LTDMC.dmc_conti_close_list(_CardID, crd);


        }

        private void button2_Click(object sender, EventArgs e)
        {
            if (LTDMC.dmc_conti_get_run_state(_CardID, GetCrd()) == 0)
            {
                LTDMC.dmc_conti_pause_list(_CardID, GetCrd());
                button2.Text = "继续插补";
            }
            else if (LTDMC.dmc_conti_get_run_state(_CardID, GetCrd()) == 1)
            {
                LTDMC.dmc_conti_pause_list(_CardID, GetCrd());
                button2.Text = "暂停插补";
            }
        }

        private void button3_Click(object sender, EventArgs e)
        {
            LTDMC.dmc_conti_stop_list(_CardID, GetCrd(), 1);
            button2.Text = "暂停插补";
        }

        private void button4_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            timer1.Stop();
            LTDMC.dmc_board_close();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            double upx = 0;
            LTDMC.dmc_get_position_unit(_CardID, GetAxisX(), ref upx);
            textBox1.Text = upx.ToString();
            double upy = 0;
            LTDMC.dmc_get_position_unit(_CardID, GetAxisY(), ref upy);
            textBox2.Text = upy.ToString();
            double upz = 0;
            LTDMC.dmc_get_position_unit(_CardID, GetAxisZ(), ref upz);
            textBox3.Text = upz.ToString();
            //
            if (_PointsX.Count > 0)
            {
                if (_PointsX[_PointsX.Count - 1] != upx || _PointsY[_PointsY.Count - 1] != upy || _PointsZ[_PointsZ.Count - 1] != upz)
                {
                    _PointsX.Add(upx);
                    _PointsY.Add(upy);
                    _PointsZ.Add(upz);
                    DrawPosImage();
                }
            }
            else
            {
                _PointsX.Add(upx);
                _PointsY.Add(upy);
                _PointsZ.Add(upz);
            }
            //
            double sp = 0;
            LTDMC.dmc_read_current_speed_unit(_CardID, GetAxisX(), ref sp);
            textBox4.Text = sp.ToString();
            _VTPoints.Add(new PointF((float)((DateTime.Now - _StartTime).TotalSeconds), (float)sp));
            DrawVTImage();
            if (LTDMC.dmc_conti_check_done(_CardID, GetCrd()) == 0)
            {
                textBox5.Text = "运动中";
            }
            else
            {
                textBox5.Text = "空闲中";
            }
            switch (LTDMC.dmc_conti_get_run_state(_CardID, GetCrd()))
            {
                case 0:
                    textBox6.Text = "运动中";
                    break;
                case 1:
                    textBox6.Text = "暂停中";
                    break;
                case 2:
                    textBox6.Text = "正常停止";
                    break;
                case 3:
                    textBox6.Text = "未启动";
                    break;
                case 4:
                    textBox6.Text = "空闲";
                    break;
            }

            textBox7.Text = LTDMC.dmc_conti_remain_space(_CardID, GetCrd()).ToString();

            textBox8.Text = LTDMC.dmc_conti_read_current_mark(_CardID, GetCrd()).ToString();

        }
    }
}
