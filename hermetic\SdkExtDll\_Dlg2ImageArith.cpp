// _Dlg2ImageArith.cpp : implementation file
//

#include "stdafx.h"
#include "..\SdkExtDll_include\SdkExt.h"
#include "_Dlg2ImageArith.h"
#include "..\\InfomationDll_Include\\_InfoDefination.h"
#ifdef SDK_WITH_VISION
#include "..\\MilEx_Include\\MilExtDisplay.h"
#endif
#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// C_Dlg2ImageArith dialog
#ifdef SDK_WITH_VISION
extern C_MILApp *mil;
extern C_Video *vdo;
#endif

C_Dlg2ImageArith::C_Dlg2ImageArith(CWnd* pParent /*=NULL*/)
	: CDialog(C_Dlg2ImageArith::IDD, pParent)
{
	//{{AFX_DATA_INIT(C_Dlg2ImageArith)
		// NOTE: the ClassWizard will add member initialization here
	//}}AFX_DATA_INIT

	for(int i=0;i<3;i++)
	{
		pDlgImage[i] = NULL;
#ifdef SDK_WITH_VISION
// 		MilOverlay[i] =0;
// 		MilDisplay[i] =0;
		MilBuffer[i] = 0;
#endif
	}
}


void C_Dlg2ImageArith::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(C_Dlg2ImageArith)
	DDX_Control(pDX, IDC_COMBO_NO2, m_ComboNo2);
	DDX_Control(pDX, IDC_COMBO_NO1, m_ComboNo1);
	DDX_Control(pDX, IDC_COMBO_MIMARITH, m_ComboMimArith);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(C_Dlg2ImageArith, CDialog)
	//{{AFX_MSG_MAP(C_Dlg2ImageArith)
	ON_CBN_SELCHANGE(IDC_COMBO_MIMARITH, OnSelchangeComboMimarith)
	ON_WM_DESTROY()
	ON_WM_SHOWWINDOW()
	ON_BN_CLICKED(IDC_BT_OPEN1, OnBtOpen1)
	ON_BN_CLICKED(IDC_BT_OPEN2, OnBtOpen2)
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// C_Dlg2ImageArith message handlers

BOOL C_Dlg2ImageArith::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
#ifdef SDK_WITH_VISION
	for(int i=0;i<15;i++)
	{
		m_ComboMimArith.AddString(mil->mMimArith.Name[i]);	
	}
	m_ComboMimArith.SetCurSel(mil->mMimArith.index);
	m_ComboNo1.AddString("Image.1");
	m_ComboNo1.AddString("Image.2");
	m_ComboNo1.SetCurSel(0);

	m_ComboNo2.AddString("Image.1");
	m_ComboNo2.AddString("Image.2");
	m_ComboNo2.SetCurSel(1);
#endif	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void C_Dlg2ImageArith::OnSelchangeComboMimarith() 
{
#ifdef SDK_WITH_VISION
	mil->mMimArith.index = m_ComboMimArith.GetCurSel();
	MimArith(MilBuffer[m_ComboNo1.GetCurSel()], MilBuffer[m_ComboNo2.GetCurSel()], MilBuffer[2], mil->mMimArith.Arith[mil->mMimArith.index]);	

	for(int i=0;i<3;i++){
		if(pDlgImage[i])
		{
			pDlgImage[i]->ShowWindow(SW_SHOW);
			pDlgImage[i]->SetActiveWindow();
		}
		else
		{
			pDlgImage[i] = new C_DlgImage;
			pDlgImage[i]->Create(IDD_DLG_IMAGE,NULL);
// 			pDlgImage[i]->CenterWindow();
			pDlgImage[i]->ShowWindow(SW_SHOW);
		}			
	}
#endif
}


void C_Dlg2ImageArith::OnDestroy() 
{
#ifdef SDK_WITH_VISION	
	if(mil->bVideoCardSuccess)
	{
		for(int i=0;i<3;i++)
		{
// 			if(MilDisplay[i] == 0) return;		
// 			if (MdispInquire(MilDisplay[i], M_DISP_MODE, M_NULL) == M_WINDOWED){
// 				//Unselect the display window
// 				MdispDeselect(MilDisplay[i], MilBuffer[i]);	
// 			}
// 			//Free the display
// 			if (MilDisplay[i] != 0) MdispFree(MilDisplay[i]);
// 			MilDisplay[i] = 0;
			milDisp[i].MilDistroyDisp(MilBuffer[i]);
			//Free the buffer
			if (MilBuffer[i] != 0) MbufFree(MilBuffer[i]);
			MilBuffer[i] = 0;			
		}
	}	
#endif	

	for(int i=0;i<3;i++)
	{
		if(pDlgImage[i]){
			delete pDlgImage[i];
			pDlgImage[i]=NULL;
		}			
	}
	
	CDialog::OnDestroy();
	
	// TODO: Add your message handler code here
	
}

void C_Dlg2ImageArith::OnShowWindow(BOOL bShow, UINT nStatus) 
{
	CDialog::OnShowWindow(bShow, nStatus);
	int i;
	for(i=0;i<3;i++){
		if(!pDlgImage[i])
		{
			pDlgImage[i] = new C_DlgImage;
			pDlgImage[i]->Create(IDD_DLG_IMAGE,NULL);
//			pDlgImage[i]->CenterWindow();
//			pDlgImage[i]->ShowWindow(SW_SHOW);
		}
#ifdef SDK_WITH_VISION
		if(MilBuffer[i]==0){
			MbufAlloc2d(mil->milApp.m_MilSystem, mil->SizeX, mil->SizeY, 8 + M_UNSIGNED, M_IMAGE + M_DISP + M_PROC, &MilBuffer[i]);
		}
		
// 		if(MilDisplay[i] == 0)
// 		{
// 			MdispAlloc(mil->MilSystem, M_DEFAULT, M_DISPLAY_SETUP, M_DEFAULT, &MilDisplay[i]);
// 		}
		nFactor = 1;
//		if (MilDisplay[i]!=0/* && !DEBUGPROGRAM*/)
		{
//#ifndef _DEBUG
			if(i<2){
				CString str;
				str.Format("Image.%d",i+1);
				pDlgImage[i]->SetWindowText(str);
			}
			else{
				pDlgImage[i]->SetWindowText("Result Image");
			}	
//			CDisplaySelect selectDisp(MilDisplay[i], MilBuffer[i], pDlgImage[i]->pLblPicture->GetSafeHwnd());
			milDisp[i].MilAllocDisp(pDlgImage[i]->pLblPicture->GetSafeHwnd(), mil->milApp.m_MilSystem, MilBuffer[i]);
//			MdispSelectWindow(MilDisplay[i], MilBuffer[i], pDlgImage[i]->pLblPicture->GetSafeHwnd());
			MdispZoom(milDisp[i].m_MilDisplay, nFactor, nFactor);
			pDlgImage[i]->SetDisplayBuffer(MilBuffer[i],milDisp[i].m_MilDisplay,nFactor, pDlgImage[i]->pLblPicture);				
			
			
//			mil->InitOverlay(MilDisplay[i],MilOverlay[i]);
//#endif
		}
#endif
	}	
	
	for(i=0;i<3;i++){
		if(pDlgImage[i])
		{
			pDlgImage[i]->ShowWindow(SW_SHOW);
			pDlgImage[i]->SetActiveWindow();
		}
//		else
//		{
//			pDlgImage[i] = new C_DlgImage;
//			pDlgImage[i]->Create(IDD_DLG_IMAGE,NULL);
//			pDlgImage[i]->CenterWindow();
//			pDlgImage[i]->ShowWindow(SW_SHOW);
//		}
	}		
	// TODO: Add your message handler code here
}

void C_Dlg2ImageArith::OnOK() 
{
	// TODO: Add extra validation here
	OnDestroy() ;
	CDialog::OnOK();
}

void C_Dlg2ImageArith::OnBtOpen1() 
{
#ifdef SDK_WITH_VISION
	vdo->OpenImage(MilBuffer[0]);
#endif
	OnSelchangeComboMimarith();
}

void C_Dlg2ImageArith::OnBtOpen2() 
{
#ifdef SDK_WITH_VISION
	vdo->OpenImage(MilBuffer[1]);
#endif
	OnSelchangeComboMimarith();
}
