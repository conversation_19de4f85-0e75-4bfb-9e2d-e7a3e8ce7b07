﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;

namespace ModbusRTU_CS
{
	public class DataReceivedEventHandle : EventArgs
	{
		public DataReceivedEventHandle(string data)
		{
			this.data = data;
		}

		// The fire event will have two pieces of information-- 
		// 1) Where the fire is, and 2) how "ferocious" it is.  

		public string data;
	}	//end of class FireEventArgs

}