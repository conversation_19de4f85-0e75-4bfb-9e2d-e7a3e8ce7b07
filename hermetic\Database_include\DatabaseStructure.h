// DatabaseStructure.h: interface for the CDatabaseStructure class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_DATABASESTRUCTURE_H__ABAD2936_9865_4760_8A21_E9DE8E2B35CD__INCLUDED_)
#define AFX_DATABASESTRUCTURE_H__ABAD2936_9865_4760_8A21_E9DE8E2B35CD__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "..\\Database_Include\\Database.h"

struct tag_FieldType
{
	int Text;
	int Numeric;
	int Date;
	int Char;
	int Int;
	int SmallInt;
	int Bit;
	tag_FieldType();
};

struct tag_FieldDescription
{
	tag_FieldDescription();
	tag_FieldDescription(CString Name, int Type, CString Size, BOOL EnableNull, CString Default);
	CString GetFieldDesc();
private:
	CString strFieldName;
	int FieldType;
	CString sSize;
	BOOL bEnableNull;
	CString strDefault;		
	CString FieldDesc();		
};


#if _USRDLL
#define _DATABASE_STRUCTTURE_DLL __declspec(dllexport) 
#else
#define _DATABASE_STRUCTTURE_DLL __declspec(dllimport) 
#endif

class _DATABASE_STRUCTTURE_DLL CDatabaseStructure
{
public:
	CDatabaseStructure();
	virtual ~CDatabaseStructure();

	BOOL GetFieldInfo(_RecordsetPtr rs, 
		CArray<CString, CString&> &arrFieldName, 
		CArray<ADOX::DataTypeEnum, ADOX::DataTypeEnum&> &arrEnumDataType, 
						   CArray<long, long&> &arrDefineSize);

	long GetFieldType(C_Database &db, IN CString TableName,IN CString FieldName,IN BOOL bPrompt);
	BOOL CreateTableByScriptFile(C_Database &db, LPCSTR SqlScriptFile, char separator = ';', char Remark = '-');
	CString CreateTableStatement(CString strTableName, int nFieldCount,tag_FieldDescription * FieldDesc);
};

#endif // !defined(AFX_DATABASESTRUCTURE_H__ABAD2936_9865_4760_8A21_E9DE8E2B35CD__INCLUDED_)
