#if !defined(AFX_WORKSHEETDLG_H__88F68DAC_4F71_409A_AEDB_8FAFCFD0C24E__INCLUDED_)
#define AFX_WORKSHEETDLG_H__88F68DAC_4F71_409A_AEDB_8FAFCFD0C24E__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// WorksheetDlg.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// CWorksheetDlg dialog

class CWorksheetDlg : public CDialog
{
// Construction
public:
	CWorksheetDlg(CWnd* pParent = NULL);   // standard constructor

	CStringArray m_sa;
	CString     SheetName;

// Dialog Data
	//{{AFX_DATA(CWorksheetDlg)
	enum { IDD = IDD_WORKSHEET_DLG };
	CListBox	m_lbSel;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CWorksheetDlg)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(CWorksheetDlg)
	afx_msg void OnOkBn();
	virtual BOOL OnInitDialog();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_WORKSHEETDLG_H__88F68DAC_4F71_409A_AEDB_8FAFCFD0C24E__INCLUDED_)
