#pragma once

#include "..\\InfomationDll_Include\\_InfoDefination.h"
#include "..\\ControlDll_include\\ControlConst.h"
#include "IOC0640.h"
#include "math.h"

class C_IOC0640
{
public:
	C_IOC0640(void);
	~C_IOC0640(void);

	int ReadADC(int nMdNo, BYTE bChannel, double &dVoltageValue, int &sDigitalValue);
	BOOL ReadInPortEx(int nPortIndex, int nBitIndex);
	void ReadInPorts();
	BOOL CardClose();
	BOOL CardOpen(tag_CardsInfo *card);
	BOOL ReadInPort(int nPortIndex, BYTE nBitIndex);
	BOOL WriteOutport(int nPortIndex, BYTE nBitIndex, BOOL bOpen);
	DWORD wInValue[16];
	tag_CardsInfo *m_pCard;
private:
	unsigned short *mOutpotBit;
};

