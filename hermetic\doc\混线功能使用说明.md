# 混线功能实现说明

## 概述

本文档描述了在hermetic气密性测试设备中实现的混线功能。混线功能允许同系列机型间的自动切换，无需重启软件，大大提高了生产效率。

## 功能特点

1. **自动检测**: 通过扫描PCBA号自动识别机型信息
2. **同系列限制**: 只允许同系列机型间的混线切换
3. **配置热加载**: 自动重新加载对应机型的配置文件
4. **最小侵入**: 在现有扫描流程中无缝集成
5. **错误处理**: 完善的异常处理和日志记录
6. **线程安全**: 使用临界区保证多线程安全

## 实现架构

### 核心类和文件

1. **CMixedLineManager** (`MixedLineManager.h/cpp`)
   - 混线功能管理类
   - 负责与MES系统通信
   - 处理JSON响应解析

2. **ChermeticFrameApp** (`hermeticFrame.h/cpp`)
   - 应用程序主类扩展
   - 混线功能初始化和管理
   - 配置文件重新加载

3. **CFlowProcess** (`ProFlow.cpp`)
   - 在PCBA扫描流程中集成混线检查

### 关键数据结构

```cpp
struct tag_MixedLineInfo
{
    CString strProdModel;    // 生产机型
    CString strSalesModel;   // 销售机型  
    CString strSeriesModel;  // 系列机型
    CString strProjectNo;    // 专案号
    CString strMfgPlanName;  // 生产方案
    CString strOrderId;      // 制造命令
    bool    bValid;          // 信息是否有效
};
```

## API接口

混线功能使用以下4个MES API接口：

1. **BasViewDataList** - 检查混线功能是否启用
   ```json
   {
     "key1": "线体ID",
     "key2": "",
     "key3": "MIXED_MODEL",
     "procstep": "1",
     "tableName": "A_LINE_TESTTOOL_TEST"
   }
   ```

2. **AateAsyApiGetModelInfoByModel** - 根据机型获取系列信息
   ```json
   {
     "productModel": "生产机型"
   }
   ```

3. **BasViewDataList** - 获取扫描项配置
   ```json
   {
     "key1": "机型",
     "key2": "工站",
     "key3": "ALL",
     "procstep": "1",
     "table_name": "A_AUTO_SCAN_SN_COORD"
   }
   ```

4. **AateAsyApiViewLotInfo** - 根据PCBA号获取批次信息
   ```json
   {
     "pcba": "PCBA号"
   }
   ```

## 工作流程

### 1. 程序启动时
```
初始化混线功能 -> 检查混线功能是否启用 -> 获取登录机型信息 -> 设置当前机型信息
```

### 2. PCBA扫描时
```
扫描PCBA号 -> 调用混线处理 -> 获取PCBA批次信息 -> 检查系列机型匹配 -> 切换配置(如需要) -> 继续测试流程
```

### 3. 配置切换过程
```
更新机型信息 -> 重新加载配置数据库 -> 重新初始化运动控制 -> 重新加载流程参数 -> 完成切换
```

## 关键函数说明

### 应用程序类函数

- `InitMixedLineFunction()`: 初始化混线功能
- `CheckMixedLineEnabled()`: 检查混线功能是否启用
- `ProcessMixedLine()`: 处理混线逻辑的主函数
- `GetLotInfoByPcba()`: 根据PCBA号获取批次信息
- `UpdateCurrentModelInfo()`: 更新当前机型信息
- `ReloadConfigForModel()`: 重新加载机型配置

### 混线管理器函数

- `SendMESRequest()`: 发送MES请求的通用函数
- `GetModelInfoByModel()`: 根据机型获取系列机型信息
- `GetLotInfoByPcba()`: 根据PCBA号获取批次信息
- `ParseJsonResponse()`: 解析JSON响应

## 配置要求

1. **MES系统配置**
   - 确保MES系统支持混线相关API
   - 配置正确的线体ID和机型信息
   - 设置混线功能启用标志

2. **本地配置**
   - 确保配置文件路径正确
   - 各机型的配置数据库完整
   - TCP服务器正常运行

## 错误处理

### 常见错误和解决方案

1. **混线功能初始化失败**
   - 检查MES连接是否正常
   - 确认线体ID配置正确
   - 查看日志了解具体错误

2. **系列机型不匹配**
   - 确认PCBA号对应的系列机型
   - 检查MES系统中的机型配置
   - 只允许同系列机型间混线

3. **配置文件加载失败**
   - 检查配置文件路径是否存在
   - 确认文件权限设置正确
   - 验证数据库文件完整性

## 日志记录

混线功能的所有操作都会记录到日志中，包括：

- 混线功能初始化状态
- PCBA扫描和机型识别
- 配置切换过程
- 错误和异常信息

日志格式：`[混线管理] 具体操作信息`

## 性能考虑

1. **临界区保护**: 使用临界区确保混线操作的原子性
2. **配置缓存**: 避免重复加载相同配置
3. **异步处理**: 网络请求使用合理的超时设置
4. **内存管理**: 及时释放不再使用的资源

## 注意事项

1. **系列机型限制**: 严格限制只能在同系列机型间混线
2. **配置一致性**: 确保切换后的配置与实际硬件匹配
3. **测试中断**: 混线切换时会中断当前测试流程
4. **权限控制**: 确保操作员有足够权限进行混线操作

## 调试和测试

### 测试步骤

1. 启动程序，检查混线功能初始化是否成功
2. 扫描不同系列的PCBA，验证系列检查功能
3. 扫描同系列不同机型的PCBA，验证切换功能
4. 检查配置是否正确加载
5. 验证测试流程是否正常继续

### 调试工具

- 查看日志文件了解详细执行过程
- 使用MES系统测试工具验证API响应
- 监控TCP通信确保网络连接正常

## 维护建议

1. **定期检查**: 定期检查MES系统连接和API可用性
2. **配置备份**: 定期备份各机型的配置文件
3. **日志清理**: 定期清理过期的日志文件
4. **性能监控**: 监控混线切换的性能影响

---

*本文档描述了hermetic设备混线功能的完整实现方案，确保了在不重启程序的情况下实现同系列机型间的自动切换。* 