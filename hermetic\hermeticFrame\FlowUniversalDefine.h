#pragma once

#include "RegApp.h"

enum e_Machine_Type
{
	eADE0204,
	eADE0206,
	eADE0208,
	eADE0208B,
	eADE0404,
	eADE0404B,
	eADE0404C,
	eADE0202,
	eADE0404F,
	eMachine_Type_Count,
};
enum e_Station
{
	eStation_Up_Left,
	eStation_Up_Right,
	eStation_Down_Left,
	eStation_Down_Right,
	eStation_Count,
};
enum e_Test_Item
{
	eItem_1,
	eItem_2,
	eItem_3,
	eItem_4,
	eItem_Count,
};
struct tag_Station_Info
{
	//CString					strName_Station[eStation_Count];
	bool					bEnable[eStation_Count];
	tag_Station_Info()
	{
		//strName_Station[eStation_Up_Left]			= "Up-Left";
		//strName_Station[eStation_Up_Right]			= "Up-Right";
		//strName_Station[eStation_Down_Left]			= "Down-Left";
		//strName_Station[eStation_Down_Right]		= "Down-Right";

		for(int i=0; i<eStation_Count; i++)
		bEnable[i] = TRUE;
	}
};