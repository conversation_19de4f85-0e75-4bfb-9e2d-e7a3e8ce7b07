// Jog.cpp : implementation file
//

#include "stdafx.h"
#include "ModbusRTU_CPP.h"
#include "Jog.h"
#include "afxdialogex.h"


// CJog dialog

IMPLEMENT_DYNAMIC(CJog, CDialog)

CJog::CJog(CWnd* pParent /*=NULL*/)
	: CDialog(CJog::IDD, pParent)
{

	m_nJogSpeed = 10.0;
	m_nJogAccel = 100.0;
	m_nJogDecel = 100.0;
}

CJog::~CJog()
{
}

void CJog::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_EDIT_JOG_SPEED, m_nJogSpeed);
	//DDV_MinMaxDouble(pDX, m_nJogSpeed, 0.025, 100);
	DDX_Text(pDX, IDC_EDIT_JOG_ACCEL, m_nJogAccel);
	//DDV_MinMaxDouble(pDX, m_nJogAccel, 0.167, 5461.167);
	DDX_Text(pDX, IDC_EDIT_JOG_DECEL, m_nJogDecel);
	//DDV_MinMaxDouble(pDX, m_nJogDecel, 0.167, 5461.167);
}


BEGIN_MESSAGE_MAP(CJog, CDialog)
	ON_WM_LBUTTONDOWN()
	ON_WM_LBUTTONUP()
	ON_EN_KILLFOCUS(IDC_EDIT_JOG_SPEED, &CJog::OnEnKillfocusEditJogSpeed)
	ON_EN_CHANGE(IDC_EDIT_JOG_SPEED, &CJog::OnChangeEditJogSpeed)
END_MESSAGE_MAP()

double CJog::GetJogSpeed()
{
	UpdateData(TRUE);
	return this->m_nJogSpeed;
}

double CJog::GetJogAccel()
{
	UpdateData(TRUE);
	return this->m_nJogAccel;
}

double CJog::GetJogDecel()
{
	UpdateData(TRUE);
	return this->m_nJogDecel;
}

// CJog message handlers


void CJog::SetButtonEnable(int nID, BOOL bEnable)
{
	((CButton *)GetDlgItem(nID))->EnableWindow(bEnable);
}

void CJog::OnLButtonDown(UINT nFlags, CPoint point)
{
	CDialog::OnLButtonDown(nFlags, point);
}


void CJog::OnLButtonUp(UINT nFlags, CPoint point)
{
	CDialog::OnLButtonUp(nFlags, point);
}


BOOL CJog::PreTranslateMessage(MSG* pMsg)
{
	// TODO: Add your specialized code here and/or call the base class

	if (pMsg->message == WM_LBUTTONDOWN)
	{
		if (pMsg->hwnd == ((CButton*)GetDlgItem(IDC_BTN_JOG_CWJOG))->m_hWnd)
		{
			int id = IDC_BTN_JOG_CWJOG;
			::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONMOUSEDOWN_MESSAGE, 0, (LPARAM)&id);
		}
		else if (pMsg->hwnd == ((CButton*)GetDlgItem(IDC_BTN_JOG_CCWJOG))->m_hWnd)
		{
			int id = IDC_BTN_JOG_CCWJOG;
			::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONMOUSEDOWN_MESSAGE, 0, (LPARAM)&id);
		}
	}
	else if (pMsg->message == WM_LBUTTONUP)
	{
		if (pMsg->hwnd == ((CButton*)GetDlgItem(IDC_BTN_JOG_CWJOG))->m_hWnd)
		{
			int id = IDC_BTN_JOG_CCWJOG;
			::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONMOUSEUP_MESSAGE, 0, (LPARAM)&id);
		}
		else if (pMsg->hwnd == ((CButton*)GetDlgItem(IDC_BTN_JOG_CCWJOG))->m_hWnd)
		{
			int id = IDC_BTN_JOG_CCWJOG;
			::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONMOUSEUP_MESSAGE, 0, (LPARAM)&id);
		}
	}
	return CDialog::PreTranslateMessage(pMsg);
}


void CJog::OnEnKillfocusEditJogSpeed()
{
	
}


void CJog::OnChangeEditJogSpeed()
{
	// TODO:  If this is a RICHEDIT control, the control will not
	// send this notification unless you override the CDialog::OnInitDialog()
	// function and call CRichEditCtrl().SetEventMask()
	// with the ENM_CHANGE flag ORed into the mask.

	// TODO:  Add your control notification handler code here

	UpdateData(TRUE);
	if (this->m_nJogSpeed < 0.025 || this->m_nJogSpeed > 100)
	{
		if (this->m_nJogSpeed < 0.025)
		{
			this->m_nJogSpeed = 0.025;
			UpdateData(FALSE);
		}
		else if (this->m_nJogSpeed > 100)
		{
			this->m_nJogSpeed = 100;
			UpdateData(FALSE);
		}
	}
}


void CJog::OnOK()
{
	// TODO: Add your specialized code here and/or call the base class

	//CDialog::OnOK();
}


void CJog::OnCancel()
{
	// TODO: Add your specialized code here and/or call the base class

	//CDialog::OnCancel();
}
