#pragma once

#include "Language_Universal.h"
#include "Language_Dlg_Main.h"
#include "Language_Dlg_Para.h"
#include "Language_Flow.h"
#include "Lang_Motion.h"


class CLanguage
{
public:
	CLanguage(void);
	~CLanguage(void);

	e_Lang_Type					m_eLang_Type;
	CString						m_strStation[eLang_Count][eStation_Count];
	CLanguage_Dlg_Main			m_Dlg_Main;
	CLanguage_Dlg_Para			m_Dlg_Para;
	CLanguage_Flow				m_Flow;
	CLang_Motion				m_Motion;

	bool Set_Lang();
	bool Set_Control(e_Lang_Type eLang_Type);
	void Set_Station(e_Lang_Type eLang_Type);
	void Set_Msg(e_Lang_Type eLang_Type);
	void Set_Menu_Lang(e_Lang_Type eLang_Type);
	void Set_Lang_Type(e_Lang_Type eLang);
	void Get_Lang_Type();
	
};

