// Excel.h: interface for the CExcel class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_EXCEL_H__08EDEC97_4905_4C72_A44E_BD495D64C7E6__INCLUDED_)
#define AFX_EXCEL_H__08EDEC97_4905_4C72_A44E_BD495D64C7E6__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "Excel9.h"

class CExcel  
{
public:
	BOOL GetWorksheet(CStringArray &saWorksheet);
	BOOL GetRowStrs(CString* pstr, long nRow, long nStartCol, long nStopCol);
	BOOL GetRowDates(CTime* pt, long nRow, long nStartCol, long nStopCol);
	BOOL GetColStrs(CString *pstr, long nCol, long nStartRow, long nStopRow);
	void Close(CString strPath);
	BOOL Save(CString strPath, CString strWb);
	//BOOL GetOpenSpec(_OpenSpec& os);
	//BOOL ClearContents(long nRow, long nCol);
	BOOL SetValues(double* dVal, long nVal, long nRow, long nCol);
	BOOL GetValue(double& dVal, long nRow, long nCol);
	BOOL SetValue(long nRow, long nCol, CString strValue);

	BOOL GetActiveCellPos(long& nRow, long& nCol);
	BOOL GetActiveWorksheet();
	BOOL GetActiveWorkbook();
	BOOL GetValues(double* dVal, long nCol, long nStartRow, long nStopRow);
	CString GetWorksheetType(CString strWbName);
	void SetIdentifier(CString str);
	void CloseFile(CString strWbName, CString strPath);
	void HideExcel();
	//BOOL DeleteCol(long nColPos, long nNoOfColToDelete);
	//BOOL DeleteRow(long nRowPos, long nNoOfRowToDelete);
	//BOOL ScanTemplateCol(CString strWbName, CString strWsName, CSVFileRecord* pcsv, CString* pTL, long nNoOfTitle, BOOL bDeleteUnusedTitleRow = FALSE);
	//BOOL ScanAllTemplateCol(CString strWbName, CSVFileRecord* pcsv, CString* pTL, long nNoOfTitle, BOOL bDeleteUnusedTitleRow = FALSE);
	//void ScanCol(CString strDestWb, CString strDestWs, long nDestRowNo, CString strSrcWs, long nHeadNo, long nNoOfRow, CString* pTL, long nNoOfTitle);
	//BOOL CopyRange(CString strDestWb, CString strDestWs, long nDestStartRow, long nDestStartCol, long nDestStopRow, long nDestStopCol, 
	//	 CString strSrcWb, CString strSrcWs, long nSrcStartRow, long nSrcStartCol, long nSrcStopRow, long nSrcStopCol);
	//BOOL CopyRangeVal(CString strDestWb, CString strDestWs, long nDestStartRow, long nDestStartCol, long nDestStopRow, long nDestStopCol, 
	//	 CString strSrcWb, CString strSrcWs, long nSrcStartRow, long nSrcStartCol, long nSrcStopRow, long nSrcStopCol);
	//CString GetWorksheetType();
	BOOL GetStr(CString& str, long nRow, long nCol);
	BOOL Search(long &nRow, long &nCol, long nStartRow, long nStopRow, long nStartCol, long nStopCol, CString strSearch);
	//BOOL InsertRow(long nRowPos, long nNoOfRowToInsert);
	BOOL SetValue(long nRow, long nCol, double dValue);
	BOOL GetWorksheet(CString strName);
	BOOL OpenFile(CString strPath);
	void ReleaseExcel();
	BOOL GetWorkbook(CString strName);
	BOOL NewWorkbook();
	void ShowExcel();
	BOOL AttachToWorkbooks();
	BOOL AttachToExcel(BOOL bCreateNew = FALSE);
	CExcel();
	virtual ~CExcel();

	_Application	m_app;
	Workbooks		m_wbs;
	_Workbook		m_wb;
	Worksheets		m_wss;
	_Worksheet		m_ws;
	Range			m_rng;
	CString			m_strIdentifier;
	BOOL			m_bUseOCR;
	BOOL			m_bUseSTS;
	BOOL			m_bUseMISI;
	BOOL			m_bUseGuzik;
	BOOL			m_bHGA;
	BOOL			m_bAuto;

private:
	//CString GetID(CString str);
	//BOOL GetValue(long& nValue, CString str);
	//BOOL ScanTemplateColExt(CString strWbName, CString strWsName, CSVFileRecord* pcsv, CString* pTL, long nNoOfTitle, BOOL bInsertEmptyRows, BOOL bDelete1stCol, BOOL bDeleteUnusedTitleRow = FALSE);
	CString XCol(long nCol1, long nCol2);
	//BOOL InsertRow2(long nRowPos, long nNoOfRowToInsert);
	CString GetName(CString strFileName);
	long GetPos(CString* pTL, long nNoOfTitle, CString str);
//	long GetRecordNo(CSVFileRecord* pcsv, long nHeadNo);
	CString XRange(long nStartRow, long nStartCol, long nStopRow, long nStopCol);
	CString XRow(long nRow1, long nRow2);
	CString XRow(long nRow);
	CString XRange(long nRow, long nCol);
	CString XCol(long nCol);
	BOOL AttachToWorksheets();
	BOOL AttachToApplication(BOOL bCreateNew = FALSE);
protected:
	//BOOL GetDate(CTime &t, long nRow, long nCol);
	//BOOL ScanRowForOpenSpec(CString* strOSName, double* dOSMax, double* dOSMin);
	BOOL GetValue(double& dValue, CString str);
};

#endif // !defined(AFX_EXCEL_H__08EDEC97_4905_4C72_A44E_BD495D64C7E6__INCLUDED_)
