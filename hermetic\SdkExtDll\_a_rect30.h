#if !defined(AFX__A_RECT30_H__F1241347_96C6_4D8B_901D_CB4D37375B9F__INCLUDED_)
#define AFX__A_RECT30_H__F1241347_96C6_4D8B_901D_CB4D37375B9F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// Machine generated IDispatch wrapper class(es) created by Microsoft Visual C++

// NOTE: Do not modify the contents of this file.  If this class is regenerated by
//  Microsoft Visual C++, your modifications will be overwritten.

/////////////////////////////////////////////////////////////////////////////
// C_A_Rect30 wrapper class

class C_A_Rect30 : public CWnd
{
protected:
	DECLARE_DYNCREATE(C_A_Rect30)
public:
	CLSID const& GetClsid()
	{
		static CLSID const clsid
			= { 0x44f191b7, 0x5bb2, 0x463c, { 0xac, 0xab, 0xa8, 0x3c, 0xe0, 0xb0, 0xd4, 0xbd } };
		return clsid;
	}
	virtual BOOL Create(LPCTSTR lpszClassName,
		LPCTSTR lpszWindowName, DWORD dwStyle,
		const RECT& rect,
		CWnd* pParentWnd, UINT nID,
		CCreateContext* pContext = NULL)
	{ return CreateControl(GetClsid(), lpszWindowName, dwStyle, rect, pParentWnd, nID); }

    BOOL Create(LPCTSTR lpszWindowName, DWORD dwStyle,
		const RECT& rect, CWnd* pParentWnd, UINT nID,
		CFile* pPersist = NULL, BOOL bStorage = FALSE,
		BSTR bstrLicKey = NULL)
	{ return CreateControl(GetClsid(), lpszWindowName, dwStyle, rect, pParentWnd, nID,
		pPersist, bStorage, bstrLicKey); }

// Attributes
public:

// Operations
public:
	void InitPos(short OffsetX, short OffsetY, short Width, short Height);
	void Refresh();
	BOOL GetEnabled(short Index);
	void SetEnabled(short Index, BOOL bNewValue);
	LPDISPATCH GetFont();
	void SetRefFont(LPDISPATCH newValue);
	unsigned long GetCenLineColor();
	void SetCenLineColor(unsigned long newValue);
	short GetCenLineWidth();
	void SetCenLineWidth(short nNewValue);
	unsigned long GetObjLineColor();
	void SetObjLineColor(unsigned long newValue);
	short GetObjLineWidth();
	void SetObjLineWidth(short nNewValue);
	unsigned long GetFontColor();
	void SetFontColor(unsigned long newValue);
	unsigned long GetBackColor();
	void SetBackColor(unsigned long newValue);
	void UserControl_LostFocus();
	CString GetCaption();
	void SetCaption(LPCTSTR lpszNewValue);
	short GetMarkSize();
	void SetMarkSize(short nNewValue);
	float GetX1();
	void SetX1(float newValue);
	float GetX2();
	void SetX2(float newValue);
	float GetY1();
	void SetY1(float newValue);
	float GetY2();
	void SetY2(float newValue);
	BOOL GetIsObjVisible(short Index);
	void SetIsObjVisible(short Index, BOOL bNewValue);
	BOOL GetIsCenVisible();
	void SetIsCenVisible(BOOL bNewValue);
	void SetIsVisible(BOOL bNewValue);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__A_RECT30_H__F1241347_96C6_4D8B_901D_CB4D37375B9F__INCLUDED_)
