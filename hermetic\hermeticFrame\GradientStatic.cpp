// GradientStatic.cpp : implementation file
//
// 29/05/03 : BUG AVEC MSC 6 ET WIN98 ACCESS VIOLATION
// 06/06/03 : PB FLIKER

#include "stdafx.h"
#include "GradientStatic.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// CGradientStatic

BEGIN_MESSAGE_MAP(CGradientStatic, CStatic)
	//{{AFX_MSG_MAP(CGradientStatic)
	ON_WM_PAINT()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// CGradientStatic message handlers

//  ************************************************************************************
//  CGradientStatic::CGradientStatic()
//  
//  Purpose:
//  
//  Parameters:
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
CGradientStatic::CGradientStatic()
{
	m_iLeftSpacing = 10;
	clLeft = GetSysColor(COLOR_ACTIVECAPTION);
	clRight = GetSysColor(COLOR_BTNFACE);
	clText = GetSysColor(COLOR_CAPTIONTEXT);
	
	m_iAlign = 0;
	hinst_msimg32 = NULL;
	m_bCanDoGradientFill = FALSE;

/* BUG AVEC MSC 6 ET WIN98

	hinst_msimg32 = LoadLibrary( "msimg32.dll" );
	if(hinst_msimg32)
	{
		m_bCanDoGradientFill = TRUE;		
		dllfunc_GradientFill = ((LPFNDLLFUNC1) GetProcAddress( hinst_msimg32, "GradientFill" ));
	}
*/

	m_strText	= _T("");
}

//  ************************************************************************************
//  CGradientStatic::~CGradientStatic()
//  
//  Purpose:
//  
//  Parameters:
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
CGradientStatic::~CGradientStatic()
{
	if (hinst_msimg32)
	{
		FreeLibrary( hinst_msimg32 );
	}
}

//  ************************************************************************************
//  SetGradientColor()
//  
//  Purpose:
//  
//  Parameters:
//		[clrStart] :
//		[clrEnd] :
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
void CGradientStatic::SetGradientColor(long clrStart, long clrEnd)
{
	clLeft	= clrStart;
	clRight	= clrEnd;
}



//this function will be used only if msimg32.dll library is not available
//  ************************************************************************************
//  CGradientStatic::DrawGradRect()
//  
//  Purpose:
//  
//  Parameters:
//		[*pDC] :
//		[r] :
//		[cLeft] :
//		[cRight] :
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
void CGradientStatic::DrawGradRect(CDC *pDC, CRect r, COLORREF cLeft, COLORREF cRight)
{
   CRect stepR;					// rectangle for color's band
   COLORREF color;				// color for the bands
 
   
   float fStep = ((float)r.Width())/255.0f;	// width of color's band
   for (int iOnBand = 0; iOnBand < 255; iOnBand++) 
   {
	// set current band
	SetRect(&stepR,
		r.left+(int)(iOnBand * fStep), 
		r.top,
		r.left+(int)((iOnBand+1)* fStep), 
		r.bottom);	
	// set current color
	color = RGB((GetRValue(cRight)-GetRValue(cLeft))*((float)iOnBand)/255.0f+GetRValue(cLeft),
			(GetGValue(cRight)-GetGValue(cLeft))*((float)iOnBand)/255.0f+GetGValue(cLeft),
			(GetBValue(cRight)-GetBValue(cLeft))*((float)iOnBand)/255.0f+GetBValue(cLeft));
	// fill current band
	pDC->FillSolidRect(stepR,color);
   }
}

//  ************************************************************************************
//  CGradientStatic::OnPaint()
//  
//  Purpose:
//  
//  Parameters:
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
void CGradientStatic::OnPaint()
{
	CPaintDC dc(this); // device context for painting
	CRect rect;
	GetClientRect(&rect);
	/*
		if (m_bCanDoGradientFill) //msimg32.dll library is loaded
		{
		TRIVERTEX rcVertex[2];
		rcVertex[0].x=rect.left;
		rcVertex[0].y=rect.top;
		rcVertex[0].Red=GetRValue(clLeft)<<8;	// color values from 0x0000 to 0xff00 !!!!
		rcVertex[0].Green=GetGValue(clLeft)<<8;
		rcVertex[0].Blue=GetBValue(clLeft)<<8;
		rcVertex[0].Alpha=0x0000;
		rcVertex[1].x=rect.right;
		rcVertex[1].y=rect.bottom;
		rcVertex[1].Red=GetRValue(clRight)<<8;
		rcVertex[1].Green=GetGValue(clRight)<<8;
		rcVertex[1].Blue=GetBValue(clRight)<<8;
		rcVertex[1].Alpha=0;
		GRADIENT_RECT rect;
		rect.UpperLeft=0;
		rect.LowerRight=1;

		// fill the area
		dllfunc_GradientFill( dc,rcVertex,2,&rect,1,GRADIENT_FILL_RECT_H);

		}
		else

		{
		//msimg32.dll is not available. Let's use our own code to display gradient background.
		//This code is very simple and produces worse gradient that function from the library - but works!
		DrawGradRect(&dc,rect,clLeft,clRight);
		}
		*/

	//This code is very simple and produces worse gradient that function from the library - but works!
	DrawGradRect(&dc, rect, clLeft, clRight);


	//let's set color defined by user
	::SetTextColor(dc, clText);

	HFONT hfontOld = NULL;
	CFont* pFont = GetFont();
	CString m_sTEXT;
	GetWindowText(m_sTEXT);

	if (pFont)
	{
		hfontOld = (HFONT)SelectObject(dc.m_hDC, (HFONT)pFont->m_hObject);
	}

	::SetBkMode(dc, TRANSPARENT);
	GetClientRect(&rect);


	if (m_iAlign == 1) // center
		::DrawText(dc, m_sTEXT, -1, &rect, DT_SINGLELINE | DT_VCENTER | DT_CENTER);
	else if (m_iAlign == 0) // left
	{
		rect.left += m_iLeftSpacing;
		::DrawText(dc, m_sTEXT, -1, &rect, DT_SINGLELINE | DT_VCENTER | DT_LEFT);
	}
	else //right
	{
		rect.right -= m_iLeftSpacing;
		::DrawText(dc, m_sTEXT, -1, &rect, DT_SINGLELINE | DT_VCENTER | DT_RIGHT);
	}


	if (pFont)
	{
		::SelectObject(dc.m_hDC, hfontOld);
	}
}

//  ************************************************************************************
//  CGradientStatic::SetReverseGradient()
//  
//  Purpose:
//  
//  Parameters:
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
void CGradientStatic::SetReverseGradient()
{
	COLORREF cTemp = clLeft;
	clLeft = clRight;
	clRight = cTemp;
}



//  ************************************************************************************
//  CGradientStatic::SetWindowText()
//  
//  Purpose:
//  
//  Parameters:
//		[a_lpstr] :
//  
//  Returns:	none
//  
//		
//  ************************************************************************************
void CGradientStatic::SetWindowText(LPCTSTR a_lpstr)
{
// 04/09/03 : PB FLIQUER
	if (m_strText == a_lpstr) return;	//

	//m_strText.ReleaseBuffer();
// 3.1.0.3-4
	m_strText.Empty();
	m_strText = a_lpstr;

// 06/06/03 : PB FLIKER
	SetRedraw(FALSE);	
	CStatic::SetWindowText(a_lpstr);
	SetRedraw(TRUE);
// 06/06/03 : PB FLIKER

	Invalidate();
}
