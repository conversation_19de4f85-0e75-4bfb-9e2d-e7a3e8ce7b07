﻿生成启动时间为 2025/7/19 15:13:34。
     1>项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\SdkExtDll\SdkExtDll.vcxproj”在节点 2 上(rebuild 个目标)。
     1>_PrepareForClean:
         正在删除文件“.\..\..\temp\sdkExtDll\Debug\SdkExtDll.lastbuildstate”。
       InitializeBuildStatus:
         正在创建“.\..\..\temp\sdkExtDll\Debug\SdkExtDll.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _AFXEXT /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yc"stdafx.h" /Fp".\..\..\temp\sdkExtDll\Debug\SdkExtDll.pch" /Fo".\..\..\temp\sdkExtDll\Debug\\" /Fd".\..\..\temp\sdkExtDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt StdAfx.cpp
         StdAfx.cpp
         _WIN32_WINNT not defined. Defaulting to _WIN32_WINNT_MAXVER (see WinSDKVer.h)
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _DEBUG /D _WINDOWS /D _WINDLL /D _AFXEXT /D _VC80_UPGRADE=0x0600 /D _WINDLL /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /Gy- /fp:precise /Zc:wchar_t /Zc:forScope /Yu"stdafx.h" /Fp".\..\..\temp\sdkExtDll\Debug\SdkExtDll.pch" /Fo".\..\..\temp\sdkExtDll\Debug\\" /Fd".\..\..\temp\sdkExtDll\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ..\CommonClass\ButtonST\BCMenu.cpp ..\CommonClass\ButtonST\BtnST.cpp ..\CommonClass\ButtonST\CeXDib.cpp ..\CommonClass\ButtonST\ShadeButtonST.cpp ..\CommonClass\ListCtrl\_ListCtrl.cpp DlgConditionMotor.cpp DlgConditionOutput.cpp DlgControl.cpp DlgHomeType.cpp Lang_Dlg_Debug.cpp RegApp.cpp _a_rect30.cpp _Dlg2ImageArith.cpp _DlgBlobSetting.cpp _DlgDebug.cpp _DlgImage.cpp _DlgInputBox.cpp _DlgSetting.cpp _DlgThresholdSet.cpp _ImageView.cpp SdkExt.cpp SdkExtDll.cpp
         SdkExtDll.cpp
         SdkExt.cpp
         _ImageView.cpp
         _DlgThresholdSet.cpp
         _DlgSetting.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(70): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(80): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(90): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(99): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(107): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(115): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(146): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(181): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(196): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(216): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(235): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgsetting.cpp(256): warning C4018: “<”: 有符号/无符号不匹配
         _DlgInputBox.cpp
         _DlgImage.cpp
         _DlgDebug.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(673): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(704): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(721): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(738): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(1173): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(1963): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(1979): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(1993): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(3013): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(3082): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(3184): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(3213): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\_dlgdebug.cpp(3834): warning C4018: “<”: 有符号/无符号不匹配
         _DlgBlobSetting.cpp
         _Dlg2ImageArith.cpp
         _a_rect30.cpp
         RegApp.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(31): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(43): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(53): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(60): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(92): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\regapp.cpp(104): warning C4996: 'sprintf': This function or variable may be unsafe. Consider using sprintf_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdio.h(371) : 参见“sprintf”的声明
         Lang_Dlg_Debug.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\lang_dlg_debug.cpp(34): warning C4996: '_splitpath': This function or variable may be unsafe. Consider using _splitpath_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\stdlib.h(791) : 参见“_splitpath”的声明
         DlgHomeType.cpp
         DlgControl.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgcontrol.cpp(268): warning C4101: “i”: 未引用的局部变量
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgcontrol.cpp(329): warning C4101: “pflags”: 未引用的局部变量
         DlgConditionOutput.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(223): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(244): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(265): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(284): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(312): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(350): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(388): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionoutput.cpp(422): warning C4018: “<”: 有符号/无符号不匹配
         DlgConditionMotor.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(85): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(106): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(127): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(146): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(172): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(210): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(248): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\sdkextdll\dlgconditionmotor.cpp(282): warning C4018: “<”: 有符号/无符号不匹配
         _ListCtrl.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\listctrl\_listctrl.cpp(1123): warning C4996: 'strcpy': This function or variable may be unsafe. Consider using strcpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(105) : 参见“strcpy”的声明
         ShadeButtonST.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\shadebuttonst.cpp(155): warning C4244: “=”: 从“long”转换到“BYTE”，可能丢失数据
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\shadebuttonst.cpp(156): warning C4244: “=”: 从“long”转换到“BYTE”，可能丢失数据
         CeXDib.cpp
         正在生成代码...
         正在编译...
         BtnST.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\btnst.cpp(14): warning C4005: “BS_TYPEMASK”: 宏重定义
                 c:\program files (x86)\microsoft sdks\windows\v7.0a\include\winuser.h(9459) : 参见“BS_TYPEMASK”的前一个定义
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\btnst.cpp(1684): warning C4996: '_tcsncpy': This function or variable may be unsafe. Consider using _tcsncpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\tchar.h(1516) : 参见“_tcsncpy”的声明
         BCMenu.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\bcmenu.cpp(210): warning C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(283) : 参见“wcscpy”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\bcmenu.cpp(1667): warning C4996: 'wcscpy': This function or variable may be unsafe. Consider using wcscpy_s instead. To disable deprecation, use _CRT_SECURE_NO_WARNINGS. See online help for details.
                 c:\program files (x86)\microsoft visual studio 10.0\vc\include\string.h(283) : 参见“wcscpy”的声明
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\commonclass\buttonst\bcmenu.cpp(1949): warning C4018: “<”: 有符号/无符号不匹配
         正在生成代码...
       ResourceCompile:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\rc.exe /D _DEBUG /D _VC80_UPGRADE=0x0600 /D _AFXDLL /l"0x0804" /nologo /fo".\..\..\temp\sdkExtDll\Debug\SdkExtDll.res" SdkExtDll.rc 
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:"../EXE/SdkExtDll.dll" /INCREMENTAL /NOLOGO /DEF:".\SdkExtDll.def" /MANIFEST /ManifestFile:".\..\..\temp\sdkExtDll\Debug\SdkExtDll.dll.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:".\..\..\temp\SdkExtDll.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"../EXE/SdkExtDll.lib" /MACHINE:X86 /DLL .\..\..\temp\sdkExtDll\Debug\SdkExtDll.res
         .\..\..\temp\sdkExtDll\Debug\BCMenu.obj
         .\..\..\temp\sdkExtDll\Debug\BtnST.obj
         .\..\..\temp\sdkExtDll\Debug\CeXDib.obj
         .\..\..\temp\sdkExtDll\Debug\ShadeButtonST.obj
         .\..\..\temp\sdkExtDll\Debug\_ListCtrl.obj
         .\..\..\temp\sdkExtDll\Debug\DlgConditionMotor.obj
         .\..\..\temp\sdkExtDll\Debug\DlgConditionOutput.obj
         .\..\..\temp\sdkExtDll\Debug\DlgControl.obj
         .\..\..\temp\sdkExtDll\Debug\DlgHomeType.obj
         .\..\..\temp\sdkExtDll\Debug\Lang_Dlg_Debug.obj
         .\..\..\temp\sdkExtDll\Debug\RegApp.obj
         .\..\..\temp\sdkExtDll\Debug\_a_rect30.obj
         .\..\..\temp\sdkExtDll\Debug\_Dlg2ImageArith.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgBlobSetting.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgDebug.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgImage.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgInputBox.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgSetting.obj
         .\..\..\temp\sdkExtDll\Debug\_DlgThresholdSet.obj
         .\..\..\temp\sdkExtDll\Debug\_ImageView.obj
         .\..\..\temp\sdkExtDll\Debug\SdkExt.obj
         .\..\..\temp\sdkExtDll\Debug\SdkExtDll.obj
         .\..\..\temp\sdkExtDll\Debug\StdAfx.obj
     1>.\SdkExtDll.def(4): warning LNK4017: DESCRIPTION 语句不支持目标平台；已忽略
            正在创建库 ../EXE/SdkExtDll.lib 和对象 ../EXE/SdkExtDll.exp
         SdkExtDll.vcxproj -> D:\ASUS\Desktop\科瑞2\hermetic\hermetic\SdkExtDll\.\..\exe\SdkExtDll.dll
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:"D:\ASUS\Desktop\科瑞2\hermetic\hermetic\SdkExtDll\.\..\exe\SdkExtDll.dll.manifest" /manifest .\..\..\temp\sdkExtDll\Debug\SdkExtDll.dll.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
       FinalizeBuildStatus:
         正在删除文件“.\..\..\temp\sdkExtDll\Debug\SdkExtDll.unsuccessfulbuild”。
         正在对“.\..\..\temp\sdkExtDll\Debug\SdkExtDll.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\SdkExtDll\SdkExtDll.vcxproj”(rebuild 个目标)的操作。

生成成功。

已用时间 00:00:11.00
