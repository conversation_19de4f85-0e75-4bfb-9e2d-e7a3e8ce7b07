// Machine generated IDispatch wrapper class(es) created by Microsoft Visual C++

// NOTE: Do not modify the contents of this file.  If this class is regenerated by
//  Microsoft Visual C++, your modifications will be overwritten.


#include "stdafx.h"
#include "_a_rect30.h"

/////////////////////////////////////////////////////////////////////////////
// C_A_Rect30

IMPLEMENT_DYNCREATE(C_A_Rect30, CWnd)

/////////////////////////////////////////////////////////////////////////////
// C_A_Rect30 properties

/////////////////////////////////////////////////////////////////////////////
// C_A_Rect30 operations

void C_A_Rect30::InitPos(short OffsetX, short OffsetY, short Width, short Height)
{
	static BYTE parms[] =
		VTS_I2 VTS_I2 VTS_I2 VTS_I2;
	InvokeHelper(0x60030011, DISPATCH_METHOD, VT_EMPTY, NULL, parms,
		 OffsetX, OffsetY, Width, Height);
}

void C_A_Rect30::Refresh()
{
	InvokeHelper(0x60030013, DISPATCH_METHOD, VT_EMPTY, NULL, NULL);
}

BOOL C_A_Rect30::GetEnabled(short Index)
{
	BOOL result;
	static BYTE parms[] =
		VTS_I2;
	InvokeHelper(0x68030010, DISPATCH_PROPERTYGET, VT_BOOL, (void*)&result, parms,
		Index);
	return result;
}

void C_A_Rect30::SetEnabled(short Index, BOOL bNewValue)
{
	static BYTE parms[] =
		VTS_I2 VTS_BOOL;
	InvokeHelper(0x68030010, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 Index, bNewValue);
}

LPDISPATCH C_A_Rect30::GetFont()
{
	LPDISPATCH result;
	InvokeHelper(0x6803000f, DISPATCH_PROPERTYGET, VT_DISPATCH, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetRefFont(LPDISPATCH newValue)
{
	static BYTE parms[] =
		VTS_DISPATCH;
	InvokeHelper(0x6803000f, DISPATCH_PROPERTYPUTREF, VT_EMPTY, NULL, parms,
		 newValue);
}

unsigned long C_A_Rect30::GetCenLineColor()
{
	unsigned long result;
	InvokeHelper(0x6803000e, DISPATCH_PROPERTYGET, VT_I4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetCenLineColor(unsigned long newValue)
{
	static BYTE parms[] =
		VTS_I4;
	InvokeHelper(0x6803000e, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

short C_A_Rect30::GetCenLineWidth()
{
	short result;
	InvokeHelper(0x6803000d, DISPATCH_PROPERTYGET, VT_I2, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetCenLineWidth(short nNewValue)
{
	static BYTE parms[] =
		VTS_I2;
	InvokeHelper(0x6803000d, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 nNewValue);
}

unsigned long C_A_Rect30::GetObjLineColor()
{
	unsigned long result;
	InvokeHelper(0x6803000c, DISPATCH_PROPERTYGET, VT_I4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetObjLineColor(unsigned long newValue)
{
	static BYTE parms[] =
		VTS_I4;
	InvokeHelper(0x6803000c, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

short C_A_Rect30::GetObjLineWidth()
{
	short result;
	InvokeHelper(0x6803000b, DISPATCH_PROPERTYGET, VT_I2, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetObjLineWidth(short nNewValue)
{
	static BYTE parms[] =
		VTS_I2;
	InvokeHelper(0x6803000b, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 nNewValue);
}

unsigned long C_A_Rect30::GetFontColor()
{
	unsigned long result;
	InvokeHelper(0x6803000a, DISPATCH_PROPERTYGET, VT_I4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetFontColor(unsigned long newValue)
{
	static BYTE parms[] =
		VTS_I4;
	InvokeHelper(0x6803000a, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

unsigned long C_A_Rect30::GetBackColor()
{
	unsigned long result;
	InvokeHelper(0x68030009, DISPATCH_PROPERTYGET, VT_I4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetBackColor(unsigned long newValue)
{
	static BYTE parms[] =
		VTS_I4;
	InvokeHelper(0x68030009, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

void C_A_Rect30::UserControl_LostFocus()
{
	InvokeHelper(0x60030022, DISPATCH_METHOD, VT_EMPTY, NULL, NULL);
}

CString C_A_Rect30::GetCaption()
{
	CString result;
	InvokeHelper(0x68030008, DISPATCH_PROPERTYGET, VT_BSTR, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetCaption(LPCTSTR lpszNewValue)
{
	static BYTE parms[] =
		VTS_BSTR;
	InvokeHelper(0x68030008, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 lpszNewValue);
}

short C_A_Rect30::GetMarkSize()
{
	short result;
	InvokeHelper(0x68030007, DISPATCH_PROPERTYGET, VT_I2, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetMarkSize(short nNewValue)
{
	static BYTE parms[] =
		VTS_I2;
	InvokeHelper(0x68030007, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 nNewValue);
}

float C_A_Rect30::GetX1()
{
	float result;
	InvokeHelper(0x68030006, DISPATCH_PROPERTYGET, VT_R4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetX1(float newValue)
{
	static BYTE parms[] =
		VTS_R4;
	InvokeHelper(0x68030006, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

float C_A_Rect30::GetX2()
{
	float result;
	InvokeHelper(0x68030005, DISPATCH_PROPERTYGET, VT_R4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetX2(float newValue)
{
	static BYTE parms[] =
		VTS_R4;
	InvokeHelper(0x68030005, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

float C_A_Rect30::GetY1()
{
	float result;
	InvokeHelper(0x68030004, DISPATCH_PROPERTYGET, VT_R4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetY1(float newValue)
{
	static BYTE parms[] =
		VTS_R4;
	InvokeHelper(0x68030004, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

float C_A_Rect30::GetY2()
{
	float result;
	InvokeHelper(0x68030003, DISPATCH_PROPERTYGET, VT_R4, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetY2(float newValue)
{
	static BYTE parms[] =
		VTS_R4;
	InvokeHelper(0x68030003, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 newValue);
}

BOOL C_A_Rect30::GetIsObjVisible(short Index)
{
	BOOL result;
	static BYTE parms[] =
		VTS_I2;
	InvokeHelper(0x68030002, DISPATCH_PROPERTYGET, VT_BOOL, (void*)&result, parms,
		Index);
	return result;
}

void C_A_Rect30::SetIsObjVisible(short Index, BOOL bNewValue)
{
	static BYTE parms[] =
		VTS_I2 VTS_BOOL;
	InvokeHelper(0x68030002, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 Index, bNewValue);
}

BOOL C_A_Rect30::GetIsCenVisible()
{
	BOOL result;
	InvokeHelper(0x68030001, DISPATCH_PROPERTYGET, VT_BOOL, (void*)&result, NULL);
	return result;
}

void C_A_Rect30::SetIsCenVisible(BOOL bNewValue)
{
	static BYTE parms[] =
		VTS_BOOL;
	InvokeHelper(0x68030001, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 bNewValue);
}

void C_A_Rect30::SetIsVisible(BOOL bNewValue)
{
	static BYTE parms[] =
		VTS_BOOL;
	InvokeHelper(0x68030000, DISPATCH_PROPERTYPUT, VT_EMPTY, NULL, parms,
		 bNewValue);
}
