﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Template|Win32">
      <Configuration>Template</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Template|x64">
      <Configuration>Template</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <SccProjectName />
    <SccLocalPath />
    <Keyword>MFCProj</Keyword>
    <ProjectGuid>{75CA42E3-47C3-681F-A043-09763020334C}</ProjectGuid>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Template|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Template|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v100</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <UseOfMfc>Dynamic</UseOfMfc>
    <CharacterSet>MultiByte</CharacterSet>
    <CLRSupport>false</CLRSupport>
    <PlatformToolset>v110</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Template|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Template|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
    <Import Project="$(VCTargetsPath)Microsoft.Cpp.UpgradeFromVC60.props" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>.\..\exe\</OutDir>
    <IntDir>.\..\..\temp\sdkExtDll\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <EmbedManifest>false</EmbedManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <OutDir>.\..\exe</OutDir>
    <IntDir>.\..\..\temp\sdkExtDll\Debug\</IntDir>
    <LinkIncremental>true</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>.\..\exe</OutDir>
    <IntDir>.\..\..\temp\sdkExtDll\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <OutDir>.\..\exe</OutDir>
    <IntDir>.\..\..\temp\sdkExtDll\Release\</IntDir>
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <MinimalRebuild>true</MinimalRebuild>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_WINDLL;_AFXEXT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\..\..\temp\sdkExtDll\Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\..\..\temp\sdkExtDll\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\temp\sdkExtDll\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OutputFile>../EXE/SdkExtDll.dll</OutputFile>
      <ImportLibrary>../EXE/SdkExtDll.lib</ImportLibrary>
      <ModuleDefinitionFile>.\SdkExtDll.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>.\..\..\temp\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <InlineFunctionExpansion>Default</InlineFunctionExpansion>
      <FunctionLevelLinking>false</FunctionLevelLinking>
      <Optimization>Disabled</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_WINDLL;_AFXEXT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\..\..\temp\sdkExtDll\Debug\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\..\..\temp\sdkExtDll\Debug\</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\temp\sdkExtDll\Debug\</ProgramDataBaseFileName>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\temp\sdkExtDll\Debug\SdkExtDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <OutputFile>../EXE/SdkExtDll.dll</OutputFile>
      <ImportLibrary>../EXE/SdkExtDll.lib</ImportLibrary>
      <ModuleDefinitionFile>.\SdkExtDll.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>.\..\..\temp\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_WINDLL;_AFXEXT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\..\..\temp\sdkExtDll\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\..\..\temp\sdkExtDll\Release\SdkExtDll.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\..\..\temp\sdkExtDll\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\temp\sdkExtDll\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\..\..\temp\sdkExtDll\Release\SdkExtDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
      <TargetEnvironment>Win32</TargetEnvironment>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\temp\sdkExtDll\Release\SdkExtDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Windows</SubSystem>
      <OutputFile>../EXE/SdkExtDll.dll</OutputFile>
      <ImportLibrary>../EXE/SdkExtDll.lib</ImportLibrary>
      <ModuleDefinitionFile>.\SdkExtDll.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>.\..\..\temp\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <StringPooling>true</StringPooling>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <Optimization>MaxSpeed</Optimization>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_WINDLL;_AFXEXT;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AssemblerListingLocation>.\..\..\temp\sdkExtDll\Release\</AssemblerListingLocation>
      <PrecompiledHeaderOutputFile>.\..\..\temp\sdkExtDll\Release\SdkExtDll.pch</PrecompiledHeaderOutputFile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>stdafx.h</PrecompiledHeaderFile>
      <ObjectFileName>.\..\..\temp\sdkExtDll\Release\</ObjectFileName>
      <ProgramDataBaseFileName>.\..\..\temp\sdkExtDll\Release\</ProgramDataBaseFileName>
    </ClCompile>
    <Midl>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <TypeLibraryName>.\..\..\temp\sdkExtDll\Release\SdkExtDll.tlb</TypeLibraryName>
      <MkTypLibCompatible>true</MkTypLibCompatible>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
    <Bscmake>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <OutputFile>.\..\..\temp\sdkExtDll\Release\SdkExtDll.bsc</OutputFile>
    </Bscmake>
    <Link>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <LinkDLL>true</LinkDLL>
      <SubSystem>Windows</SubSystem>
      <OutputFile>../EXE/SdkExtDll.dll</OutputFile>
      <ImportLibrary>../EXE/SdkExtDll.lib</ImportLibrary>
      <ModuleDefinitionFile>.\SdkExtDll.def</ModuleDefinitionFile>
      <ProgramDatabaseFile>.\..\..\temp\$(TargetName).pdb</ProgramDatabaseFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\CommonClass\ButtonST\BCMenu.cpp" />
    <ClCompile Include="..\CommonClass\ButtonST\BtnST.cpp" />
    <ClCompile Include="..\CommonClass\ButtonST\CeXDib.cpp" />
    <ClCompile Include="..\CommonClass\ButtonST\ShadeButtonST.cpp" />
    <ClCompile Include="..\CommonClass\ListCtrl\_ListCtrl.cpp" />
    <ClCompile Include="DlgConditionMotor.cpp" />
    <ClCompile Include="DlgConditionOutput.cpp" />
    <ClCompile Include="DlgControl.cpp" />
    <ClCompile Include="DlgHomeType.cpp" />
    <ClCompile Include="Lang_Dlg_Debug.cpp" />
    <ClCompile Include="RegApp.cpp" />
    <ClCompile Include="_a_rect30.cpp" />
    <ClCompile Include="_Dlg2ImageArith.cpp" />
    <ClCompile Include="_DlgBlobSetting.cpp" />
    <ClCompile Include="_DlgDebug.cpp" />
    <ClCompile Include="_DlgImage.cpp" />
    <ClCompile Include="_DlgInputBox.cpp" />
    <ClCompile Include="_DlgSetting.cpp" />
    <ClCompile Include="_DlgThresholdSet.cpp" />
    <ClCompile Include="_ImageView.cpp" />
    <ClCompile Include="SdkExt.cpp" />
    <ClCompile Include="SdkExtDll.cpp" />
    <ClCompile Include="StdAfx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">stdafx.h</PrecompiledHeaderFile>
      <PrecompiledHeaderFile Condition="'$(Configuration)|$(Platform)'=='Release|x64'">stdafx.h</PrecompiledHeaderFile>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="SdkExtDll.def" />
    <CustomBuild Include="res\folder open.ico" />
    <CustomBuild Include="res\folder.ico" />
    <CustomBuild Include="res\Hand.cur" />
    <CustomBuild Include="res\icon_mcl.ico" />
    <CustomBuild Include="res\icon_mop.ico" />
    <CustomBuild Include="res\LedOff.ico" />
    <CustomBuild Include="res\LedOn.ico" />
    <CustomBuild Include="res\SdkExtDll.rc2">
      <FileType>RC</FileType>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarEdit.bmp" />
    <CustomBuild Include="res\ToolbarEditDisabled.bmp" />
    <CustomBuild Include="res\ToolbarEditHot.bmp" />
    <CustomBuild Include="res\ToolbarSys.bmp" />
    <CustomBuild Include="res\ToolbarSysDisabled.bmp" />
    <CustomBuild Include="res\ToolbarSysHot.bmp" />
    <CustomBuild Include="res\ToolbarUser.bmp" />
    <CustomBuild Include="res\ToolbarUserDisabled.bmp" />
    <CustomBuild Include="res\ToolbarUserHot.bmp" />
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="SdkExtDll.rc" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\CommonClass\ButtonST\BCMenu.h" />
    <ClInclude Include="..\CommonClass\ButtonST\BtnST.h" />
    <ClInclude Include="..\CommonClass\ButtonST\CeXDib.h" />
    <ClInclude Include="..\CommonClass\ButtonST\ShadeButtonST.h" />
    <ClInclude Include="..\CommonClass\ListCtrl\_ListCtrl.h" />
    <ClInclude Include="..\SdkExtDll_include\SdkExt.h" />
    <ClInclude Include="DlgConditionMotor.h" />
    <ClInclude Include="DlgConditionOutput.h" />
    <ClInclude Include="DlgControl.h" />
    <ClInclude Include="DlgHomeType.h" />
    <ClInclude Include="Lang_Def.h" />
    <ClInclude Include="Lang_Dlg_Debug.h" />
    <ClInclude Include="RegApp.h" />
    <ClInclude Include="_a_rect30.h" />
    <ClInclude Include="_Dlg2ImageArith.h" />
    <ClInclude Include="_DlgBlobSetting.h" />
    <ClInclude Include="_DlgDebug.h" />
    <ClInclude Include="_DlgImage.h" />
    <ClInclude Include="_DlgInputBox.h" />
    <ClInclude Include="_DlgSetting.h" />
    <ClInclude Include="_DlgThresholdSet.h" />
    <ClInclude Include="_ImageView.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="StdAfx.h" />
  </ItemGroup>
  <ItemGroup>
    <None Include="res\Control.ico" />
    <None Include="res\ico00001.ico" />
    <None Include="res\icon_led.ico" />
    <None Include="res\MotorOff.ico" />
    <None Include="res\MotorOn.ico" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties RESOURCE_FILE="SdkExtDll.rc" />
    </VisualStudio>
  </ProjectExtensions>
</Project>