#if !defined(AFX__DLGINPUTBOX_H__D9A7CE91_65AD_42BD_88A6_26D103023C0F__INCLUDED_)
#define AFX__DLGINPUTBOX_H__D9A7CE91_65AD_42BD_88A6_26D103023C0F__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _DlgInputBox.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_DlgInputBox dialog
#include "Resource.h"

// #if _USRDLL
// #define _DLGINPUTBOX_DLL __declspec(dllexport) 
// #else
// #define _DLGINPUTBOX_DLL __declspec(dllimport) 
// #endif

class AFX_EXT_CLASS C_DlgInputBox : public CDialog
{
// Construction
public:
	CString m_strWindowText;
	CString m_strPrompt;
//	CString m_strInput;
	
	CString GetInput();
//	void SetInput(CString str);
//	void SetPrompt(CString str);		
//	void SetWindowCaption(CString str);		
	C_DlgInputBox(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(C_DlgInputBox)
	enum { IDD = IDD_DLG_INPUTBOX };
	CString	m_strInput;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_DlgInputBox)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_DlgInputBox)
	virtual BOOL OnInitDialog();
	afx_msg void OnShowWindow(BOOL bShow, UINT nStatus);
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
//	CString m_strWindowText;
//	CString m_strPrompt;
//	CString m_strInput;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLGINPUTBOX_H__D9A7CE91_65AD_42BD_88A6_26D103023C0F__INCLUDED_)
