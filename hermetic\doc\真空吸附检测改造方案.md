# 真空吸附检测功能改造方案

## 1. 功能概述

为提升生产安全，防止因产品未及时取出或气路堵塞导致的问题，本次改造为设备集成了强制性的“无料检测”功能。该功能在核心生产环节（复位、启动）前自动检查治具状态，确保其已清空。

**关键硬件限制**：功能的实现完全基于真空发生器压力开关的**数字I/O信号**，而非读取具体的压力数值。

## 2. 功能使用说明

### 2.1 如何开启/关闭功能

您可以通过修改配置文件来灵活控制此功能的启用状态。

1. **找到配置文件**：打开路径 `D:\ADE_All_Type_Config\Database\MachinePara.ini`。
2. **修改配置项**：在文件中的 `[System]` 段落，找到或添加以下配置行：
   ```ini
   [System]
   EnableNoMaterialCheck=1  ; 1=启用无料检测, 0=禁用
   ForceRebootAfter24H=1    ; 1=启用24小时强制重启, 0=禁用
   ```

### 2.2 自动检测流程

当 `EnableNoMaterialCheck=1` 时：

* 在操作员点击**"复位"**或**"启动"**按钮后，设备将首先进入"无料检测"状态。
* **检测通过**：如果治具上无产品且气路通畅，检测通过，设备正常执行复位或启动流程。
* **检测失败**：如果检测到治具上有残留产品或气路被堵塞，设备将**立即报警（红灯闪烁）**并中断当前操作。此时，必须再次点击"复位"按钮，在排除异常后才能继续。

### 2.3 24小时强制重启

当 `ForceRebootAfter24H=1` 时：

* 为了保证软件长时间运行的稳定性，当程序连续运行时间超过24小时，系统会自动弹出一个提示框，要求操作员重启软件。确认后，软件将自动关闭。

## 3. 实现细节 (技术参考)

### 3.1 核心类与文件

* **配置文件**：`D:\ADE_All_Type_Config\Database\MachinePara.ini`
* **主程序类**：`ChermeticFrameApp` (位于 `hermeticFrame.h`, `hermeticFrame.cpp`)
  * 新增 `m_bEnableNoMaterialCheck`, `m_bForceReboot` 成员变量，在启动时读取配置。
* **流程控制类**：`CFlowProcess` (位于 `ProFlow.h`, `ProFlow.cpp`)
  * 是所有流程改动的核心。
* **主对话框类**：`ChermeticFrameDlg` (位于 `hermeticFrameDlg.h`, `hermeticFrameDlg.cpp`)
  * 负责UI交互和定时器。

### 3.2 关键函数与逻辑

* **检测核心函数**：`bool CFlowProcess::NoMaterialCheck_Process()`
  * 此函数封装了完整的检测逻辑。它通过调用 `HolderZhengKong(eStation, true)` 来打开真空，延时500ms后，通过 `theApp.m_pCtrl->ReadInPort(...)` 读取名为 `eIn_Vacuo` 的输入信号。
  * 如果读取到信号，则判定为异常并返回 `false`。
* **流程入口修改**：
  * `CFlowProcess::Reset()` 和 `CFlowProcess::Run()` 两个函数现在会在执行其原有逻辑前，先调用 `NoMaterialCheck_Process()` 进行前置检查。如果检查失败，则中断后续流程。
* **UI交互实现**：
  * `ChermeticFrameDlg::OnTimer()`：在ID为 `1` 的定时器事件中，增加了对程序运行时间的判断，以实现24小时强制重启功能。
