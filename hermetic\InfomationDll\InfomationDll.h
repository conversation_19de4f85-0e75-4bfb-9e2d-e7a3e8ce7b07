// InfomationDll.h : main header file for the INFOMATIONDLL DLL
//

#if !defined(AFX_INFOMATIONDLL_H__5AD1C424_7083_4F76_9512_089F6ED236E9__INCLUDED_)
#define AFX_INFOMATIONDLL_H__5AD1C424_7083_4F76_9512_089F6ED236E9__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// CInfomationDllApp
// See InfomationDll.cpp for the implementation of this class
//

class CInfomationDllApp : public CWinApp
{
public:
	CInfomationDllApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CInfomationDllApp)
	//}}AFX_VIRTUAL

	//{{AFX_MSG(CInfomationDllApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_INFOMATIONDLL_H__5AD1C424_7083_4F76_9512_089F6ED236E9__INCLUDED_)
