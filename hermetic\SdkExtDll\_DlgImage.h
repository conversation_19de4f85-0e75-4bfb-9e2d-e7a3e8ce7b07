#if !defined(AFX__DLGIMAGE_H__D4D061D8_9664_4942_B9F8_3F9C019DEE74__INCLUDED_)
#define AFX__DLGIMAGE_H__D4D061D8_9664_4942_B9F8_3F9C019DEE74__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _DlgImage.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_DlgImage dialog
#include "_ImageView.h"
#include "resource.h"//2012.10.19

class C_DlgImage : public CDialog
{
// Construction
public:
	void SetDisplayBuffer(long MilBuffer,long MilDisplay,double nFactor, CWnd *pWndDisplay);
	long nSizeX,nSizeY;
	CStatic* CreateLabel(CWnd* pParentWnd, int nID, int OffSetX,int OffSetY,int Width,int Height, int nStyle,CString WindowText);	
	CStatic *pLblPicture;
	C_ImageView  * m_pView;
	
	C_DlgImage(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(C_DlgImage)
	enum { IDD = IDD_DLG_IMAGE };
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_DlgImage)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_DlgImage)
	virtual BOOL OnInitDialog();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnDestroy();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	CFrameWnd * m_pFrame ;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLGIMAGE_H__D4D061D8_9664_4942_B9F8_3F9C019DEE74__INCLUDED_)
