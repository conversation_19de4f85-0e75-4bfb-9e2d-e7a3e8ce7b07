// QProgram.cpp : implementation file
//

#include "stdafx.h"
#include "ModbusRTU_CPP.h"
#include "QProgram.h"
#include "afxdialogex.h"


// CQProgram dialog

IMPLEMENT_DYNAMIC(CQProgram, CDialog)

CQProgram::CQProgram(CWnd* pParent /*=NULL*/)
	: CDialog(CQProgram::IDD, pParent)
{

	m_txt_QProgram_Segment = 1;
}

CQProgram::~CQProgram()
{
}

void CQProgram::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Text(pDX, IDC_EDIT_QPROGRAM_SEGMENT, m_txt_QProgram_Segment);
	DDV_MinMaxInt(pDX, m_txt_QProgram_Segment, 1, 12);
}


BEGIN_MESSAGE_MAP(CQProgram, CDialog)
	ON_BN_CLICKED(IDC_BTN_QPROGRAM_START, &CQProgram::OnClickedBtnQprogramStart)
	ON_BN_CLICKED(IDC_BTN_QPROGRAM_STOP, &CQProgram::OnClickedBtnQprogramStop)
END_MESSAGE_MAP()


// CQProgram message handlers

void CQProgram::SetButtonEnable(int nID, BOOL bEnable)
{
	((CButton *)GetDlgItem(nID))->EnableWindow(bEnable);
}

void CQProgram:: SetQProgramSegment(int nSegment)
{
	m_txt_QProgram_Segment = nSegment;
	UpdateData(TRUE);
}
int CQProgram::GetQProgramSegment()
{
	UpdateData(FALSE);
	return m_txt_QProgram_Segment;
}

void CQProgram::OnClickedBtnQprogramStart()
{
	// TODO: Add your control notification handler code here
	int id = IDC_BTN_QPROGRAM_START;
	::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONCLICK_MESSAGE, 0, (LPARAM)&id);
}


void CQProgram::OnClickedBtnQprogramStop()
{
	// TODO: Add your control notification handler code here
	int id = IDC_BTN_QPROGRAM_STOP;
	::SendMessage(this->GetParent()->GetParent()->m_hWnd, WM_BUTTONCLICK_MESSAGE, 0, (LPARAM)&id);
}


void CQProgram::OnOK()
{
	// TODO: Add your specialized code here and/or call the base class

	//CDialog::OnOK();
}


void CQProgram::OnCancel()
{
	// TODO: Add your specialized code here and/or call the base class

	//CDialog::OnCancel();
}
