#pragma once

class CRegApp
{
public:
	CRegApp(void);
	~CRegApp(void);
	double	GetProfileDouble(TCHAR* pstrSec, TCHAR* pstrKey, double dbDefault);
	void	SetProfileDouble(TCHAR* pstrSec, TCHAR* pstrKey, double dbDes);
	int		GetProfileInt(TCHAR* pstrSec, TCHAR* pstrKey, int nDefault);
	void	SetProfileInt(TCHAR* pstrSec, TCHAR* pstrKey, int nDes);
	CString GetProfileString(TCHAR* pstrSec, TCHAR* pstrKey, CString strDefault);
	void	SetProfileString(TCHAR* pstrSec, TCHAR* pstrKey, CString strDes);
    void	SetProfileString(TCHAR* pstrSec, TCHAR* pstrKey, LPCTSTR strDes);
	double	PluseToMm(int nAxis, double dbPulse);
	double	MmToPulse(int nAxis, double dbMm);
	BOOL	SetFileName(CString filenamePath, BOOL bModuleFile =TRUE);
	CString m_strFileName;
};

extern CRegApp g_RegApp;

#define G_DB	g_RegApp.GetProfileDouble
#define G_INT	g_RegApp.GetProfileInt
#define G_STR	g_RegApp.GetProfileString
#define S_DB	g_RegApp.SetProfileDouble
#define S_INT	g_RegApp.SetProfileInt
#define S_STR	g_RegApp.SetProfileString

#define PULSETOMM	g_RegApp.PluseToMm
#define MMTOPULSE	g_RegApp.MmToPulse