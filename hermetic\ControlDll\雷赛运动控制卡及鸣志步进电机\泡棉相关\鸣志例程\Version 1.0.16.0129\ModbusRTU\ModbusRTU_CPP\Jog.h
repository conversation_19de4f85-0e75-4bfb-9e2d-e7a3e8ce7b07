#pragma once


// CJog dialog

class CJog : public CDialog
{
	DECLARE_DYNAMIC(CJog)

public:
	CJog(CWnd* pParent = NULL);   // standard constructor
	virtual ~CJog();

// Dialog Data
	enum { IDD = IDD_DIALOG_JOG };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support

	DECLARE_MESSAGE_MAP()
public:
	void SetButtonEnable(int nID, BOOL bEnable);
	double GetJogSpeed();
	double GetJogAccel();
	double GetJogDecel();
	afx_msg void OnLButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	virtual BOOL PreTranslateMessage(MSG* pMsg);
	double m_nJogSpeed;
	double m_nJogAccel;
	double m_nJogDecel;
	afx_msg void OnEnKillfocusEditJogSpeed();
	afx_msg void OnChangeEditJogSpeed();
	virtual void OnOK();
	virtual void OnCancel();
};
