{"C_Cpp.default.intelliSenseMode": "msvc-x86", "C_Cpp.default.cppStandard": "c++98", "C_Cpp.default.cStandard": "c89", "files.autoGuessEncoding": true, "files.encoding": "gbk", "files.associations": {"*.h": "cpp", "*.cpp": "cpp", "*.c": "c", "*.hpp": "cpp"}, "C_Cpp.default.defines": ["_AFXDLL", "WIN32", "_WINDOWS", "_DEBUG", "_UNICODE", "UNICODE"], "C_Cpp.enhancedColorization": "Enabled", "C_Cpp.intelliSenseEngine": "<PERSON><PERSON><PERSON>", "C_Cpp.errorSquiggles": "Enabled", "C_Cpp.autocomplete": "<PERSON><PERSON><PERSON>", "editor.formatOnSave": false, "editor.formatOnPaste": false, "editor.formatOnType": false, "editor.tabSize": 4, "editor.insertSpaces": false, "search.exclude": {"**/ipch/**": true, "**/Release/**": true, "**/Debug/**": true, "**/*.sdf": true, "**/*.opensdf": true, "**/*.suo": true, "**/temp/**": true}, "files.exclude": {"**/ipch/**": true, "**/*.sdf": true, "**/*.opensdf": true, "**/*.suo": true, "**/temp/**": true}}