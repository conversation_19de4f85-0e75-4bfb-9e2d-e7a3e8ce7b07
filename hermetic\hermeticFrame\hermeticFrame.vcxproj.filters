﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="_ListCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AppGeneral.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AutoCal.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="AxisState.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BCMenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="BtnST.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CeXDib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartAxis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartAxisLabel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartBarSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartCandlestickSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartCrossHairCursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartCursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartDateTimeAxis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartDragLineCursor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartFont.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartGanttSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartGradient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartGrid.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartLegend.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartLineSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartLogarithmicAxis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartPointsSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartScrollBar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartStandardAxis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartSurfaceSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartXYSerie.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\ChartCtrl\ChartTitle.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="CountDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Dlg_MES_SMT.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgPageResult.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgPar.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F28Light.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FileOperator.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlowLeakDetector.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlowCOM.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="FlowMotion.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="GradientStatic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="HeaderCtrlCl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hermeticFrame.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="hermeticFrameDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Lang_Motion.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Language.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Language_Dlg_Main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Language_Dlg_Para.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Language_Flow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ListCtrlCl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="LogRec.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="markup.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MesLogin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MixedLineConfirmDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="MixedLineManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="NameBaseDef.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="OPPO_MES.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ProFlow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RegApp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ReportCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SerialCom.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SerialPort.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ShadeButtonST.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SysCfg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TotalATEQDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="VCF28LightControlDemoDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="TCPSocket.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RobotTCP.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\json\jsoncpp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="_ListCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="VCF28LightControlDemoDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AppGeneral.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AutoCal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="AxisState.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BCMenu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="BtnST.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CeXDib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartAxis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartAxisLabel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartBalloonLabel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartBarSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartCandlestickSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartCrossHairCursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartCursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartDateTimeAxis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartDragLineCursor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartFont.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartGanttSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartGradient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartGrid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartLabel.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartLegend.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartLineSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartLogarithmicAxis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartMouseListener.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartPointsArray.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartPointsSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartScrollBar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartSerieBase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartSeriesMouseListener.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartStandardAxis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartString.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartSurfaceSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartTitle.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\ChartXYSerie.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="comm.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="CountDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Dlg_MES_SMT.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgPageResult.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgPar.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F28Light.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\F28LightControl_Eth\Include\F28LightControl_ETH.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FileOperator.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlowCOM.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlowLeakDetector.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlowMotion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="FlowUniversalDefine.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="GradientStatic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="HeaderCtrlCl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hermeticFrame.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="hermeticFrameDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lang_Motion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Language.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Language_Dlg_Main.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Language_Dlg_Para.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Language_Flow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Language_Universal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ListCtrlCl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LogRec.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="LTDMC.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MacroMotion.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="markup.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MesLogin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MixedLineConfirmDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="MixedLineManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="NameBaseDef.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="OPPO_MES.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\ChartCtrl\PointsOrdering.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ProFlow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RegApp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ReportCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SENTENCE.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SerialCom.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SerialPort.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ShadeButtonST.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="SysCfg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TotalATEQDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="TCPSocket.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RobotTCP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <None Include="res\1.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\9.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\11.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\11.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\12.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\13.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\14.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\14.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\14.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\15.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\15.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\15.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\16.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\16.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\17.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\17.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\17.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\18.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\19.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\2.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\2.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\20.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\21.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\22.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\3.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\3.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\4.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\4.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\5.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\5.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\6.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\6.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\7.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\7.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\8.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\8.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\9.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\bitmap1.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\bitmap1.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\bitmap3.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\bitmap3.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Config.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Control.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Config.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Doc.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Doc.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Exit.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Exit.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\ico00001.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\icon1.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\icon2.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\LedOff.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\LedOn.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Login.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Login.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Manger.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Manger.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\MotorOff.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\MotorOn.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\notrecode.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\recode.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\reset.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\reset.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\Run.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Run.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Set.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Set.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Start.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Start.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Stop (2).ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Stop.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Stop.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Toolbar.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Toolbar.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\toolbar1.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Toolbar256.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\Toolbar256.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\toolbar256disable.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\toolbar256disable.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\toolbar256Hot.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\toolbar256Hot.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\UnLogin.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\UnLogin.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\unuse.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\unuse.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\消息.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\消息.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\消息16.bmp">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\消息16.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\hermeticFrame.rc2">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\hermeticFrame.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\hermeticFrame.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\hermetic.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\GUIDoc.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="..\ChartCtrl\ChartBalloonLabel.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="..\ChartCtrl\ChartLabel.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="..\ChartCtrl\ChartPointsArray.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="..\ChartCtrl\ChartSerieBase.inl">
      <Filter>Header Files</Filter>
    </None>
    <None Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{071bbecf-ab01-4bc9-af54-a47c23ca1e3b}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{136457f5-4083-4375-985b-e98512df928e}</UniqueIdentifier>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{580e8e25-eff1-41b6-abc6-54898a2cd24b}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="hermeticFrame.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>