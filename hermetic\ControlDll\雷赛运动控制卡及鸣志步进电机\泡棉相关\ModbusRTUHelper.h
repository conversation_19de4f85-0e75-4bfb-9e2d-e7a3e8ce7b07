#pragma once

#define MAX_REGISTERCOUNT 100
#define MAX_BYTES_COUNT 1024

typedef   void   (CALLBACK   *EventCallback)();

struct StructRegisterValue
{
	int Count;
	int Values[MAX_REGISTERCOUNT];
};

struct CommandStu
{
	int Count;
	BYTE Values[MAX_BYTES_COUNT];
};

typedef BOOL (__stdcall *lpIsOpen)(int nPort);
typedef int (__stdcall *lpOpenPort)(int nPort, int nBaudRate, BOOL bBigEndian);
typedef int (__stdcall *lpClosePort)(int nPort);
typedef void (__stdcall *lpSetEndianType)(int nPort, BOOL bBigEndian);
typedef int (__stdcall *lpIsBigEndian)(int nPort);
typedef int (__stdcall *lpSendCommand)(int nPort, BYTE* lpCommand, int nCommandLen);
typedef int (__stdcall *lpExecuteCommand)(int nPort, BYTE* lpCommand, int nCommandLen, int nTimeOut);
typedef int (__stdcall *lpReadSingleHoldingRegister)(int nPort, BYTE nSlaveAddress, int nRegisterNo, int* pValue, int nTimeOut);
typedef int (__stdcall *lpWriteSingleHoldingRegister)(int nPort, BYTE nSlaveAddress, int nRegisterNo, int nValue, int nTimeOut);
typedef int (__stdcall *lpReadMultiHoldingRegisters)(int nPort, BYTE nSlaveAddress, int nRegisterNo, int nCount, StructRegisterValue& holdingRegister, int nTimeOut);
typedef int (__stdcall *lpWriteMultiHoldingRegisters)(int nPort, BYTE nSlaveAddress, int nRegisterNo, int nCount, int* valueList, int nTimeOut);
typedef void (__stdcall *lpGetLastCommandSend)(int nPort, CommandStu& command);
typedef void (__stdcall *lpGetLastCommandReceived)(int nPort, CommandStu& command);
typedef int (__stdcall *lpReadSingleSCLRegister)(int nPort, BYTE nSlaveAddress, int nSCLRegAddress, int* nValue, int nTimeOut);
typedef int (__stdcall *lpWriteSingleSCLRegister)(int nPort, BYTE nSlaveAddress, int nSCLRegAddress, int nValue, int nTimeOut);
typedef int (__stdcall *lpReadMultiSCLRegisters)(int nPort, BYTE nSlaveAddress, int nSCLRegAddress, int nCount, StructRegisterValue& pRegisterValue, int nTimeOut);
typedef int (__stdcall *lpWriteMultiSCLRegisters)(int nPort, BYTE nSlaveAddress, int nSCLRegAddress, int nCount, int* pValueList, int nTimeOut);
typedef int (__stdcall *lpExecuteCommandWithOpcode)(int nPort, BYTE nSlaveAddress, int nOpCode, int nParam1, int nParam2, int nParam3, int nParam4, int nParam5, int nTimeOut);

typedef void (__stdcall *lpOnDataSend)(EventCallback func);
typedef void (__stdcall *lpOnDataReceived)(EventCallback func);

typedef int (__stdcall *lpAlarmReset)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpStartJogging)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpStopJogging)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpSetEncoderFunction)(int nPort, BYTE nSlaveAddress, int nFunc, int nTimeOut);
typedef int (__stdcall *lpSetEncoderPosition)(int nPort, BYTE nSlaveAddress, int nPosition, int nTimeOut);
typedef int (__stdcall *lpFeedtoDoubleSensor)(int nPort, BYTE nSlaveAddress, int nInput1, int nCondition1, int nInput2, int nCondition2, int nTimeOut);
typedef int (__stdcall *lpFollowEncoder)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpFeedtoLength)(int nPort, BYTE nSlaveAddress, int nDistance, int nTimeOut);
typedef int (__stdcall *lpFeedtoSensorwithMaskDistance)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpFeedandSetOutput)(int nPort, BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpFeedtoPosition)(int nPort, BYTE nSlaveAddress, int nDistance, int nTimeOut);
typedef int (__stdcall *lpFeedtoSensor)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpFeedtoSensorwithSafetyDistance)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpJogDisable)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpJogEnable)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpMotorDisable)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpMotorEnable)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpSeekHome)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpSetPosition)(int nPort, BYTE nSlaveAddress, int nPosition, int nTimeOut);
typedef int (__stdcall *lpSetFilterInput)(int nPort, BYTE nSlaveAddress, int nInput, int nFilterTime, int nTimeOut);
typedef int (__stdcall *lpSetFilterSelectInputs)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpAnalogDeadband)(int nPort, BYTE nSlaveAddress, int nDeadband, int nTimeOut);
typedef int (__stdcall *lpAlarmResetInput)(int nPort, BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut);
typedef int (__stdcall *lpAlarmOutput)(int nPort, BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut);
typedef int (__stdcall *lpAnalogScaling)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpDefineLimits)(int nPort, BYTE nSlaveAddress, int nLimitType, int nTimeOut);
typedef int (__stdcall *lpSetOutput)(int nPort, BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpWaitforInput)(int nPort, BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut);
typedef int (__stdcall *lpQueueLoadAndExecute)(int nPort, BYTE nSlaveAddress, int nSegment, int nTimeOut);
typedef int (__stdcall *lpWaitTime)(int nPort, BYTE nSlaveAddress, int nTime, int nTimeOut);
typedef int (__stdcall *lpStopMoveAndKillBuffer)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpStopMoveAndKillBufferwithNormalDecel)(int nPort, BYTE nSlaveAddress, int nTimeOut);
typedef int (__stdcall *lpSetPointtoPointMoveParam)(int nPort, BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nDistance, int nTimeOut);
typedef int (__stdcall *lpSetJogMoveParam)(int nPort, BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nTimeOut);

class ModbusRTUHelper
{
private:
	HINSTANCE m_hDll; //DLL Handle
	bool m_bWasLoaded;
	int m_Port;
protected:
	lpIsOpen m_IsOpen;
	lpOpenPort m_OpenPort;
	lpClosePort m_ClosePort;
	lpSetEndianType m_SetEndianType;
	lpIsBigEndian m_IsBigEndian;
	lpSendCommand m_SendCommand;
	lpExecuteCommand m_ExecuteCommand;
	lpReadSingleHoldingRegister m_ReadSingleHoldingRegister;
	lpWriteSingleHoldingRegister m_WriteSingleHoldingRegister;
	lpReadMultiHoldingRegisters m_ReadMultiHoldingRegisters;
	lpWriteMultiHoldingRegisters m_WriteMultiHoldingRegisters;
	lpGetLastCommandSend m_GetLastCommandSend;
	lpGetLastCommandReceived m_GetLastCommandReceived;
	lpReadSingleSCLRegister m_ReadSingleSCLRegister;
	lpWriteSingleSCLRegister m_WriteSingleSCLRegister;
	lpReadMultiSCLRegisters m_ReadMultiSCLRegisters;
	lpWriteMultiSCLRegisters m_WriteMultiSCLRegisters;
	lpExecuteCommandWithOpcode m_ExecuteCommandWithOpcode;
	lpOnDataSend m_OnDataSend;
	lpOnDataReceived m_OnDataReceived;
	lpAlarmReset m_AlarmReset;
	lpStartJogging m_StartJogging;
	lpStopJogging m_StopJogging;
	lpSetEncoderFunction m_SetEncoderFunction;
	lpSetEncoderPosition m_SetEncoderPosition;
	lpFeedtoDoubleSensor m_FeedtoDoubleSensor;
	lpFollowEncoder m_FollowEncoder;
	lpFeedtoLength m_FeedtoLength;
	lpFeedtoSensorwithMaskDistance m_FeedtoSensorwithMaskDistance;
	lpFeedandSetOutput m_FeedandSetOutput;
	lpFeedtoPosition m_FeedtoPosition;
	lpFeedtoSensor m_FeedtoSensor;
	lpFeedtoSensorwithSafetyDistance m_FeedtoSensorwithSafetyDistance;
	lpJogDisable m_JogDisable;
	lpJogEnable m_JogEnable;
	lpMotorDisable m_MotorDisable;
	lpMotorEnable m_MotorEnable;
	lpSeekHome m_SeekHome;
	lpSetPosition m_SetPosition;
	lpSetFilterInput m_SetFilterInput;
	lpSetFilterSelectInputs m_SetFilterSelectInputs;
	lpAnalogDeadband m_AnalogDeadband;
	lpAlarmResetInput m_AlarmResetInput;
	lpAlarmOutput m_AlarmOutput;
	lpAnalogScaling m_AnalogScaling;
	lpDefineLimits m_DefineLimits;
	lpSetOutput m_SetOutput;
	lpWaitforInput m_WaitforInput;
	lpQueueLoadAndExecute m_QueueLoadAndExecute;
	lpWaitTime m_WaitTime;
	lpStopMoveAndKillBuffer m_StopMoveAndKillBuffer;
	lpStopMoveAndKillBufferwithNormalDecel m_StopMoveAndKillBufferwithNormalDecel;
	lpSetPointtoPointMoveParam m_SetPointtoPointMoveParam;
	lpSetJogMoveParam m_SetJogMoveParam;
public:
	ModbusRTUHelper(void);
	virtual ~ModbusRTUHelper(void);
	//void OnDataSendCallback(void);
	//EventCallback OnDataSendEvent;
	//EventCallback OnDataSend();
	//EventCallback OnDataReceived;
	
	void GetLastCommandSend(CommandStu& command);
	void GetLastCommandReceived(CommandStu& command);

	BOOL IsOpen();
	int OpenPort(int nPort, int nBaudRate, BOOL bBigEndian);
	int ClosePort();
	void SetEndianType(BOOL bBigEndian);
	int IsBigEndian();
	int SendCommand(BYTE* lpCommand, int nCommandLen);
	int ExecuteCommand(BYTE* lpCommand, int nCommandLen, int nTimeOut = 60);
	int ReadSingleHoldingRegister(int nSlaveAddress, int nRegisterNo, int* pValue, int nTimeOut = 60);
	int WriteSingleHoldingRegister(int nSlaveAddress, int nRegisterNo, int nValue, int nTimeOut = 60);
	int ReadMultiHoldingRegisters(int nSlaveAddress, int nRegisterNo, int nCount, StructRegisterValue& holdingRegister, int nTimeOut = 100);
	int WriteMultiHoldingRegisters(int nSlaveAddress, int nRegisterNo, int nCount, int* valueList, int nTimeOut = 100);
	int ReadSingleSCLRegister(BYTE nSlaveAddress, int nSCLRegAddress, int* nValue, int nTimeOut = 60);
	int WriteSingleSCLRegister(BYTE nSlaveAddress, int nSCLRegAddress, int nValue, int nTimeOut = 60);
	int ReadMultiSCLRegisters(BYTE nSlaveAddress, int nSCLRegAddress, int nCount, StructRegisterValue& pRegisterValue, int nTimeOut = 60);
	int WriteMultiSCLRegisters(BYTE nSlaveAddress, int nSCLRegAddress, int nCount, int* pValueList, int nTimeOut = 60);
	int ExecuteCommandWithOpcode(BYTE nSlaveAddress, int nOpCode, int nParam1 = 0, int nParam2 = 0, int nParam3 = 0, int nParam4 = 0, int nParam5 = 0, int nTimeOut = 60);
	int AlarmReset(BYTE nSlaveAddress, int nTimeOut = 60);
	int StartJogging(BYTE nSlaveAddress, int nTimeOut = 60);
	int StopJogging(BYTE nSlaveAddress, int nTimeOut = 60);
	int SetEncoderFunction(BYTE nSlaveAddress, int nFunc, int nTimeOut = 60);
	int SetEncoderPosition(BYTE nSlaveAddress, int nPosition, int nTimeOut = 60);
	int FeedtoDoubleSensor(BYTE nSlaveAddress, int nInput1, int nCondition1, int nInput2, int nCondition2, int nTimeOut = 60);
	int FollowEncoder(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int FeedtoLength(BYTE nSlaveAddress, int nDistance, int nTimeOut = 60);
	int FeedtoSensorwithMaskDistance(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int FeedandSetOutput(BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut = 60);
	int FeedtoPosition(BYTE nSlaveAddress, int nDistance, int nTimeOut = 60);
	int FeedtoSensor(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int FeedtoSensorwithSafetyDistance(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int JogDisable(BYTE nSlaveAddress, int nTimeOut = 60);
	int JogEnable(BYTE nSlaveAddress, int nTimeOut = 60);
	int MotorDisable(BYTE nSlaveAddress, int nTimeOut = 60);
	int MotorEnable(BYTE nSlaveAddress, int nTimeOut = 60);
	int SeekHome(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int SetPosition(BYTE nSlaveAddress, int nPosition, int nTimeOut = 60);
	int SetFilterInput(BYTE nSlaveAddress, int nInput, int nFilterTime, int nTimeOut = 60);
	int SetFilterSelectInputs(BYTE nSlaveAddress, int nTimeOut = 60);
	int AnalogDeadband(BYTE nSlaveAddress, int nDeadband, int nTimeOut = 60);
	int AlarmResetInput(BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut = 60);
	int AlarmOutput(BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut = 60);
	int AnalogScaling(BYTE nSlaveAddress, int nTimeOut = 60);
	int DefineLimits(BYTE nSlaveAddress, int nLimitType, int nTimeOut = 60);
	int SetOutput(BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut = 60);
	int WaitforInput(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut = 60);
	int QueueLoadAndExecute(BYTE nSlaveAddress, int nSegment, int nTimeOut = 60);
	int WaitTime(BYTE nSlaveAddress, int nTime, int nTimeOut = 60);
	int StopMoveAndKillBuffer(BYTE nSlaveAddress, int nTimeOut = 60);
	int StopMoveAndKillBufferwithNormalDecel(BYTE nSlaveAddress, int nTimeOut = 60);
	int SetPointtoPointMoveParam(BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nDistance, int nTimeOut = 60);
	int SetJogMoveParam(BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nTimeOut = 60);


};

