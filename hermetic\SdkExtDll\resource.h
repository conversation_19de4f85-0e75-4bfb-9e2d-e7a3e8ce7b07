//{{NO_DEPENDENCIES}}
// Microsoft Visual C++ generated include file.
// Used by SdkExtDll.rc
//
#define ID_ALLSELECT                    3
#define IDREFRESH                       3
#define ID_SELECTNONE                   4
#define IDR_MENU_SDKPUP                 120
#define IDD_DLG_CONTROL                 130
#define IDI_ICON_COTROL                 136
#define IDI_ICON_LEDON                  137
#define IDI_ICON_MOTOROFF               139
#define IDI_ICON_MOTORON                140
#define IDI_ICON_LEDOFF                 149
#define IDB_TOOLBAR_EDIT                179
#define IDB_TOOLBAR_EDIT_HOT            180
#define IDB_TOOLBAR_EDIT_DISABLED       181
#define IDB_TOOLBAR_USER                184
#define IDB_TOOLBAR_USER_HOT            185
#define IDB_TOOLBAR_USER_DISABLED       186
#define IDB_TOOLBAR_SYS                 189
#define IDB_TOOLBAR_SYS_DISABLED        190
#define IDB_TOOLBAR_SYS_HOT             191
#define IDC_CURSOR_HAND                 197
#define IDI_ICON_MOPEN                  208
#define IDI_ICON_MCLOSE                 209
#define IDI_ICON_MCLOSE1                210
#define IDI_ICON_MOPEN1                 211
#define IDC_LISTPOS                     1000
#define IDC_CHECK_PRE                   1000
#define IDC_LISTIN                      1001
#define IDD_DLG_CONDITION_OUTPORT       1001
#define IDC_CHECK_OPEN                  1001
#define IDC_EDIT_INPUT                  1002
#define IDD_DLG_CONDITION_MOTOR         1002
#define IDC_LISTOUT                     1003
#define IDC_LIST_DATA                   1003
#define IDD_DLG_HOME_TYPE               1003
#define IDC_LBL_PROMPT                  1004
#define IDC_LISTRELAY                   1004
#define IDC_TREE_MOTOR                  1004
#define IDC_LIST_IN                     1004
#define IDC_STATIC_OUTPORT              1005
#define IDC_LISTMT                      1006
#define IDC_LIST_OUT                    1006
#define IDC_LIST_MT_POS                 1007
#define IDC_LISTPOSIMG                  1008
#define IDC_LIST_MT_ENABLE              1008
#define IDC_TABPOS_IO                   1009
#define IDC_STATIC_MOTOR                1009
#define IDC_TABMT_IMG                   1011
#define IDC_LIST1                       1011
#define IDC_LIST_HOME_TYPE              1011
#define IDC_LISTCCD                     1012
#define IDC_CHK_INPORTS                 1013
#define IDC_BTMTHOME                    1014
#define IDC_PROGRESS1                   1015
#define IDC_BTMTLEFT                    1016
#define IDC_BTMTRIGHT                   1017
#define IDC_BTMTTO                      1018
#define IDC_EDITTOSTEP                  1019
#define IDC_EDIT_Threshold              1020
#define IDC_LISTSETTING                 1020
#define IDC_COMBO_PATTERN               1020
#define IDC_EDIT_Threshold2             1021
#define IDC_LISTGROUP                   1021
#define IDC_CHK_BLACK                   1022
#define IDC_CHK_RELAY                   1022
#define IDC_CHK_WHITE                   1023
#define IDC_LABELPROMPT                 1023
#define IDC_EDIT_Threshold3             1024
#define IDC_CHK_BY_STEP                 1024
#define IDC_CHK_BLACK2                  1025
#define IDC_LABELSTEP                   1025
#define IDC_CHK_WHITE2                  1026
#define IDC_BT_SAVEPIC                  1026
#define IDC_EDIT_Threshold4             1027
#define IDC_BTMTHOME2                   1027
#define IDC_BTMTENABLE                  1027
#define IDC_CHECK_SKELETON              1028
#define IDC_STATIC_COLOR                1029
#define IDC_CHK_STEP_MOVE               1029
#define IDC_EDIT2                       1030
#define IDC_CHK_CONTGRAB                1032
#define IDC_CHECK_FILLBLOB              1033
#define IDC_LIST_STEP                   1033
#define IDC_STATIC_COLOR2               1034
#define IDC_CHECK_FILLBLOB2             1039
#define IDC_COMBO_STEP                  1040
#define IDC_CHECK_OUT_RANGE             1042
#define IDC_CHECK_OUT_RANGE2            1043
#define IDC_CHK_OUT_CONDITION           1046
#define IDC_CHECK_BINARIZE_DISPLAY      1047
#define IDC_FRAME_MT                    1053
#define IDC_BT_OPENPIC                  1054
#define IDC_COMBO_MIMARITH              1059
#define IDC_COMBO_NO1                   1061
#define IDC_COMBO_NO2                   1062
#define IDC_BT_OPEN1                    1064
#define IDC_BT_OPEN2                    1065
#define IDC_BTIMGSEARCH                 1087
#define IDC_BTIMGMODEL                  1088
#define IDC_BTIMGMATCH                  1089
#define IDC_BTIMGBLOB                   1090
#define IDC_CHKMAXAREA                  1096
#define IDC_CHKMINAREA                  1097
#define IDC_EDITMAXAREA                 1098
#define IDC_EDITMINAREA                 1099
#define IDC_EDITMin_Radius              1100
#define IDC_BTTEST                      1102
#define IDC_LISTRESULT                  1103
#define IDC_CHKMAXAREA2                 1104
#define IDC_BT_OUTPUT                   1104
#define IDC_CHKMINAREA2                 1105
#define IDC_SLIDERTHRESHOLD1            1106
#define IDC_SLIDERTHRESHOLD2            1107
#define IDC_EDITMAXAREA2                1108
#define IDC_EDITMINAREA2                1109
#define IDC_EDITMin_Radius2             1110
#define IDC_SLIDERTHRESHOLD3            1114
#define IDC_SLIDERTHRESHOLD4            1115
#define IDD_DLG_BLOBSETTING             21000
#define IDD_DLG_DEBUG                   21001
#define IDD_DLG_IMAGE                   21002
#define IDD_DLG_INPUTBOX                21003
#define IDD_DLG_SETTING                 21004
#define IDD_DLG_THRESHOLD_SET           21005
#define IDD__DLG2IMAGEARITH             21006
#define ID_MENU_SDK_OUTPORT_CONDITION   32772
#define ID_MENU_SDK_MOVE_CONDITION      32777
#define ID_MENU_HM_LeftLimitOnly        32803
#define ID_MENU_HM_RightLimitOnly       32805
#define ID_MENU_HM_HomeLeftLimit        32806
#define ID_MENU_HM_HomeRightLimit       32807
#define ID_MENU_HM_HomeLeftLimitIndex   32808
#define ID_MENU_HM_HomeRightLimitIndex  32809
#define ID_32810                        32810
#define ID_MENU_SDK_HomeType            32811
#define ID_MENU_SDK_MaxTravel           32818
#define ID_MENU_SDK_MinTravel           32819
#define ID_MENU_SDK_SETRELAY            33771
#define ID_MENU_SDK_SETMT               33772
#define ID_MENU_SDK_SETCCD              33773
#define ID_MENU_SDK_DELAYON             33774
#define ID_MENU_SDK_DELAYOFF            33775
#define ID_MENU_SDK_SETCCDPScaleX       33776
#define ID_MENU_SDK_SETCCDPScaleY       33777
#define ID_MENU_SDK_SETVMAX             33778
#define ID_MENU_SDK_SETVMIN             33779
#define ID_MENU_SDK_APPLYSELECT         33780
#define ID_MENU_SDK_SETVHOME            33781
#define ID_MENU_SDK_BINARILIZE          33782
#define ID_MENU_SDK_SETHOMEDIST         33783
#define ID_MENU_SDK_SETSTEPPERROUND     33784
#define ID_MENU_SDK_SETSTROKE           33785
#define ID_MENU_SDK_SETRATIO            33786
#define ID_MENU_SDK_SAVEPOS             33787
#define ID_MENU_SDK_ACCEPSCORE          33788
#define ID_MENU_SDK_CertThrld           33789
#define ID_MENU_SDK_SETCCDPAL           33790
#define ID_MENU_SDK_SETCCDNTSC          33791
#define ID_MENU_SDK_PATTERNQTY          33792
#define ID_MENU_SDK_SETVWORK            33793
#define ID_MENU_SDK_InputStart          33795
#define ID_MENU_SDK_InputEnd            33796
#define ID_MENU_SDK_HomeDir             33797
#define ID_MENU_SDK_MoveDir             33798
#define ID_MENU_SDK_Acc                 33799
#define ID_MENU_SDK_Jerk                33800
#define ID_MENU_SDK_Kp                  33801
#define ID_MENU_SDK_Ki                  33802
#define ID_MENU_SDK_Kd                  33803
#define ID_MENU_SDK_Kvff                33804
#define ID_MENU_SDK_Kaff                33805
#define ID_MENU_SDK_LmtSns1             33806
#define ID_MENU_SDK_LmtSns2             33807
#define ID_MENU_SDK_EnLevel             33808
#define ID_MENU_SDK_Rotating            33809
#define ID_MENU_SDK_SETPOS              33810
#define ID_MENU_SDK_SETOUT              33811
#define ID_MENU_SDK_SETIN               33812
#define ID_MENU_SDK_CtrlMode            33813
#define ID_MENU_SDK_MotionMode          33814
#define ID_MENU_SDK_EncSns              33815
#define ID_MENU_IMG_ZOOM_100            36770
#define ID_MENU_IMG_ZOOM_IN             36771
#define ID_MENU_IMG_ZOOM_OUT            36772
#define ID_MENU_IMG_SAVE                37001
#define IDR_MENU_APPPUP                 37002

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        1004
#define _APS_NEXT_COMMAND_VALUE         32812
#define _APS_NEXT_CONTROL_VALUE         1012
#define _APS_NEXT_SYMED_VALUE           1000
#endif
#endif
