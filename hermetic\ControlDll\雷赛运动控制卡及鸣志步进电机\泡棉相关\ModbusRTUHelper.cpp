#include "StdAfx.h"
#include "ModbusRTUHelper.h"


EventCallback OnDataSend()
{
	::SendMessage(AfxGetApp()->m_pMainWnd->m_hWnd, WM_DATASEND_MESSAGE, 0, 0);
	return 0;
}

EventCallback OnDataReceived()
{
	::SendMessage(AfxGetApp()->m_pMainWnd->m_hWnd, WM_DATARECEIVED_MESSAGE, 0, 0);
	return 0;
}

ModbusRTUHelper::ModbusRTUHelper(void)
{
	m_bWasLoaded = false;
	m_hDll = NULL;
	m_hDll = LoadLibrary("ModbusRTU.dll");
	if (m_hDll != NULL)
	{
		m_IsOpen = (lpIsOpen)GetProcAddress(m_hDll, "IsOpen");
		m_OpenPort = (lpOpenPort)GetProcAddress(m_hDll, "OpenPort");
		m_ClosePort = (lpClosePort)GetProcAddress(m_hDll, "ClosePort");
		m_SetEndianType = (lpSetEndianType)GetProcAddress(m_hDll, "SetEndianType");
		m_IsBigEndian = (lpIsBigEndian)GetProcAddress(m_hDll, "IsBigEndian");
		m_SendCommand = (lpSendCommand)GetProcAddress(m_hDll, "SendCommand");
		m_ExecuteCommand = (lpExecuteCommand)GetProcAddress(m_hDll, "ExecuteCommand");
		m_ReadSingleHoldingRegister = (lpReadSingleHoldingRegister)GetProcAddress(m_hDll, "ReadSingleHoldingRegister");
		m_WriteSingleHoldingRegister = (lpWriteSingleHoldingRegister)GetProcAddress(m_hDll, "WriteSingleHoldingRegister");
		m_ReadMultiHoldingRegisters = (lpReadMultiHoldingRegisters)GetProcAddress(m_hDll, "ReadMultiHoldingRegisters");
		m_WriteMultiHoldingRegisters = (lpWriteMultiHoldingRegisters)GetProcAddress(m_hDll, "WriteMultiHoldingRegisters");
		m_GetLastCommandSend = (lpGetLastCommandSend)GetProcAddress(m_hDll, "GetLastCommandSend");
		m_GetLastCommandReceived = (lpGetLastCommandReceived)GetProcAddress(m_hDll, "GetLastCommandReceived");
		m_ReadSingleSCLRegister = (lpReadSingleSCLRegister)GetProcAddress(m_hDll, "ReadSingleSCLRegister");
		m_WriteSingleSCLRegister = (lpWriteSingleSCLRegister)GetProcAddress(m_hDll, "WriteSingleSCLRegister");
		m_ReadMultiSCLRegisters = (lpReadMultiSCLRegisters)GetProcAddress(m_hDll, "ReadMultiSCLRegisters");
		m_WriteMultiSCLRegisters = (lpWriteMultiSCLRegisters)GetProcAddress(m_hDll, "WriteMultiSCLRegisters");
		m_ExecuteCommandWithOpcode = (lpExecuteCommandWithOpcode)GetProcAddress(m_hDll, "ExecuteCommandWithOpcode");
		m_AlarmReset = (lpAlarmReset)GetProcAddress(m_hDll, "AlarmReset");
		m_StartJogging = (lpStartJogging)GetProcAddress(m_hDll, "StartJogging");
		m_StopJogging = (lpStopJogging)GetProcAddress(m_hDll, "StopJogging");
		m_SetEncoderFunction = (lpSetEncoderFunction)GetProcAddress(m_hDll, "SetEncoderFunction");
		m_SetEncoderPosition = (lpSetEncoderPosition)GetProcAddress(m_hDll, "SetEncoderPosition");
		m_FeedtoDoubleSensor = (lpFeedtoDoubleSensor)GetProcAddress(m_hDll, "FeedtoDoubleSensor");
		m_FollowEncoder = (lpFollowEncoder)GetProcAddress(m_hDll, "FollowEncoder");
		m_FeedtoLength = (lpFeedtoLength)GetProcAddress(m_hDll, "FeedtoLength");
		m_FeedtoSensorwithMaskDistance = (lpFeedtoSensorwithMaskDistance)GetProcAddress(m_hDll, "FeedtoSensorwithMaskDistance");
		m_FeedandSetOutput = (lpFeedandSetOutput)GetProcAddress(m_hDll, "FeedandSetOutput");
		m_FeedtoPosition = (lpFeedtoPosition)GetProcAddress(m_hDll, "FeedtoPosition");
		m_FeedtoSensor = (lpFeedtoSensor)GetProcAddress(m_hDll, "FeedtoSensor");
		m_FeedtoSensorwithSafetyDistance = (lpFeedtoSensorwithSafetyDistance)GetProcAddress(m_hDll, "FeedtoSensorwithSafetyDistance");
		m_JogDisable = (lpJogDisable)GetProcAddress(m_hDll, "JogDisable");
		m_JogEnable = (lpJogEnable)GetProcAddress(m_hDll, "JogEnable");
		m_MotorDisable = (lpMotorDisable)GetProcAddress(m_hDll, "MotorDisable");
		m_MotorEnable = (lpMotorEnable)GetProcAddress(m_hDll, "MotorEnable");
		m_SeekHome = (lpSeekHome)GetProcAddress(m_hDll, "SeekHome");
		m_SetPosition = (lpSetPosition)GetProcAddress(m_hDll, "SetPosition");
		m_SetFilterInput = (lpSetFilterInput)GetProcAddress(m_hDll, "SetFilterInput");
		m_SetFilterSelectInputs = (lpSetFilterSelectInputs)GetProcAddress(m_hDll, "SetFilterSelectInputs");
		m_AnalogDeadband = (lpAnalogDeadband)GetProcAddress(m_hDll, "AnalogDeadband");
		m_AlarmResetInput = (lpAlarmResetInput)GetProcAddress(m_hDll, "AlarmResetInput");
		m_AlarmOutput = (lpAlarmOutput)GetProcAddress(m_hDll, "AlarmOutput");
		m_AnalogScaling = (lpAnalogScaling)GetProcAddress(m_hDll, "AnalogScaling");
		m_DefineLimits = (lpDefineLimits)GetProcAddress(m_hDll, "DefineLimits");
		m_SetOutput = (lpSetOutput)GetProcAddress(m_hDll, "SetOutput");
		m_WaitforInput = (lpWaitforInput)GetProcAddress(m_hDll, "WaitforInput");
		m_QueueLoadAndExecute = (lpQueueLoadAndExecute)GetProcAddress(m_hDll, "QueueLoadAndExecute");
		m_WaitTime = (lpWaitTime)GetProcAddress(m_hDll, "WaitTime");
		m_StopMoveAndKillBuffer = (lpStopMoveAndKillBuffer)GetProcAddress(m_hDll, "StopMoveAndKillBuffer");
		m_StopMoveAndKillBufferwithNormalDecel = (lpStopMoveAndKillBufferwithNormalDecel)GetProcAddress(m_hDll, "StopMoveAndKillBufferwithNormalDecel");
		m_SetPointtoPointMoveParam = (lpSetPointtoPointMoveParam)GetProcAddress(m_hDll, "SetPointtoPointMoveParam");
		m_SetJogMoveParam = (lpSetJogMoveParam)GetProcAddress(m_hDll, "SetJogMoveParam");


		m_OnDataSend = (lpOnDataSend)GetProcAddress(m_hDll, "OnDataSend");
		m_OnDataReceived = (lpOnDataReceived)GetProcAddress(m_hDll, "OnDataReceived");
		m_OnDataSend((EventCallback)OnDataSend);
		m_OnDataReceived((EventCallback)OnDataReceived);
	}
	else
	{
		AfxMessageBox("Fail to load ModbusRTU.dll");
	}
	m_bWasLoaded = true;
}


ModbusRTUHelper::~ModbusRTUHelper(void)
{
	m_bWasLoaded = false;
	if (m_hDll != NULL)
	{
		m_ClosePort(m_Port);
		FreeLibrary(m_hDll);
	}	
	m_hDll = NULL;
}

BOOL ModbusRTUHelper::IsOpen()
{
	return m_IsOpen(m_Port);
}

int ModbusRTUHelper::OpenPort(int nPort, int nBaudRate, BOOL bBigEndian)
{
	m_Port = nPort;
	return m_OpenPort(nPort, nBaudRate, bBigEndian);
}

int ModbusRTUHelper::ClosePort()
{
	return m_ClosePort(m_Port);
}

void ModbusRTUHelper::SetEndianType(BOOL bBigEndian)
{
	return m_SetEndianType(m_Port, bBigEndian);
}

int ModbusRTUHelper::IsBigEndian()
{
	return m_IsBigEndian(m_Port);
}

int ModbusRTUHelper::SendCommand(BYTE* lpCommand, int nCommandLen)
{
	return m_SendCommand(m_Port, lpCommand, nCommandLen);
}

int ModbusRTUHelper::ExecuteCommand(BYTE* lpCommand, int nCommandLen, int nTimeOut)
{
	return m_ExecuteCommand(m_Port, lpCommand, nCommandLen, nTimeOut);
}

int ModbusRTUHelper::ReadSingleHoldingRegister(int nSlaveAddress, int nRegisterNo, int* pValue, int nTimeOut)
{
	return m_ReadSingleHoldingRegister(m_Port, nSlaveAddress, nRegisterNo, pValue, nTimeOut);
}

int ModbusRTUHelper::WriteSingleHoldingRegister(int nSlaveAddress, int nRegisterNo, int nValue, int nTimeOut)
{
	return m_WriteSingleHoldingRegister(m_Port, nSlaveAddress, nRegisterNo, nValue, nTimeOut);
}

int ModbusRTUHelper::ReadMultiHoldingRegisters(int nSlaveAddress, int nRegisterNo, int nCount, StructRegisterValue& holdingRegister, int nTimeOut)
{
	return m_ReadMultiHoldingRegisters(m_Port, nSlaveAddress, nRegisterNo, nCount, holdingRegister, nTimeOut);
}

int ModbusRTUHelper::WriteMultiHoldingRegisters(int nSlaveAddress, int nRegisterNo, int nCount, int* valueList, int nTimeOut)
{
	return m_WriteMultiHoldingRegisters(m_Port, nSlaveAddress, nRegisterNo, nCount, valueList, nTimeOut);
}

int ModbusRTUHelper::ReadSingleSCLRegister(BYTE nSlaveAddress, int nSCLRegAddress, int* nValue, int nTimeOut)
{
	return m_ReadSingleSCLRegister(m_Port, nSlaveAddress, nSCLRegAddress, nValue, nTimeOut);
}

int ModbusRTUHelper::WriteSingleSCLRegister(BYTE nSlaveAddress, int nSCLRegAddress, int nValue, int nTimeOut)
{
	return m_WriteSingleSCLRegister(m_Port, nSlaveAddress, nSCLRegAddress, nValue, nTimeOut);
}

int ModbusRTUHelper::ReadMultiSCLRegisters(BYTE nSlaveAddress, int nSCLRegAddress, int nCount, StructRegisterValue& pRegisterValue, int nTimeOut)
{
	return m_ReadMultiSCLRegisters(m_Port, nSlaveAddress, nSCLRegAddress, nCount, pRegisterValue, nTimeOut);
}

int ModbusRTUHelper::WriteMultiSCLRegisters(BYTE nSlaveAddress, int nSCLRegAddress, int nCount, int* pValueList, int nTimeOut)
{
	return m_WriteMultiSCLRegisters(m_Port, nSlaveAddress, nSCLRegAddress, nCount, pValueList, nTimeOut);
}

void ModbusRTUHelper::GetLastCommandSend(CommandStu& command)
{
	return m_GetLastCommandSend(m_Port, command);
}

void ModbusRTUHelper::GetLastCommandReceived(CommandStu& command)
{
	return m_GetLastCommandReceived(m_Port, command);
}

int ModbusRTUHelper::ExecuteCommandWithOpcode(BYTE nSlaveAddress, int nOpCode, int nParam1, int nParam2, int nParam3, int nParam4, int nParam5, int nTimeOut)
{
	return m_ExecuteCommandWithOpcode(m_Port, nSlaveAddress, nOpCode, nParam1, nParam2, nParam3, nParam4, nParam5, nTimeOut);
}

int ModbusRTUHelper::AlarmReset(BYTE nSlaveAddress, int nTimeOut)
{
	return m_AlarmReset(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::StartJogging(BYTE nSlaveAddress, int nTimeOut)
{
	return m_StartJogging(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::StopJogging(BYTE nSlaveAddress, int nTimeOut)
{
	return m_StopJogging(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::SetEncoderFunction(BYTE nSlaveAddress, int nFunc, int nTimeOut)
{
	return m_SetEncoderFunction(m_Port, nSlaveAddress, nFunc, nTimeOut);
}

int ModbusRTUHelper::SetEncoderPosition(BYTE nSlaveAddress, int nPosition, int nTimeOut)
{
	return m_SetEncoderPosition(m_Port, nSlaveAddress, nPosition, nTimeOut);
}

int ModbusRTUHelper::FeedtoDoubleSensor(BYTE nSlaveAddress, int nInput1, int nCondition1, int nInput2, int nCondition2, int nTimeOut)
{
	return m_FeedtoDoubleSensor(m_Port, nSlaveAddress, nInput1, nCondition1, nInput2, nCondition2, nTimeOut);
}

int ModbusRTUHelper::FollowEncoder(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_FollowEncoder(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::FeedtoLength(BYTE nSlaveAddress, int nDistance, int nTimeOut)
{
	return m_FeedtoLength(m_Port, nSlaveAddress, nDistance, nTimeOut);
}

int ModbusRTUHelper::FeedtoSensorwithMaskDistance(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_FeedtoSensorwithMaskDistance(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::FeedandSetOutput(BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut)
{
	return m_FeedandSetOutput(m_Port, nSlaveAddress, nOutput, nCondition, nTimeOut);
}

int ModbusRTUHelper::FeedtoPosition(BYTE nSlaveAddress, int nDistance, int nTimeOut)
{
	return m_FeedtoPosition(m_Port, nSlaveAddress, nDistance, nTimeOut);
}

int ModbusRTUHelper::FeedtoSensor(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_FeedtoSensor(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::FeedtoSensorwithSafetyDistance(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_FeedtoSensorwithSafetyDistance(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::JogDisable(BYTE nSlaveAddress, int nTimeOut)
{
	return m_JogDisable(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::JogEnable(BYTE nSlaveAddress, int nTimeOut)
{
	return m_JogEnable(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::MotorDisable(BYTE nSlaveAddress, int nTimeOut)
{
	return m_MotorDisable(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::MotorEnable(BYTE nSlaveAddress, int nTimeOut)
{
	return m_MotorEnable(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::SeekHome(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_SeekHome(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::SetPosition(BYTE nSlaveAddress, int nPosition, int nTimeOut)
{
	return m_SetPosition(m_Port, nSlaveAddress, nPosition, nTimeOut);
}

int ModbusRTUHelper::SetFilterInput(BYTE nSlaveAddress, int nInput, int nFilterTime, int nTimeOut)
{
	return m_SetFilterInput(m_Port, nSlaveAddress, nInput, nFilterTime, nTimeOut);
}

int ModbusRTUHelper::SetFilterSelectInputs(BYTE nSlaveAddress, int nTimeOut)
{
	return m_SetFilterSelectInputs(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::AnalogDeadband(BYTE nSlaveAddress, int nDeadband, int nTimeOut)
{
	return m_AnalogDeadband(m_Port, nSlaveAddress, nDeadband, nTimeOut);
}

int ModbusRTUHelper::AlarmResetInput(BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut)
{
	return m_AlarmResetInput(m_Port, nSlaveAddress, nFunction, nInput, nTimeOut);
}

int ModbusRTUHelper::AlarmOutput(BYTE nSlaveAddress, int nFunction, int nInput, int nTimeOut)
{
	return m_AlarmOutput(m_Port, nSlaveAddress, nFunction, nInput, nTimeOut);
}

int ModbusRTUHelper::AnalogScaling(BYTE nSlaveAddress, int nTimeOut)
{
	return m_AnalogScaling(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::DefineLimits(BYTE nSlaveAddress, int nLimitType, int nTimeOut)
{
	return m_DefineLimits(m_Port, nSlaveAddress, nLimitType, nTimeOut);
}

int ModbusRTUHelper::SetOutput(BYTE nSlaveAddress, int nOutput, int nCondition, int nTimeOut)
{
	return m_SetOutput(m_Port, nSlaveAddress, nOutput, nCondition, nTimeOut);
}

int ModbusRTUHelper::WaitforInput(BYTE nSlaveAddress, int nInput, int nCondition, int nTimeOut)
{
	return m_WaitforInput(m_Port, nSlaveAddress, nInput, nCondition, nTimeOut);
}

int ModbusRTUHelper::QueueLoadAndExecute(BYTE nSlaveAddress, int nSegment, int nTimeOut)
{
	return m_QueueLoadAndExecute(m_Port, nSlaveAddress, nSegment, nTimeOut);
}

int ModbusRTUHelper::WaitTime(BYTE nSlaveAddress, int nTime, int nTimeOut)
{
	return m_WaitTime(m_Port, nSlaveAddress, nTime, nTimeOut);
}

int ModbusRTUHelper::StopMoveAndKillBuffer(BYTE nSlaveAddress, int nTimeOut)
{
	return m_StopMoveAndKillBuffer(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::StopMoveAndKillBufferwithNormalDecel(BYTE nSlaveAddress, int nTimeOut)
{
	return m_StopMoveAndKillBufferwithNormalDecel(m_Port, nSlaveAddress, nTimeOut);
}

int ModbusRTUHelper::SetPointtoPointMoveParam(BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nDistance, int nTimeOut)
{
	return m_SetPointtoPointMoveParam(m_Port, nSlaveAddress, dAccel, dDecel, dVelocity, nDistance, nTimeOut);
}

int ModbusRTUHelper::SetJogMoveParam(BYTE nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nTimeOut)
{
	return m_SetJogMoveParam(m_Port, nSlaveAddress, dAccel, dDecel, dVelocity, nTimeOut);
}


