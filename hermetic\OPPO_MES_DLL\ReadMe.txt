﻿========================================================================
    MICROSOFT 基础类库 : OPPO_MES_DLL 项目概述
========================================================================


应用程序向导已为您创建了此 OPPO_MES_DLL DLL。  此 DLL 不仅演示 
Microsoft 基础类的基本使用方法，还可作为您编写 DLL 的起点。

本文件概要介绍组成 OPPO_MES_DLL DLL 的每个文件的内容。

OPPO_MES_DLL.vcxproj
    这是使用应用程序向导生成的 VC++ 项目的主项目文件，
    其中包含生成该文件的 Visual C++ 
    的版本信息，以及有关使用应用程序向导选择的平台、配置和项目功能的信息。

OPPO_MES_DLL.vcxproj.filters
    这是使用“应用程序向导”生成的 VC++ 项目筛选器文件。 
    它包含有关项目文件与筛选器之间的关联信息。 在 IDE 
    中，通过这种关联，
    在特定节点下以分组形式显示具有相似扩展名的文件。
    例如，“.cpp”文件与“源文件”筛选器关联。

OPPO_MES_DLL.h
    这是 DLL 的主头文件。  它声明了 COPPO_MES_DLLApp 类。

OPPO_MES_DLL.cpp
    这是主 DLL 源文件。  它包含 COPPO_MES_DLLApp 类。

OPPO_MES_DLL.rc
    这是程序使用的所有 Microsoft Windows 资源的列表。  它包括 RES 
    子目录中存储的图标、位图和光标。  此文件可以直接在 Microsoft Visual C++ 
    中进行编辑。

res\OPPO_MES_DLL.rc2
    此文件包含不在 Microsoft Visual C++ 中进行编辑的资源。  
    您应该将不可由资源编辑器编辑的所有资源放在此文件中。

OPPO_MES_DLL.def
    此文件包含在 Microsoft Windows 中运行所必需的 DLL 的有关信息。它定义了 DLL 
    的名称和说明等参数，而且还从 DLL 导出函数。

/////////////////////////////////////////////////////////////////////////////
其他标准文件：

StdAfx.h，StdAfx.cpp
    这些文件用于生成名为 OPPO_MES_DLL.pch 的预编译头 (PCH) 文件和名为 
    StdAfx.obj 的预编译类型文件。

Resource.h
    这是标准头文件，可用于定义新的资源 ID。
    Microsoft Visual C++ 将读取并更新此文件。

/////////////////////////////////////////////////////////////////////////////
其他注释：

应用程序向导使用“TODO:”来指示应添加或自定义的源代码部分。

/////////////////////////////////////////////////////////////////////////////
