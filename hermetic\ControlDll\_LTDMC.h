#pragma once

#include "ModbusRTUHelper.h"
#include "..\\ControlDll_include\\ControlConst.h"

class C_LTDMC
{
public:
	 C_LTDMC(void);
	~ C_LTDMC(void);
public:
	ModbusRTUHelper *pStep;
	int nPosition[8];
public:
	int GetPosition(int addr);
	bool OpenPort(int nPort, int nBaudRate = 9600);
	void ClosePort();
	void Enable(int addr, BOOL bEnable);
	void Stop(int addr);
	bool Home(int addr, bool bIsRotating, int level, double dAcc = 100, double dDec = 100, double dVel = 10);
	bool Move(int addr, double relDis, /*bool bIsRotating = false,*/ double dAcc = 100, double dDec = 100, double dVel = 10);
	bool MoveTo(int addr, double absDis, /*bool bIsRotating = false,*/ double dAcc = 100, double dDec = 100, double dVel = 10);
	void DoEvents(void);
	bool WaitMove(int addr);
	enControlStatus CheckStatus(int addr);
};

