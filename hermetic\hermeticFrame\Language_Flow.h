#pragma once

#include "Language_Universal.h"
//#include "FlowUniversalDefine.h"

enum e_Msg_Type
{
	eMsg_Normal,
	eMsg_Warning,
	eMsg_Err,
	eMsg_Type_Count,
};
enum e_Msg_Item
{
	//eMsg_SN=-1,
	eMsg_Reset_Finish=0,
	eMsg_Thread_Begin,
	eMsg_Thread_Exception_Exit,
	eMsg_Thread_Finish,
	eMsg_Cylinder_Back_Fail,
	eMsg_Pressure_Too_Big,
	eMsg_Pressure_Read_Time_Out,
	eMsg_Leak_Detector_Start_Cycle_Fail,
	eMsg_Leak_Detector_Reconnect_Fail,
	eMsg_Leak_Detector_Read_Result_Timeout,
	eMsg_Leak_Detector_Test_Fail,
	eMsg_Move_Protect_Trigger,
	eMsg_Scram_Trigger,
	eMsg_Set_Valve_Fail,
	eMsg_Home_Move_Fail,
	eMsg_Move_To_Press_Fail,
	eMsg_Test_Time,
	eMsg_COM_Open_Fail,
	eMsg_SN,
	eMsg_Leak_Detector_Not_Connect,
	eMsg_Leak_Detector_Connect_Success,
	eMsg_Leak_Detector_Add_Moudel_Failed,
	eMsg_Aixs_Z,									//2020-04-09
	eMsg_Mes_Send_Data_Fail,
	eMsg_Other,
	eMsg_Cylinder_Down_Fail,
	eMsg_Cylinder_Up_Fail,
	eMsg_Valcuo_Fail,
	eMsg_Break_Valcuo_Fail,
	eMsg_Door_Open,
	eMsg_Door_Close,
	eMsg_Item_Count,
};
class CLanguage_Flow
{
public:
	CLanguage_Flow(void);
	~CLanguage_Flow(void);

	CString			m_strMsg[eLang_Count][eMsg_Item_Count];

	void Set_Msg(e_Lang_Type eLang_Type);
};

