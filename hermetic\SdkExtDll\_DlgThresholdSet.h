#if !defined(AFX__DLGTHRESHOLDSET_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_)
#define AFX__DLGTHRESHOLDSET_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _DlgThresholdSet.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_DlgThresholdSet dialog
#ifdef SDK_WITH_VISION
#include "..\\VisionDll\\_MILApp.h"
#endif

#include "_DlgImage.h"	
#include "..\\InfomationDll_Include\\_Information.h"
#include "..\\InfomationDll_Include\\_InfoReadWrite.h"


class C_DlgThresholdSet : public CDialog
{
// Construction
public:

	void ChangeDisplay(int index, BOOL bBinarizeDisplay);
	HWND m_hWndDlgDebug;
	C_DlgThresholdSet(CWnd* pParent = NULL);   // standard constructor
	tag_ImgPara* pPosImgPara[2];

// Dialog Data
	//{{AFX_DATA(C_DlgThresholdSet)
	enum { IDD = IDD_DLG_THRESHOLD_SET };
	CComboBox	m_ComboMimArith;
	CSliderCtrl	m_sldThreshold1;
	CSliderCtrl	m_sldThreshold2;
	CButton	m_ChkMinArea;
	CButton	m_ChkMaxArea;
	CButton	m_ChkWhite;
	CButton	m_ChkBlack;
	CSliderCtrl	m_sldThreshold3;
	CSliderCtrl	m_sldThreshold4;
	CButton	m_ChkMinArea2;
	CButton	m_ChkMaxArea2;
	CButton	m_ChkWhite2;
	CButton	m_ChkBlack2;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_DlgThresholdSet)
	public:
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_DlgThresholdSet)
	virtual BOOL OnInitDialog();
	afx_msg void OnReleasedcaptureSLIDERThreshold1(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnReleasedcaptureSLIDERThreshold2(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnDestroy();
	afx_msg void OnChangeEDITMinRadius();
	afx_msg void OnChangeEditminarea();
	afx_msg void OnChangeEditmaxarea();
	afx_msg void OnChkBlack();
	afx_msg void OnChkWhite();
	virtual void OnCancel();
	afx_msg void OnCheckSkeleton();
	afx_msg void OnCheckBinarizeDisplay();
	afx_msg void OnChkBlack2();
	afx_msg void OnChkWhite2();
	afx_msg void OnChangeEditmaxarea2();
	afx_msg void OnChangeEditminarea2();
	afx_msg void OnChangeEDITMinRadius2();
	afx_msg void OnReleasedcaptureSliderthreshold3(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnReleasedcaptureSliderthreshold4(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnShowWindow(BOOL bShow, UINT nStatus);
	virtual void OnOK();
	afx_msg void OnSelchangeComboMimarith();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	C_DlgImage *pDlgImage[3];
	void FreeMilBuffer(int index);
	void FreeMilBuffer();
	BOOL bInit;
	double nFactor;

	long MilBlobBuffer[3];				// display the binary image, some times it's also the child buffer
//	long MilBlobDisplay[3];
	long MilChildBuffer[2];
//	long MilBlobOverlay[3];			//child buffer of overlay
#ifdef SDK_WITH_VISION
	CMilExtDisplay milDispBlob[3];
	tag_BlobParaInfo BlobParaInfo[3];		
	std::vector<BlobRltInfo>blobVector[3];
#endif
	void myBinarize(int index, CListCtrl *pListResult);
	BOOL bBinarizing[2];
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLGTHRESHOLDSET_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_)
