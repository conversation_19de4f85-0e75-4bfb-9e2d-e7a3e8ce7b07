#if !defined(AFX__DLGSETTING_H__0D81497F_8498_483B_8842_8D6CFC09353C__INCLUDED_)
#define AFX__DLGSETTING_H__0D81497F_8498_483B_8842_8D6CFC09353C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _DlgSetting.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_DlgSetting dialog
//#include "_ListCtrl.h"
#include "resource.h"//2010.12

// #include "..\\InfomationDll\\Include\\_Information.h"
// #include "..\\InfomationDll\\Include\\_InfoReadWrite.h"

class C_DlgSetting : public CDialog
{
// Construction
public:

	void InitPara(enGroupType sType, CString DataSource, int PosIndex);
	C_DlgSetting(CWnd* pParent = NULL);   // standard constructor
	
// Dialog Data
	//{{AFX_DATA(C_DlgSetting)
	enum { IDD = IDD_DLG_SETTING };
	C_ListCtrl	m_ListSetting;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_DlgSetting)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_DlgSetting)
	afx_msg void OnShowWindow(BOOL bShow, UINT nStatus);
	virtual BOOL OnInitDialog();
	virtual void OnOK();
	afx_msg void OnAllselect();
	afx_msg void OnSelectnone();
	afx_msg void OnDestroy();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	int nCurIndex;
	enGroupType mType;
	CString mDataSource;
public:
	afx_msg void OnLvnItemchangedListsetting(NMHDR *pNMHDR, LRESULT *pResult);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLGSETTING_H__0D81497F_8498_483B_8842_8D6CFC09353C__INCLUDED_)
