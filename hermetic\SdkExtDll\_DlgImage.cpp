// _DlgImage.cpp : implementation file
//

#include "stdafx.h"
#include "..\SdkExtDll_include\SdkExt.h"
#include "_DlgImage.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// C_DlgImage dialog


C_DlgImage::C_DlgImage(CWnd* pParent /*=NULL*/)
	: CDialog(C_DlgImage::IDD, pParent)
{
	//{{AFX_DATA_INIT(C_DlgImage)
	//}}AFX_DATA_INIT

	m_pView = NULL;	
	m_pFrame = NULL;
}


void C_DlgImage::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(C_DlgImage)
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(C_DlgImage, CDialog)
	//{{AFX_MSG_MAP(C_DlgImage)
	ON_WM_SIZE()
	ON_WM_DESTROY()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// C_DlgImage message handlers

BOOL C_DlgImage::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
	{
		CRect rectWndClient;
		GetClientRect(&rectWndClient);
//		rectWndClient = CRect(0,0,400,300);
		m_pFrame= new CFrameWnd();
		m_pFrame->Create(NULL,NULL,WS_VISIBLE|WS_CHILD,rectWndClient,this);
		
		CRuntimeClass *pViewClass=RUNTIME_CLASS(C_ImageView);
		
		m_pView=(C_ImageView*)pViewClass->CreateObject();
		
		m_pView->Create(NULL,NULL,WS_VISIBLE|WS_CHILD,rectWndClient,m_pFrame,123);
		m_pView->OnInitialUpdate();
		
//		pLblPicture = CreateLabel(m_pView, 1000,0,0,100,100,/*ES_LEFT|ES_CENTER|*/SS_BITMAP,"");
		pLblPicture = CreateLabel(m_pView, 1000,0,0,rectWndClient.Width(),rectWndClient.Height(),
			/*ES_LEFT|ES_CENTER|*/SS_BITMAP,"");
		
		SetWindowText("Image");
		nSizeX = nSizeY = 0;
	}
	
// 	HICON hIcon=AfxGetApp()->LoadIcon(IDR_MAINFRAME); 
// 	ASSERT(hIcon); 
// 	this->SendMessage(WM_SETICON,TRUE,(LPARAM) hIcon); 
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

void C_DlgImage::OnSize(UINT nType, int cx, int cy) 
{
	CDialog::OnSize(nType, cx, cy);
	
	// TODO: Add your message handler code here
	if (m_pFrame && m_pView)
	{
		m_pFrame->SetWindowPos(NULL,0,0,cx,cy,SWP_NOZORDER);
		m_pView->SetWindowPos(NULL,0,0,cx,cy,SWP_NOZORDER);	
		
		if(nSizeX != 0 && nSizeY != 0){
			m_pView->SetScrollSizes(MM_TEXT, CSize(nSizeX,nSizeY));	
		}
		else{			
			m_pView->SetScrollSizes(MM_TEXT, CSize(cx,cy));	
		}
	} 
}

void C_DlgImage::SetDisplayBuffer(long MilBuffer, long MilDisplay, double nFactor, CWnd *pWndDisplay)
{
#ifdef SDK_WITH_VISION
//	CString str;
//	str.Format("SetDisplayBuffer=%f",nFactor);
//	AfxMessageBox(str);
	m_pView->MilBuffer = MilBuffer;
	m_pView->MilDisplay = MilDisplay;
	m_pView->nFactor = nFactor;
	m_pView->pLblPicture = pWndDisplay;
	
	MbufInquire(MilBuffer, M_SIZE_X, &nSizeX);
	MbufInquire(MilBuffer, M_SIZE_Y, &nSizeY);	
 	m_pView->SetScrollSizes(MM_TEXT, CSize(nSizeX,nSizeY));	
//	MbufSave("c:\\1.bmp", MilBuffer);
	pLblPicture->MoveWindow(0, 0,nSizeX,nSizeY);
#endif
}

CStatic* C_DlgImage::CreateLabel(CWnd* pParentWnd, int nID, int OffSetX,int OffSetY,int Width,int Height, int nStyle,CString WindowText)
{
	CStatic *p_Label = new CStatic();
	ASSERT_VALID(p_Label);
	CRect rect(OffSetX,OffSetY,OffSetX+Width,OffSetY+Height);
	p_Label->Create(WindowText,/*WS_CHILD|WS_VISIBLE |SS_SUNKEN|*/ nStyle, rect, pParentWnd, nID ); 
	p_Label->ShowWindow(SW_SHOW);
	return p_Label;		
}

void C_DlgImage::OnDestroy() 
{
	CDialog::OnDestroy();
	
	// TODO: Add your message handler code here
	if(pLblPicture) delete pLblPicture;
}

