#if !defined(AFX__DLGBLOBSETTING_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_)
#define AFX__DLGBLOBSETTING_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _DlgBlobSetting.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_DlgBlobSetting dialog
#include "_DlgImage.h"

#ifdef SDK_WITH_VISION
#include "..\\VisionDll\\_MILApp.h"
#include "..\\VisionDll\\_Video.h"
#endif
#include "..\\InfomationDll_Include\\_Information.h"
#include "..\\ControlDll_Include\\_ControlMultiThread.h"

class C_DlgBlobSetting : public CDialog
{
// Construction
public:

//	tag_BlobParaInfo BlobPara;
	void ChangeDisplay(BOOL bBinarizeDisplay);
	BOOL bPan,bPanOnPicture;
	double nFactor;
	long dx,dy,x0,y0;
	double k;
	CPoint ptLast,ptDn,ptDlt,ptDlt0;
	HWND m_hWndDlgDebug;
	C_DlgBlobSetting(CWnd* pParent = NULL);   // standard constructor
	
// Dialog Data
	//{{AFX_DATA(C_DlgBlobSetting)
	enum { IDD = IDD_DLG_BLOBSETTING };
	CSliderCtrl	m_sldThreshold2;
	CButton	m_ChkMinArea;
	CButton	m_ChkMaxArea;
	CButton	m_ChkWhite;
	CButton	m_ChkBlack;
	CSliderCtrl	m_sldThreshold1;
	CListCtrl	m_ListResult;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_DlgBlobSetting)
	public:
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_DlgBlobSetting)
	virtual void OnOK();
	virtual BOOL OnInitDialog();
	afx_msg void OnReleasedcaptureSLIDERThreshold1(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnReleasedcaptureSLIDERThreshold2(NMHDR* pNMHDR, LRESULT* pResult);
	afx_msg void OnDestroy();
	afx_msg void OnChangeEDITMinRadius();
	afx_msg void OnChangeEditminarea();
	afx_msg void OnChangeEditmaxarea();
	afx_msg void OnChkBlack();
	afx_msg void OnChkWhite();
	afx_msg void OnBttest();
	afx_msg void OnMouseMove(UINT nFlags, CPoint point);
	virtual void OnCancel();
	afx_msg void OnPaint();
	afx_msg BOOL OnSetCursor(CWnd* pWnd, UINT nHitTest, UINT message);
	afx_msg void OnLButtonUp(UINT nFlags, CPoint point);
	afx_msg void OnCheckSkeleton();
	afx_msg void OnMenuImgZoomPan();
	afx_msg void OnButtonTemp();
	afx_msg void OnCheckBinarizeDisplay();
	afx_msg void OnBtOutput();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	C_DlgImage *pDlgImage;
	void FreeMilBuffer();
	BOOL bInit;
	BOOL bBinarizing;
	long MilBlobBuffer;				// display the binary image, some times it's also the child buffer
// 	long MilBlobDisplay;
	long MilChildBuffer;
// 	long MilBlobOverlay;			//child buffer of overlay
#ifdef SDK_WITH_VISION
	CMilExtDisplay milDispblob;
	tag_BlobParaInfo BlobParaInfo;		
	std::vector<BlobRltInfo>blobVector;
#endif
	HCURSOR	m_hCursor;				// Handle to cursor
	void myBinarize(CListCtrl *pListResult);
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLGBLOBSETTING_H__F6904677_E607_4F26_825B_1D9C68E6B585__INCLUDED_)
