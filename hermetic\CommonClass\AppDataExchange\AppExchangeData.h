// AppExchangeData.h: interface for the CAppExchangeData class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_APPEXCHANGEDATA_H__6CC5F625_173A_49A6_8A19_C923F9541845__INCLUDED_)
#define AFX_APPEXCHANGEDATA_H__6CC5F625_173A_49A6_8A19_C923F9541845__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class CAppExchangeData  
{
public:
	CAppExchangeData(CString strAppKeyName, CString strPath);
	virtual ~CAppExchangeData();
	enum enumAction
	{
		start = 1,
		close,
		package,
		unpakage,
		initializeContrl,
		initializeCamera,
		initFinished,
		CaptureImage,
		SaveImage,
		closeApp,
		encoderError,
		nothing
	};
	void SendMessage(enumAction action);
	BOOL ReciveMessage(WPARAM wParam, LPARAM lParam, enumAction &action);
	void writeApp(CString strAppRealName);
private:
	CString strDelimiter;
	CString strFileName;
	CString m_strAppKeyName;
	CString readAppName();
};

#endif // !defined(AFX_APPEXCHANGEDATA_H__6CC5F625_173A_49A6_8A19_C923F9541845__INCLUDED_)
