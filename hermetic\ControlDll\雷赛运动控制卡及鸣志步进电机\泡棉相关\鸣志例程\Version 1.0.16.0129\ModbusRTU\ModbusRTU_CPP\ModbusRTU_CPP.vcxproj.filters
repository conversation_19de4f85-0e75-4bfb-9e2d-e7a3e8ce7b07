﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="res\ModbusRTU_CPP.rc2">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\ModbusRTU_CPP.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="MOONS.png">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="ModbusRTU.dll" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ModbusRTU_CPP.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ModbusRTU_CPPDlg.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="stdafx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="targetver.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PngStatic.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="PosMode.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="QProgram.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Jog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="ModbusRTUHelper.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ModbusRTU_CPP.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ModbusRTU_CPPDlg.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="stdafx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PngStatic.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="PosMode.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="QProgram.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Jog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="ModbusRTUHelper.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="ModbusRTU_CPP.rc">
      <Filter>Resource Files</Filter>
    </ResourceCompile>
  </ItemGroup>
</Project>