// _CommonFun.h: interface for the C_CommonFun class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX__COMMONFUN_H__B97C78B4_E03D_4EA1_9875_DCB14993F2AC__INCLUDED_)
#define AFX__COMMONFUN_H__B97C78B4_E03D_4EA1_9875_DCB14993F2AC__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

class AFX_EXT_CLASS C_CommonFun  
{
public:
	C_CommonFun();
	virtual ~C_CommonFun();

	long HexToLong(CString strHex);
	CString LongToHex(long Num);
};

#endif // !defined(AFX__COMMONFUN_H__B97C78B4_E03D_4EA1_9875_DCB14993F2AC__INCLUDED_)
