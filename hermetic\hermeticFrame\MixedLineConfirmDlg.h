#pragma once
#include "afxwin.h"
#include "resource.h"

// 混线确认对话框类
class CMixedLineConfirmDlg : public CDialog
{
	DECLARE_DYNAMIC(CMixedLineConfirmDlg)

public:
	CMixedLineConfirmDlg(CWnd* pParent = NULL);   // 标准构造函数
	virtual ~CMixedLineConfirmDlg();

// 对话框数据
	enum { IDD = IDD_MIXED_LINE_CONFIRM };

public:
	// 设置对话框显示信息
	void SetMixedLineInfo(const CString& strCurrentModel, 
						  const CString& strTargetModel,
						  const CString& strSeriesModel,
						  const CString& strPcbaCode);
	
	// 新增：设置系统状态信息
	void SetSystemStatus(bool bAllStationsIdle, const CString& strStatusMsg);
	void UpdateWaitingStatus(const CString& strWaitMsg);
	void UpdateStatusDisplay();  // 更新状态显示

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV 支持
	virtual BOOL OnInitDialog();

	DECLARE_MESSAGE_MAP()

public:
	afx_msg void OnBnClickedBtnConfirmSwitch();
	afx_msg void OnBnClickedBtnCancelSwitch();

private:
	CString m_strCurrentModel;
	CString m_strTargetModel;
	CString m_strSeriesModel;
	CString m_strPcbaCode;
	
	// 新增：系统状态相关
	bool m_bAllStationsIdle;
	CString m_strStatusMessage;
	CString m_strWaitMessage;

	// 控件变量
	CStatic m_staticCurrentModel;
	CStatic m_staticTargetModel;
	CStatic m_staticSeriesModel;
	CStatic m_staticPcbaCode;
}; 