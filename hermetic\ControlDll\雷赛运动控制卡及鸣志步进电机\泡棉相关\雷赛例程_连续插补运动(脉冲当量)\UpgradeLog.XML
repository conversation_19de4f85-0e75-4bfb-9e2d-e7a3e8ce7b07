<?xml version="1.0" encoding="UTF-8"?><?xml-stylesheet type='text/xsl' href='_UpgradeReport_Files/UpgradeReport.xslt'?><UpgradeLog>
<Properties><Property Name="Solution" Value="连续插补运动(脉冲当量)">
</Property><Property Name="解决方案文件" Value="E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\连续插补运动(脉冲当量).sln">
</Property><Property Name="用户选项文件" Value="E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\连续插补运动(脉冲当量).suo">
</Property><Property Name="Date" Value="2014年12月11日">
</Property><Property Name="Time" Value="15:47">
</Property></Properties><Event ErrorLevel="0" Project="" Source="连续插补运动(脉冲当量).sln" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量).sln">
</Event><Event ErrorLevel="0" Project="" Source="连续插补运动(脉冲当量).suo" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量).suo">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\连续插补运动(脉冲当量).csproj" Description="项目文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\连续插补运动(脉冲当量).csproj">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Form1.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Form1.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Form1.Designer.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Form1.Designer.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\LTDMC.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\LTDMC.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Program.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Program.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Properties\AssemblyInfo.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Properties\AssemblyInfo.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Properties\Resources.Designer.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Properties\Resources.Designer.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Properties\Settings.Designer.cs" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Properties\Settings.Designer.cs">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Properties\Settings.settings" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Properties\Settings.settings">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Form1.resx" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Form1.resx">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\Properties\Resources.resx" Description="文件已成功备份为 E:\DMC5801\例程\C#\例18_连续插补运动(脉冲当量)\Backup\连续插补运动(脉冲当量)\Properties\Resources.resx">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\连续插补运动(脉冲当量).csproj" Description="项目已成功转换">
</Event><Event ErrorLevel="3" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\连续插补运动(脉冲当量).csproj" Description="Converted">
</Event><Event ErrorLevel="0" Project="" Source="连续插补运动(脉冲当量).sln" Description="成功转换解决方案">
</Event><Event ErrorLevel="3" Project="" Source="连续插补运动(脉冲当量).sln" Description="Converted">
</Event><Event ErrorLevel="0" Project="连续插补运动(脉冲当量)" Source="连续插补运动(脉冲当量)\连续插补运动(脉冲当量).csproj" Description="扫描完成: 项目文件不需要升级。">
</Event></UpgradeLog>