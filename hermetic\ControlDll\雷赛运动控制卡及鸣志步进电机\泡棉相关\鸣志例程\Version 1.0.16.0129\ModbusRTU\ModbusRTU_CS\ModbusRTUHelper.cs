﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.InteropServices;

namespace ModbusRTU_CS
{
	public delegate void CSCallback();

	public struct StructRegisterValue
	{
		public int Count;
		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 100)]
		public int[] Values;
	};

	public struct StructCommand
	{
		public int Count;
		[MarshalAs(UnmanagedType.ByValArray, SizeConst = 1024)]
		public byte[] Values;
	};

	public enum SCLCommandEncodingTable
	{
		AlarmReset = 0xBA,
		StartJogging = 0x96,
		StopJogging = 0xD8,
		EncoderFunction = 0xD6,
		EncoderPosition = 0x98,
		FeedtoDoubleSensor = 0x69,
		FollowEncoder = 0xCC,
		FeedtoLength = 0x66,
		FeedtoSensorwithMaskDistance = 0x6A,
		FeedandSetOutput = 0x68,
		FeedtoPosition = 0x67,
		FeedtoSensor = 0x6B,
		FeedtoSensorwithSafetyDistance = 0x6C,
		JogDisable = 0xA3,
		JogEnable = 0xA2,
		MotorDisable = 0x9E,
		MotorEnable = 0x9F,
		SeekHome = 0x6E,
		SetPosition = 0xA5,
		FilterInput = 0xC0,
		FilterSelectInputs = 0xD3,
		StepFilterFreq = 0x06,
		AnalogDeadband = 0xD2,
		AlarmResetInput = 0x46,
		AlarmOutput = 0x47,
		AnalogScaling = 0xD1,
		DefineLimits = 0x42,
		SetOutput = 0x8B,
		WaitforInput = 0x70,
		QueueLoadAndExecute = 0x78,
		WaitTime = 0x6F,
		StopMoveAndKillBuffer = 0xE1,
		StopMoveAndKillBufferWithNormalDecel = 0xE2,
	}

	public class ModbusRTUHelper
	{
		#region public fields
		public int MBERROR_OK = 0x00; // OK
		public int MBERROR_TIMEOUT = 0x01; // Command time out				?1
		public int MBERROR_PARAMETERISTOOLONG = 0x02; // Parameter is too long			?2
		public int MBERROR_TOOFEWPARAMETERS = 0x03; // Too few parameters				?3
		public int MBERROR_TOOMANYPARAMETERS = 0x04; // Too many parameters			?4
		public int MBERROR_PARAMETEROUTOFRANGE = 0x05; // Parameter out of range			?5
		public int MBERROR_CommandBufferFull = 0x06; // Command buffer full			?6
		public int MBERROR_CANNOTPROCESSCOMMAND = 0x07; // Cannot process command			?7
		public int MBERROR_PROGRAMRUNNING = 0x08; // Program running				?8
		public int MBERROR_BADPASSWORD = 0x09; // Bad password					?9
		public int MBERROR_COMMPORTERROR = 0x0A; // Comm port error				?10
		public int MBERROR_BADCHARACTER = 0x0B; // Bad character					?11
		public int MBERROR_IOALREADYUSED = 0x0C; // IO already used				?12
		public int MBERROR_INCORRECTIOCONFIGURATION = 0x0D;  // Incorrect IO Configuration		?13
		public int MBERROR_INCORRECTIOFUNCTION = 0x0D; // Incorrect IO function			?14
		public int MBERROR_OPENPORTFAILED = 0x64; // Fail to OpenPort
		public int MBERROR_PORTISNOTOPEN = 0x65;// Serial Port is not open
		public int MBERROR_NORESPONSE = 0x66;// Drive did not response
		public int MBERROR_INCORRECTRESPONSE = 0x67; // Drive's response is incorrect
		public int MBERROR_CHECKSUMERROR = 0x68;  // CheckSumError
		#endregion

		#region DllImport

		[DllImport(@"ModbusRTU.dll", EntryPoint = "IsOpen", CharSet = CharSet.Auto)]
		private static extern bool _IsOpen();

		[DllImport(@"ModbusRTU.dll", EntryPoint = "OpenPort", CharSet = CharSet.Auto)]
		private static extern int _OpenPort(int nPort, int baudRate, bool bigEndian = true);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ClosePort", CharSet = CharSet.Auto)]
		private static extern int _ClosePort(int nPort);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ExecuteCommand", CharSet = CharSet.Auto)]
		private static extern int _ExecuteCommand(int nPort, [MarshalAs(UnmanagedType.LPArray, SizeConst = 1024)]byte[] lpCommand, int nCommandLen,int timeOut = 50);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ReadSingleHoldingRegister", CharSet = CharSet.Auto)]
		private static extern int _ReadSingleHoldingRegister(int nPort, byte nSlaveAddress, int registerNo, ref int value, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ReadMultiHoldingRegisters", CharSet = CharSet.Auto)]
		private static extern int _ReadMultiHoldingRegisters(int nPort, byte nSlaveAddress, int registerNo, int count, IntPtr ptrHoldingRegisterStu, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "WriteMultiHoldingRegisters", CharSet = CharSet.Auto)]		
		private static extern int _WriteMultiHoldingRegisters(int nPort, byte nSlaveAddress, int registerNo, int count, [MarshalAs(UnmanagedType.LPArray, SizeConst=100)]int[] valueList, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "GetLastCommandSend", CharSet = CharSet.Auto)]
		private static extern int _GetLastCommandSend(int nPort, IntPtr ptrCommandStu);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "GetLastCommandReceived", CharSet = CharSet.Auto)]
		private static extern int _GetLastCommandReceived(int nPort, IntPtr ptrCommandStu);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ClearSendBuffer", CharSet = CharSet.Auto)]
		private static extern int _ClearSendBuf(int nPort);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ClearReceivedBuffer", CharSet = CharSet.Auto)]
		private static extern int _ClearReceivedBuf(int nPort);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ClearBuffer", CharSet = CharSet.Auto)]
		private static extern int _ClearBuffer(int nPort);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ReadSingleSCLRegister", CharSet = CharSet.Auto)]
		private static extern int _ReadSingleSCLRegister(int nPort, byte nSlaveAddress, int sclRegisterAddress, ref int nValue, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "WriteSingleSCLRegister", CharSet = CharSet.Auto)]
		private static extern int _WriteSingleSCLRegister(int nPort, byte nSlaveAddress, int sclRegisterAddress, int nValue, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ReadMultiSCLRegisters", CharSet = CharSet.Auto)]
		private static extern int _ReadMultiSCLRegisters(int nPort, byte nSlaveAddress, int sclRegisterAddress, int nCount, IntPtr ptrRegisterValue, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "WriteMultiSCLRegisters", CharSet = CharSet.Auto)]
		private static extern int _WriteMultiSCLRegisters(int nPort, byte nSlaveAddress, int sclRegisterAddress, int nCount, [MarshalAs(UnmanagedType.LPArray, SizeConst=100)]int[] valueList, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "ExecuteCommandWithOpcode", CharSet = CharSet.Auto)]
		private static extern int _ExecuteCommandWithOpcode(int nPort, byte nSlaveAddress, int opCode, int p1 = 0, int p2 = 0, int p3 = 0, int p4 = 0, int p5 =0, int nTimeOut = 60);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "SetPointtoPointMoveParam", CharSet = CharSet.Auto)]
		private static extern int _SetPointtoPointMoveParam(int nPort, byte nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nDistance, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "FeedtoLength", CharSet = CharSet.Auto)]
		private static extern int _FeedtoLength(int nPort, byte nSlaveAddress, int nDistance, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "FeedtoPosition", CharSet = CharSet.Auto)]
		private static extern int _FeedtoPosition(int nPort, byte nSlaveAddress, int nDistance, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "SetJogMoveParam", CharSet = CharSet.Auto)]
		private static extern int _SetJogMoveParam(int nPort, byte nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "StartJogging", CharSet = CharSet.Auto)]
		private static extern int _StartJogging(int nPort, byte nSlaveAddress, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "StopJogging", CharSet = CharSet.Auto)]
		private static extern int _StopJogging(int nPort, byte nSlaveAddress, int nTimeOut = 100);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "OnDataSend", CharSet = CharSet.Auto)]
		private static extern void _OnDataSend(CSCallback callback);

		[DllImport(@"ModbusRTU.dll", EntryPoint = "OnDataReceived", CharSet = CharSet.Auto)]
		private static extern void _OnDataReceived(CSCallback callback);

		#endregion

		#region Public Methods

		private static CSCallback m_DataSendCallback;

		private static CSCallback m_DataReceivedCallback;

		public delegate void OnDataSendOrReceivedEventHandler(EventArgs e);

		public event OnDataSendOrReceivedEventHandler DataSend;

		public event OnDataSendOrReceivedEventHandler DataReceived;

		public void DataSendCallbackFunction()
		{
			if (DataSend != null)
			{
				EventArgs e = new EventArgs();
				DataSend(e);
			}
		}

		public void DataReceivedCallbackFunction()
		{
			if (DataReceived != null)
			{
				EventArgs e = new EventArgs();
				DataReceived(e);
			}
		}

		public ModbusRTUHelper()
		{
			m_DataSendCallback = DataSendCallbackFunction;
			_OnDataSend(m_DataSendCallback);

			m_DataReceivedCallback = DataReceivedCallbackFunction;
			_OnDataReceived(m_DataReceivedCallback);
		}

		private bool m_IsOpen = false;
		public bool IsOpen
		{
			get
			{
				return m_IsOpen;
			}
		}

		private int m_Port = 1;
		public int Port
		{
			get
			{
				return m_Port;
			}
		}
		
		public int OpenPort(int port, int baudRate, bool bigEndian = true)
		{
			m_Port = port;
			int ret = _OpenPort(m_Port, baudRate, bigEndian);
			if (ret == MBERROR_OK)
			{
				m_IsOpen = true;
			}
			return ret;
		}

		public int ClosePort()
		{
			int ret = _ClosePort(m_Port);
			m_IsOpen = false;
			return ret;
		}

		~ModbusRTUHelper()
		{
			_ClosePort(m_Port);
		}

		public int ExecuteCommand(byte[] lpCommand, int timeOut = 50)
		{
			return _ExecuteCommand(m_Port, lpCommand, lpCommand.Length,timeOut);
		}
		
		public int ReadSingleHoldingRegister(byte nSlaveAddress, int registerNo, ref int value, int nTimeOut = 60)
		{
			return _ReadSingleHoldingRegister(m_Port, nSlaveAddress, registerNo, ref value, nTimeOut);
		}

		public int ReadMultiHoldingRegisters(byte nSlaveAddress, int registerNo, int count, ref List<int> valueList, int nTimeOut = 100)
		{
			IntPtr ptrHoldingRegisterStu = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(StructRegisterValue)));
			int ret = _ReadMultiHoldingRegisters(m_Port, nSlaveAddress, registerNo, count, ptrHoldingRegisterStu, nTimeOut);
			StructRegisterValue holdingRegisterStu = (StructRegisterValue)Marshal.PtrToStructure((IntPtr)((UInt32)ptrHoldingRegisterStu), typeof(StructRegisterValue));

			for (int i = 0; i < holdingRegisterStu.Count; i++)
			{
				valueList.Add(holdingRegisterStu.Values[i]);
			}
			Marshal.FreeHGlobal(ptrHoldingRegisterStu);

			return ret;
		}

		public int WriteMultiHoldingRegisters(byte nSlaveAddress, int registerNo, int count, List<int> lstValue, int nTimeOut = 100)
		{
			int[] valueList = lstValue.ToArray();
			int ret = _WriteMultiHoldingRegisters(m_Port, nSlaveAddress, registerNo, count, valueList, nTimeOut);			

			return ret;
		}


		public void GetLastCommandSend(ref List<byte> commandList)
		{
			IntPtr ptrCommandStu = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(StructCommand)));
			int ret = _GetLastCommandSend(m_Port, ptrCommandStu);
			StructCommand commandStu = (StructCommand)Marshal.PtrToStructure((IntPtr)((UInt32)ptrCommandStu), typeof(StructCommand));

			for (int i = 0; i < commandStu.Count; i++)
			{
				commandList.Add(commandStu.Values[i]);
			}
			Marshal.FreeHGlobal(ptrCommandStu);
		}


		public void GetLastCommandReceived(ref List<byte> commandList)
		{
			IntPtr ptrCommandStu = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(StructCommand)));
			int ret = _GetLastCommandReceived(m_Port, ptrCommandStu);
			StructCommand commandStu = (StructCommand)Marshal.PtrToStructure((IntPtr)((UInt32)ptrCommandStu), typeof(StructCommand));

			for (int i = 0; i < commandStu.Count; i++)
			{
				commandList.Add(commandStu.Values[i]);
			}
			Marshal.FreeHGlobal(ptrCommandStu);
		}

		public void ClearSendBuffer()
		{
			_ClearSendBuf(m_Port);
		}

		public void ClearReceivedBuffer()
		{
			_ClearReceivedBuf(m_Port);
		}

		public void ClearBuffer()
		{
			_ClearBuffer(m_Port);
		}

		public int ReadSingleSCLRegister(byte nSlaveAddress, int sCLRegisterAddress, ref int value, int nTimeOut = 60)
		{
			return _ReadSingleSCLRegister(m_Port, nSlaveAddress, sCLRegisterAddress, ref value, nTimeOut);
		}

		public int WriteSingleSCLRegister(byte nSlaveAddress, int sCLRegisterAddress, int value, int nTimeOut = 60)
		{
			return _WriteSingleSCLRegister(m_Port, nSlaveAddress, sCLRegisterAddress, value, nTimeOut);
		}

		public int ReadMultiSCLRegisters(byte nSlaveAddress, int nSCLRegisterAddress, int nCount, ref List<int> registerValueList, int nTimeOut = 60)
		{

			IntPtr ptrHoldingRegisterStu = Marshal.AllocHGlobal(Marshal.SizeOf(typeof(StructRegisterValue)));
			int ret = _ReadMultiSCLRegisters(m_Port, nSlaveAddress, nSCLRegisterAddress, nCount, ptrHoldingRegisterStu, nTimeOut);
			StructRegisterValue holdingRegisterStu = (StructRegisterValue)Marshal.PtrToStructure((IntPtr)((UInt32)ptrHoldingRegisterStu), typeof(StructRegisterValue));

			for (int i = 0; i < holdingRegisterStu.Count; i++)
			{
				registerValueList.Add(holdingRegisterStu.Values[i]);
			}
			Marshal.FreeHGlobal(ptrHoldingRegisterStu);

			if (ret > 0 || holdingRegisterStu.Count != nCount)
			{

			}

			return ret;
		}

		public int WriteMultiSCLRegisters(byte nSlaveAddress, int sCLRegisterAddress, int nCount, List<int> valueList,  int nTimeOut = 60)
		{
			return _WriteMultiSCLRegisters(m_Port, nSlaveAddress, sCLRegisterAddress, nCount, valueList.ToArray(), nTimeOut);
		}

		public int ExecuteCommandWithOpcode(byte nSlaveAddress, SCLCommandEncodingTable commandCode, int p1 = 0, int p2 = 0, int p3 = 0, int p4 = 0, int p5 = 0, int nTimeOut = 60)
		{
			return _ExecuteCommandWithOpcode(m_Port, nSlaveAddress, (int)commandCode, p1, p2, p3, p4, p5, nTimeOut);
		}

		public int SetPointtoPointMoveParam(byte nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nDistance, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _SetPointtoPointMoveParam(m_Port, nSlaveAddress, dAccel, dDecel, dVelocity, nDistance, nTimeOut);
			return ret;
		}

		public int FeedtoLength(byte nSlaveAddress, int nDistance, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _FeedtoLength(m_Port, nSlaveAddress, nDistance, nTimeOut);
			return ret;
		}

		public int FeedtoPosition(byte nSlaveAddress, int nDistance, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _FeedtoPosition(m_Port, nSlaveAddress, nDistance, nTimeOut);
			return ret;
		}

		public int SetJogMoveParam(byte nSlaveAddress, double dAccel, double dDecel, double dVelocity, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _SetJogMoveParam(m_Port, nSlaveAddress, dAccel, dDecel, dVelocity, nTimeOut);
			return ret;
		}

		public int StartJogging(byte nSlaveAddress, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _StartJogging(m_Port, nSlaveAddress, nTimeOut);
			return ret;
		}

		public int StopJogging(byte nSlaveAddress, int nTimeOut = 100)
		{
			int ret = MBERROR_OK;
			ret = _StopJogging(m_Port, nSlaveAddress, nTimeOut);
			return ret;
		}

		#endregion
	}
}