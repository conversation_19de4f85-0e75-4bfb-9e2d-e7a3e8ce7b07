#if !defined(AFX__DLG2IMAGEARITH_H__CCCA1A1E_ABD7_4D31_9603_480927384D2C__INCLUDED_)
#define AFX__DLG2IMAGEARITH_H__CCCA1A1E_ABD7_4D31_9603_480927384D2C__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _Dlg2ImageArith.h : header file
//

#include "_DlgImage.h"

/////////////////////////////////////////////////////////////////////////////
// C_Dlg2ImageArith dialog

class C_Dlg2ImageArith : public CDialog
{
// Construction
public:

	C_Dlg2ImageArith(CWnd* pParent = NULL);   // standard constructor

// Dialog Data
	//{{AFX_DATA(C_Dlg2ImageArith)
	enum { IDD = IDD__DLG2IMAGEARITH };
	CComboBox	m_ComboNo2;
	CComboBox	m_ComboNo1;
	CComboBox	m_ComboMimArith;
	//}}AFX_DATA


// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_Dlg2ImageArith)
	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	//}}AFX_VIRTUAL

// Implementation
protected:

	// Generated message map functions
	//{{AFX_MSG(C_Dlg2ImageArith)
	virtual BOOL OnInitDialog();
	afx_msg void OnSelchangeComboMimarith();
	afx_msg void OnDestroy();
	afx_msg void OnShowWindow(BOOL bShow, UINT nStatus);
	virtual void OnOK();
	afx_msg void OnBtOpen1();
	afx_msg void OnBtOpen2();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
private:
	C_DlgImage *pDlgImage[3];
#ifdef SDK_WITH_VISION
//	long MilOverlay[3];
//	long MilDisplay[3];
	long MilBuffer[3];
	CMilExtDisplay milDisp[3];
#endif
	double nFactor;
};

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__DLG2IMAGEARITH_H__CCCA1A1E_ABD7_4D31_9603_480927384D2C__INCLUDED_)
