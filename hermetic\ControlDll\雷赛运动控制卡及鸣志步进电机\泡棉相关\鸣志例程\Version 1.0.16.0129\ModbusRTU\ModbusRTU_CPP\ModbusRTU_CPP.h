
// ModbusRTU_CPP.h : main header file for the PROJECT_NAME application
//

#pragma once

#ifndef __AFXWIN_H__
	#error "include 'stdafx.h' before including this file for PCH"
#endif

#include "resource.h"		// main symbols


// CModbusRTU_CPPApp:
// See ModbusRTU_CPP.cpp for the implementation of this class
//

class CModbusRTU_CPPApp : public CWinApp
{
public:
	CModbusRTU_CPPApp();

// Overrides
public:
	virtual BOOL InitInstance();

// Implementation

	DECLARE_MESSAGE_MAP()
};

extern CModbusRTU_CPPApp theApp;