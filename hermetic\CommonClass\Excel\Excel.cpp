// Excel.cpp: implementation of the CExcel class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "Excel.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

CExcel::CExcel()
{
	m_strIdentifier = "";
	m_bUseOCR = FALSE;
	m_bUseSTS = FALSE;
	m_bUseMISI = FALSE;
	m_bUseGuzik = FALSE;
	m_bHGA = TRUE;
	m_bAuto = FALSE;
}

CExcel::~CExcel()
{

}

BOOL CExcel::AttachToExcel(BOOL bCreateNew/* = FALSE*/)
{
	if(!AttachToApplication(bCreateNew) ||
	   !AttachToWorkbooks()) return FALSE;

	return TRUE;	
}

void CExcel::ShowExcel()
{
	m_app.SetVisible(TRUE);
}

BOOL CExcel::NewWorkbook()
{
	if(LPDISPATCH pDisp = m_wbs.Add(COleVariant((short)1)))
	{
		m_wb.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

void CExcel::ReleaseExcel()
{
	m_app.ReleaseDispatch();
	m_wbs.ReleaseDispatch();
	m_wb.ReleaseDispatch();
	m_wss.ReleaseDispatch();
	m_ws.ReleaseDispatch();
	m_rng.ReleaseDispatch();
}

BOOL CExcel::AttachToApplication(BOOL bCreateNew/*=FALSE*/)
{
	if(bCreateNew) return m_app.CreateDispatch(_T("Excel.Application"));

	LPDISPATCH	pDisp;
	LPUNKNOWN	pUnk;
	CLSID		clsid;

	if(::CLSIDFromProgID(L"Excel.Application", &clsid)!=S_OK) return FALSE;
	if(GetActiveObject(clsid, NULL, &pUnk)==S_OK)
	{
		if(pUnk->QueryInterface(IID_IDispatch, (void**)&pDisp)!=S_OK)
		{
			pUnk->Release();
			return FALSE;
		}
		m_app.AttachDispatch(pDisp);
		pUnk->Release();
	}
	else
		if(!m_app.CreateDispatch(_T("Excel.Application"))) return FALSE;

	return TRUE;
}

BOOL CExcel::AttachToWorkbooks()
{
	if(LPDISPATCH pDisp = m_app.GetWorkbooks())
	{
		m_wbs.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::AttachToWorksheets()
{
	if(LPDISPATCH pDisp = m_wb.GetWorksheets())
	{
		m_wss.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::OpenFile(CString strPath)
{
	CFileFind ff;
	BOOL bFound = ff.FindFile(strPath);
	if(!bFound) return FALSE;

	if(strPath=="") return FALSE;

	VARIANT varOpt; 
    varOpt.vt = VT_ERROR; 
    varOpt.scode = DISP_E_PARAMNOTFOUND; 

	if(LPDISPATCH pDisp = m_wbs.Open(strPath, varOpt, varOpt, varOpt, varOpt, varOpt,
									 varOpt, varOpt, varOpt, varOpt, varOpt,
									 varOpt, varOpt))
	//if(LPDISPATCH pDisp = m_wbs.Open(strPath, COleVariant(), COleVariant(), COleVariant(), COleVariant(), COleVariant(),
	//								 COleVariant(), COleVariant(), COleVariant(), COleVariant(), COleVariant(),
	//								 COleVariant(), COleVariant()))
	//if(LPDISPATCH pDisp = m_wbs.Open(strPath))
	{
		m_wb.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::GetWorkbook(CString strName)
{
	for(long n=1; n<=m_wbs.GetCount(); n++)
	{
		if(LPDISPATCH pDisp = m_wbs.GetItem(COleVariant((short)n)))
		{
			m_wb.AttachDispatch(pDisp);
			CString str = m_wb.GetName();
			str.MakeUpper();
			strName.MakeUpper();
			if(str==strName)
			{
				m_wb.Activate();
				return TRUE;
			}
		}
	}
	
	return FALSE;
}

BOOL CExcel::GetWorksheet(CString strName)
{
	if(!AttachToWorksheets()) return FALSE;

	for(long n=1; n<=m_wss.GetCount(); n++)
	{
		if(LPDISPATCH pDisp = m_wss.GetItem(COleVariant((short)n)))
		{
			m_ws.AttachDispatch(pDisp);
			CString str = m_ws.GetName();
			str.MakeUpper();
			str.TrimLeft();
			str.TrimRight();
			strName.MakeUpper();
			if(str==strName)
			{
				m_ws.Activate();
				return TRUE;
			}
		}
	}
	
	return FALSE;
}

BOOL CExcel::SetValue(long nRow, long nCol, double dValue)
{
	CString str = XRange(nRow, nCol);
	if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(str), COleVariant(str)))
	//if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(XRange(nRow, nCol))))
	{
		m_rng.AttachDispatch(pDisp);
		m_rng.SetValue(COleVariant(dValue));
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::SetValue(long nRow, long nCol, CString strValue)
{
	CString str = XRange(nRow, nCol);
	if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(str), COleVariant(str)))
		//if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(XRange(nRow, nCol))))
	{
		m_rng.AttachDispatch(pDisp);
		m_rng.SetValue(COleVariant(strValue));
		return TRUE;
	}
	else
		return FALSE;
}

CString CExcel::XCol(long nCol)
{
	CString str = __toascii(65 + (nCol - 1) % 26);
	long nNo = (nCol - 1) / 26;
	if(nNo>0) str = CString(__toascii(65 + nNo - 1)) + str;

	return str;
}

CString CExcel::XRange(long nRow, long nCol)
{
	CString str;
	str.Format("%d", nRow);
	return XCol(nCol) + str;
}

CString CExcel::XRange(long nStartRow, long nStartCol, long nStopRow, long nStopCol)
{
	CString strRes = "";
	CString str;

	if(nStartCol!=-1) strRes += XCol(nStartCol);
	if(nStartRow!=-1)
	{
		str.Format("%d", nStartRow);
		strRes += str;
	}
	strRes += ":";
	if(nStopCol!=-1) strRes += XCol(nStopCol);
	if(nStopRow!=-1)
	{
		str.Format("%d", nStopRow);
		strRes += str;
	}

	return strRes;
}


CString CExcel::XRow(long nRow)
{
	CString str;
	str.Format("%d", nRow);
	return str;
}

CString CExcel::XRow(long nRow1, long nRow2)
{
	CString str;
	str.Format("%d:%d", nRow1, nRow2);
	return str;
}

BOOL CExcel::Search(long &nRow, long &nCol, long nStartRow, long nStopRow, long nStartCol, long nStopCol, CString strSearch)
{
	for(long nR=nStartRow; nR<nStopRow; nR++)
		for(long nC=nStartCol; nC<nStopCol; nC++)
		{
			CString str;
			if(GetStr(str, nR, nC) && str==strSearch)
			{
				nRow = nR;
				nCol = nC;
				return TRUE;
			}
		}	
	return FALSE;
}

BOOL CExcel::GetStr(CString &str, long nRow, long nCol)
{
	CString s = XRange(nRow, nCol);
	if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(s), COleVariant(s)))
	//if(LPDISPATCH pDisp = m_ws.GetRange(COleVariant(XRange(nRow, nCol))))
	{
		m_rng.AttachDispatch(pDisp);
		str = m_rng.GetText().bstrVal;
		return TRUE;
	}
	else
		return FALSE;
}


long CExcel::GetPos(CString* pTL, long nNoOfTitle, CString str)
{
	if(str=="_ProductName") return 0;
	if(str=="_TesterNo") return 1;
	if(str=="_TestTime") return 2;
	if(str=="_SerialNo") return 3;

	if(str=="_WO") return 4;
	if(str=="_Time") return 5;
	if(str=="_DateTime") return 6;
	if(str=="_WAFNO") return 7;
	if(str=="_BAR_CHIP") return 8;
	if(str=="_HeadDepFailedItems") return 9;
	if(str=="_HeadIndepFailedItems") return 10;
	if(str=="_Actuator") return 11;
	if(str=="_HeadResult") return 12;
	if(str=="_QS_Equip") return 13;
	if(str=="_TstrID") return 14;
	if(str=="_DtEquip") return 15;

	if(str=="_sSN") return 4;
	if(str=="_sWO") return 5;
	if(str=="_sDate") return 6;
	if(str=="_sTime") return 7;
	if(str=="_sTstrID") return 8;
	if(str=="_sPrdct") return 9;
	if(str=="_sPltfrm") return 10;
	if(str=="_sChip") return 11;
	if(str=="_sSetup") return 12;
	if(str=="_sIbiasOld") return 13;
	if(str=="_sActuator") return 14;
	if(str=="_sStandardName") return 15;
	if(str=="_sIbias") return 16;
	if(str=="_sHeadResult") return 17;
	if(str=="_sHeadStdName") return 18;
	if(str=="_sHeadDepItems") return 19;
	if(str=="_sHeadIndepItems") return 20;

	if(str=="_iWO") return 4;
	if(str=="_iTester") return 5;
	if(str=="_iTstrID") return 6;

	if(str=="_gHeadStackSN") return 4;
	if(str=="_gDiskPackSN") return 5;
	if(str=="_gDeviceID") return 6;
	if(str=="_eLotNo") return 7;
	if(str=="_eEC") return 8;
	if(str=="_eDiskSN") return 9;

	long nOffset = 4;
	if(m_bUseOCR) nOffset = 16;
	if(m_bUseSTS) nOffset = 21;
	if(m_bUseMISI) nOffset = 7;
	if(m_bUseGuzik) nOffset = 10;
	for(long n=0; n<nNoOfTitle; n++) if(pTL[n]==str) return n+nOffset;

	return -1;
}

CString CExcel::GetName(CString strFileName)
{
	int n = strFileName.Find(".");
	if(n==-1) return strFileName;

	return strFileName.Mid(0, n);
}

CString CExcel::XCol(long nCol1, long nCol2)
{
	return XCol(nCol1) + ":" + XCol(nCol2);
}

void CExcel::HideExcel()
{
	m_app.SetVisible(FALSE);
}

void CExcel::CloseFile(CString strWbName, CString strPath)
{
	if(GetWorkbook(strWbName)) m_wb.Close(COleVariant((short)0), COleVariant(strPath), COleVariant());
//	if(GetWorkbook(strWbName)) m_wb.Close(COleVariant((short)0));
}

void CExcel::SetIdentifier(CString str)
{
	m_strIdentifier = str;
	m_strIdentifier.MakeUpper();
}
BOOL CExcel::GetActiveWorkbook()
{
	if(LPDISPATCH pDisp = m_app.GetActiveWorkbook())
	{
		m_wb.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::GetActiveWorksheet()
{
	if(LPDISPATCH pDisp = m_app.GetActiveSheet())
	{
		m_ws.AttachDispatch(pDisp);
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::GetActiveCellPos(long &nRow, long &nCol)
{
	if(LPDISPATCH pDisp = m_app.GetActiveCell())
	{
		m_rng.AttachDispatch(pDisp);
		nRow = m_rng.GetRow();
		nCol = m_rng.GetColumn();
		return TRUE;
	}
	else
		return FALSE;
}

BOOL CExcel::SetValues(double *dVal, long nVal, long nRow, long nCol)
{
	for(long n=nRow; n<nRow+nVal; n++)
		if(dVal[n-nRow]!=-8888)
		{
			if(!SetValue(n, nCol, dVal[n-nRow])) return FALSE;
		}
	//	else
	//		if(!ClearContents(n, nCol)) return FALSE;

	return TRUE;
}

BOOL CExcel::Save(CString strPath, CString strWb)
{
	if(!GetWorkbook(strWb)) return FALSE;
	 
	m_wb.SaveAs(COleVariant(strPath), COleVariant((short)1), COleVariant(), COleVariant(),
				COleVariant(), COleVariant(), 0, COleVariant(), COleVariant(),
				COleVariant(), COleVariant());

	return TRUE;
}

void CExcel::Close(CString strPath)
{
	m_wb.Close(COleVariant((short)0), COleVariant(strPath), COleVariant());
	//m_wb.Close(COleVariant((short)0));
	m_app.Quit();
}

BOOL CExcel::GetColStrs(CString *pstr, long nCol, long nStartRow, long nStopRow)
{
	for(long n=nStartRow; n<nStopRow + 1; n++)
	{
		CString str;
		if(GetStr(str, n, nCol))
			pstr[n - nStartRow] = str;
		else
			pstr[n - nStartRow] = "";
	}
	
	return TRUE;	
}

BOOL CExcel::GetRowStrs(CString *pstr, long nRow, long nStartCol, long nStopCol)
{
	for(long n=nStartCol; n<nStopCol + 1; n++)
	{
		CString str;
		if(GetStr(str, nRow, n))
			pstr[n - nStartCol] = str;
		else
			pstr[n - nStartCol] = "";
	}
	
	return TRUE;	
}

BOOL CExcel::GetWorksheet(CStringArray &saWorksheet)
{
	if(!AttachToWorksheets()) return FALSE;

	saWorksheet.RemoveAll();
	for(long n=1; n<=m_wss.GetCount(); n++)
	{
		if(LPDISPATCH pDisp = m_wss.GetItem(COleVariant((short)n)))
		{
			m_ws.AttachDispatch(pDisp);
			CString str = m_ws.GetName();
			saWorksheet.Add(str);
		}
	}
	
	return TRUE;
}
