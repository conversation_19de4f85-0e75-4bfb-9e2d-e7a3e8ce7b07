﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using System.Threading;
using System.Diagnostics;

namespace ModbusRTU_CS
{
	public partial class MainForm : Form
	{
		ModbusRTUHelper m_ModbusRTUHelper = new ModbusRTUHelper();

		private byte m_NodeID = 1;

		private int m_MonitorInterval = 300;

		private bool m_StopMonitor;

		private bool m_ShowSent = true;
		private bool m_ShowReceived = true;


		public delegate void d_RefreshUI(SortedList<string, string> KeyValueList);
		public delegate void d_AppendCommandText(bool ReceiveData, List<byte> CommandList);

		private bool m_PauseMonitor = false;
		private void Monitor()
		{
			while (m_StopMonitor == false)
			{
				d_RefreshUI Refresh = new d_RefreshUI(RefreshUI);
				if (m_PauseMonitor)
				{
					System.Threading.Thread.Sleep(500);
					continue;
				}
				else
				{
					Stopwatch sw = new Stopwatch();
					sw.Start();
					int value = 0;
					//Drive Status,40002
					SortedList<string, string> valueList = new SortedList<string, string>();
					int ret = 0;

					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 1, ref value);
					if (ret == 0)
					{
						valueList.Add("DriveStatus", string.Format("{0}", value));
					}

					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 0, ref value);
					if (ret == 0)
					{
						valueList.Add("AlarmCode", string.Format("{0}", value));
					}

					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 10, ref value);
					if (ret == 0)
					{
						valueList.Add("ActualSpeed", string.Format("{0} rps", (int)Math.Round((double)(value > 32767 ? value - 65536 : value) / 240)));
					}

					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 11, ref value);
					if (ret == 0)
					{
						valueList.Add("TargetSpeed", string.Format("{0} rps", (int)Math.Round((double)(value > 32767 ? value - 65536 : value) / 240)));
					}

					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 4, ref value);
					if (ret == 0)
					{
						valueList.Add("EncoderPosition", string.Format("{0} steps", value));
					}

					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					ret = this.m_ModbusRTUHelper.ReadSingleSCLRegister(m_NodeID, 6, ref value);
					if (ret == 0)
					{
						valueList.Add("CommandPosition", string.Format("{0} steps", value));
					}
					this.BeginInvoke(Refresh, valueList);
					sw.Stop();


					if (m_PauseMonitor)
					{
						System.Threading.Thread.Sleep(100);
						continue;
					}
					System.Threading.Thread.Sleep(m_MonitorInterval);
				}
			}
		}

		private int GetSpeedValue(int value)
		{
			return (int)Math.Round((double)(value > 32767 ? value - 65536 : value) / 240);
		}

		public void RefreshUI(SortedList<string, string> UIKeyValueList)
		{
			this.txt_DriveStatus.Text = UIKeyValueList.ContainsKey("DriveStatus") ? UIKeyValueList["DriveStatus"] : "";
			this.txt_AlarmCode.Text = UIKeyValueList.ContainsKey("AlarmCode") ? UIKeyValueList["AlarmCode"] : "";
			this.txt_ActualSpeed.Text = UIKeyValueList.ContainsKey("ActualSpeed") ? UIKeyValueList["ActualSpeed"] : "";
			this.txt_TargetSpeed.Text = UIKeyValueList.ContainsKey("TargetSpeed") ? UIKeyValueList["TargetSpeed"] : "";
			this.txt_EncoderPosition.Text = UIKeyValueList.ContainsKey("EncoderPosition") ? UIKeyValueList["EncoderPosition"] : "";
			this.txt_CommandPosition.Text = UIKeyValueList.ContainsKey("CommandPosition") ? UIKeyValueList["CommandPosition"] : "";
		}

		private void CommandHistoryAppendText(bool ReceiveData, List<byte> CommandList)
		{
			if (ReceiveData)
			{
				this.txt_CommandHistory.AppendText("<- ");
			}
			else
			{
				this.txt_CommandHistory.AppendText("-> ");
			}
			for (int i = 0; i < CommandList.Count; i++)
			{
				this.txt_CommandHistory.AppendText(string.Format("{0:X2} ", CommandList[i]));
			}
			this.txt_CommandHistory.AppendText("\r\n");
		}

		private void SetActionEnable()
		{
			if (this.m_ModbusRTUHelper.IsOpen)
			{
				this.btn_Open.Enabled = false;
				this.btn_Close.Enabled = true;
				this.cmb_SerialPort.Enabled = false;
				this.cmb_BaudRate.Enabled = false;

				this.btn_Enable.Enabled = true;
				this.btn_Disable.Enabled = true;
				this.btn_AlarmReset.Enabled = true;
				this.btn_Stop.Enabled = true;

				this.btn_AbsMoveStart.Enabled = true;
				this.btn_RelMoveStart.Enabled = true;
				this.btn_ExecuteQProgram.Enabled = true;
				this.btn_CWJog.Enabled = true;
				this.btn_CCWJog.Enabled = true;

				this.btn_ReadSingleReg.Enabled = true;
				this.btn_WriteSingleReg.Enabled = true;
				this.btn_ReadMultiReg.Enabled = true;
				this.btn_WriteMultiReg.Enabled = true;

				this.btn_AbsMoveStop.Enabled = true;
				this.btn_RelMoveStop.Enabled = true;
				this.btn_StopQProgram.Enabled = true;

			}
			else
			{
				this.btn_Open.Enabled = true;
				this.btn_Close.Enabled = false;
				this.cmb_SerialPort.Enabled = true;
				this.cmb_BaudRate.Enabled = true;

				this.btn_Enable.Enabled = false;
				this.btn_Disable.Enabled = false;
				this.btn_AlarmReset.Enabled = false;
				this.btn_Stop.Enabled = false;

				this.btn_AbsMoveStart.Enabled = false;
				this.btn_RelMoveStart.Enabled = false;
				this.btn_ExecuteQProgram.Enabled = false;
				this.btn_CWJog.Enabled = false;
				this.btn_CCWJog.Enabled = false;

				this.btn_ReadSingleReg.Enabled = false;
				this.btn_WriteSingleReg.Enabled = false;
				this.btn_ReadMultiReg.Enabled = false;
				this.btn_WriteMultiReg.Enabled = false;

				this.btn_AbsMoveStop.Enabled = false;
				this.btn_RelMoveStop.Enabled = false;
				this.btn_StopQProgram.Enabled = false;
			}
		}

		public MainForm()
		{
			InitializeComponent();
		}

		private void ModbusRTUForm_Load(object sender, EventArgs e)
		{
			this.cmb_SerialPort.DataSource = System.IO.Ports.SerialPort.GetPortNames();
			if (this.cmb_SerialPort.Items.Count > 1)
			{
				this.cmb_SerialPort.SelectedIndex = 1;
			}
			this.cmb_BaudRate.SelectedIndex = 4;

			this.cmb_RelMoveDir.SelectedIndex = 0;

			m_ModbusRTUHelper.DataSend += new ModbusRTUHelper.OnDataSendOrReceivedEventHandler(m_ModbusRTUHelper_DataSend);
			m_ModbusRTUHelper.DataReceived += new ModbusRTUHelper.OnDataSendOrReceivedEventHandler(m_ModbusRTUHelper_DataReceived);
			//MonitorStatus = new d_Monitor(Monitor);
			//m_AsyncMonitor = this.BeginInvoke(MonitorStatus);

			m_PauseMonitor = !(m_ModbusRTUHelper.IsOpen && chk_Monitor.Checked);

			Thread thread = new Thread(Monitor);
			thread.Start();
		}

		void m_ModbusRTUHelper_DataSend(EventArgs e)
		{
			List<byte> commandList = new List<byte>();
			m_ModbusRTUHelper.GetLastCommandSend(ref commandList);
			if (m_ShowSent)
			{
				d_AppendCommandText append = new d_AppendCommandText(CommandHistoryAppendText);
				this.BeginInvoke(append, false, commandList);
			}
		}
		void m_ModbusRTUHelper_DataReceived(EventArgs e)
		{
			List<byte> commandList = new List<byte>();
			m_ModbusRTUHelper.GetLastCommandReceived(ref commandList);
			if (m_ShowReceived)
			{
				d_AppendCommandText append = new d_AppendCommandText(CommandHistoryAppendText);
				this.BeginInvoke(append, true, commandList);
			}
		}

		private void ModbusRTUForm_FormClosing(object sender, FormClosingEventArgs e)
		{
			m_StopMonitor = true;
			m_ModbusRTUHelper.ClosePort();
			Application.ExitThread();
			System.Environment.Exit(0);
		}

		private void btn_ReadSingleReg_Click(object sender, EventArgs e)
		{
			int value = 0;
			int registerNo = Convert.ToInt32(this.nud_RegNo.Value) - 40001;

			int ret = m_ModbusRTUHelper.ReadSingleHoldingRegister(m_NodeID, registerNo, ref value);
			this.txt_SingleRegValue.Text = value.ToString("X4");
		}

		private void btn_WriteSingleReg_Click(object sender, EventArgs e)
		{
			int regNo = Convert.ToInt32(this.nud_RegNo.Value) - 40001;
			int value = 0;
			List<int> valueList = new List<int>();
			if (int.TryParse(this.txt_SingleRegValue.Text, System.Globalization.NumberStyles.HexNumber, null, out value) == false)
			{
				MessageBox.Show("Incorrect Data Length.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
				return;
			}
			valueList.Add(value);
			int ret = m_ModbusRTUHelper.WriteMultiHoldingRegisters(m_NodeID, regNo, 1, valueList);
			if (ret != m_ModbusRTUHelper.MBERROR_OK)
			{
				MessageBox.Show(string.Format("Fail to write multi holding registers. Return: {0}", ret), "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
				return;
			}
		}

		private void btn_ReadMultiReg_Click(object sender, EventArgs e)
		{
			int registerNo = Convert.ToInt32(this.nud_MultiRegNo.Value) - 40001;
			int registerCount = Convert.ToInt32(this.nud_MultiRegCount.Value);
			List<int> valueList = new List<int>();
			int ret = m_ModbusRTUHelper.ReadMultiHoldingRegisters(m_NodeID, registerNo, registerCount, ref valueList);
			if (valueList.Count > 0)
			{
				string valueStr = "";

				foreach (int value in valueList)
				{
					string HexValue = value.ToString("X4");
					valueStr += valueStr.Length > 0 ? (" " + HexValue) : HexValue;
				}
				this.txt_MultiRegValue.Text = valueStr;
			}
		}

		private void btn_WriteMultiReg_Click(object sender, EventArgs e)
		{
			int regNo = Convert.ToInt32(this.nud_MultiRegNo.Value) - 40001;
			int count = Convert.ToInt32(this.nud_MultiRegCount.Value);

			List<int> valueList = new List<int>();

			string[] arr = this.txt_MultiRegValue.Text.Split(new char[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
			if (arr.Length != count)
			{
				MessageBox.Show("Incorrect Data Length.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
				return;
			}

			int value = 0;
			for (int i = 0; i < count; i++)
			{
				if (int.TryParse(arr[i], System.Globalization.NumberStyles.HexNumber, null, out value) == false)
				{
					MessageBox.Show("Incorrect Data Length.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
					return;
				}
				valueList.Add(value);
			}

			int ret = m_ModbusRTUHelper.WriteMultiHoldingRegisters(m_NodeID, regNo, count, valueList);

			if (ret != m_ModbusRTUHelper.MBERROR_OK)
			{
				MessageBox.Show(string.Format("Fail to write multi holding registers. Return: {0}", ret), "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
				return;
			}
		}

		private void btn_Open_Click(object sender, EventArgs e)
		{
			int port = 0;
			int baudRate = Convert.ToInt32(this.cmb_BaudRate.Text);
			if (int.TryParse(this.cmb_SerialPort.Text.Replace("COM", ""), out port))
			{
				int ret = m_ModbusRTUHelper.OpenPort(port, baudRate);
				if (ret != 0)
				{
					MessageBox.Show(string.Format("Fail to Open COM{0}", port), "Error", MessageBoxButtons.OK, MessageBoxIcon.Information);
				}
			}
			SetActionEnable();
		}

		private void btn_Close_Click(object sender, EventArgs e)
		{
			m_ModbusRTUHelper.ClosePort();
			SetActionEnable();
		}

		private void btn_ExecuteQProgram_Click(object sender, EventArgs e)
		{
			int segment = Convert.ToInt32(this.nud_QProgramSegment.Value);

			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.QueueLoadAndExecute, segment);
		}

		private void btn_Stop_Click(object sender, EventArgs e)
		{
			bool _PauseMonitr = m_PauseMonitor;
			m_PauseMonitor = true;
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.StopMoveAndKillBuffer);

			this.btn_AbsMoveStart.Enabled = true;
			this.btn_AbsMoveStop.Enabled = false;
			this.btn_RelMoveStart.Enabled = true;
			this.btn_AbsMoveStop.Enabled = false;
			this.btn_ExecuteQProgram.Enabled = true;
			this.btn_StopQProgram.Enabled = false;
			m_PauseMonitor = _PauseMonitr;
		}

		private void btn_ClearCommandHistory_Click(object sender, EventArgs e)
		{
			this.txt_CommandHistory.Clear();
		}

		private void btn_CWJog_MouseDown(object sender, MouseEventArgs e)
		{
			double accel = (double)nud_JogAccel.Value;
			double decel = (double)nud_JogDecel.Value;
			double speed = (double)nud_JogSpeed.Value;
			m_ModbusRTUHelper.WriteSingleSCLRegister(m_NodeID, 30, 1);
			m_ModbusRTUHelper.SetJogMoveParam(m_NodeID, accel, decel, speed);
			m_ModbusRTUHelper.StartJogging(m_NodeID);
		}

		private void btn_CWJog_MouseUp(object sender, MouseEventArgs e)
		{
			bool _PauseMonitr = m_PauseMonitor;
			m_PauseMonitor = true;
			int ret = m_ModbusRTUHelper.StopJogging(m_NodeID);
			if (ret != m_ModbusRTUHelper.MBERROR_OK)
			{
				ret = m_ModbusRTUHelper.StopJogging(m_NodeID);
			}
			m_PauseMonitor = _PauseMonitr;
		}

		private void btn_CCWJog_MouseDown(object sender, MouseEventArgs e)
		{
			m_ModbusRTUHelper.WriteSingleSCLRegister(m_NodeID, 30, -1);
			m_ModbusRTUHelper.SetJogMoveParam(m_NodeID, (double)nud_JogAccel.Value, (double)nud_JogDecel.Value, (double)nud_JogSpeed.Value);
			m_ModbusRTUHelper.StartJogging(m_NodeID);
		}

		private void btn_CCWJog_MouseUp(object sender, MouseEventArgs e)
		{
			bool _PauseMonitr = m_PauseMonitor;
			m_PauseMonitor = true;
			int ret = m_ModbusRTUHelper.StopJogging(m_NodeID);
			if (ret != m_ModbusRTUHelper.MBERROR_OK)
			{
				ret = m_ModbusRTUHelper.StopJogging(m_NodeID);
			}
			m_PauseMonitor = _PauseMonitr;
		}

		private void btn_AlarmReset_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.AlarmReset);
		}

		private void btn_Enable_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.MotorEnable);
		}

		private void btn_Disable_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.MotorDisable);
		}

		private void nud_NodeID_ValueChanged(object sender, EventArgs e)
		{
			m_NodeID = Convert.ToByte(this.nud_NodeID.Value);
		}

		private void btn_AbsMoveStart_Click(object sender, EventArgs e)
		{
			int nDI = Convert.ToInt32(this.nud_AbsMovePos.Value);
			m_ModbusRTUHelper.SetPointtoPointMoveParam(m_NodeID, (double)nud_PositionModeAcceleration.Value, (double)nud_PositionModeDeceleration.Value, (double)nud_PositionModeVelocity.Value, nDI);
			int ret = m_ModbusRTUHelper.FeedtoPosition(m_NodeID, nDI);
		}

		private void btn_AbsMoveStop_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.StopMoveAndKillBufferWithNormalDecel);
		}

		private void btn_RelMoveStart_Click(object sender, EventArgs e)
		{
			int nDI = Convert.ToInt32(this.nud_AbsMovePos.Value);
			m_ModbusRTUHelper.SetPointtoPointMoveParam(m_NodeID, (double)nud_PositionModeAcceleration.Value, (double)nud_PositionModeDeceleration.Value, (double)nud_PositionModeVelocity.Value, nDI);
			int ret = 0;

			if (this.cmb_RelMoveDir.SelectedIndex == 0)
			{
				ret = m_ModbusRTUHelper.FeedtoLength(m_NodeID, nDI);
			}
			else
			{
				ret = m_ModbusRTUHelper.FeedtoLength(m_NodeID, -nDI);
			}
		}

		private void btn_RelMoveStop_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.StopMoveAndKillBufferWithNormalDecel);
		}

		private void btn_StopQProgram_Click(object sender, EventArgs e)
		{
			int ret = m_ModbusRTUHelper.ExecuteCommandWithOpcode(m_NodeID, SCLCommandEncodingTable.StopMoveAndKillBufferWithNormalDecel);
		}

		private void chk_Monitor_CheckedChanged(object sender, EventArgs e)
		{
			m_PauseMonitor = !(m_ModbusRTUHelper.IsOpen && chk_Monitor.Checked);
		}

		private void chk_ShowSend_CheckedChanged(object sender, EventArgs e)
		{
			m_ShowSent = this.chk_ShowSend.Checked;
		}

		private void chk_ShowReceived_CheckedChanged(object sender, EventArgs e)
		{
			m_ShowReceived = this.chk_ShowReceived.Checked;
		}

		private void nud_Interval_ValueChanged(object sender, EventArgs e)
		{
			m_MonitorInterval = Convert.ToInt32(this.nud_Interval.Value);
		}

	}
}
