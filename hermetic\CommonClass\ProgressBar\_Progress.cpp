// _Progress.cpp: implementation of the C_Progress class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "_Progress.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

C_Progress::C_Progress(CStatusBar *wndStatusBar)
{
	m_wndStatusBar = wndStatusBar;
	maxValue = 100;
	strInfo = "";
	pBar = NULL;
}

C_Progress::~C_Progress()
{ 	
	FreeBar(); 
}
void C_Progress::InitProgress(CString strPromptInfo, int imaxValue, int nPane)
{
	strInfo = strPromptInfo;
	strInfoOther = "";
	curValue = 0;
	maxValue = imaxValue;
	FreeBar();
	pBar = new CProgressBar("", 60, 100, TRUE, nPane, m_wndStatusBar);
	nLastPercent = -1;
}
void C_Progress::SetOtherInfo(CString strOtherInfo)
{
	strInfoOther = strOtherInfo;
}
void C_Progress::SetPercentPos(int PercentValue)
{
	nPercentValue = PercentValue;
	if(PercentValue != nLastPercent)
	{
		if(pBar != NULL)
		{
			pBar->SetText(GetProgressInfo());			
			pBar->StepIt();							
		}
	}
	if(PercentValue >= 99) FreeBar();
	nLastPercent = PercentValue;
}
void C_Progress::SetStep()
{
	if(maxValue == 0) return;
	curValue++;
	if(curValue <= 1 ) nLastPercent = -1;
	nPercentValue = 100* curValue/maxValue;
//	if(pBar) pBar->SetText(GetProgressInfo());
	if(nPercentValue != nLastPercent)
	{
		nLastPercent = nPercentValue;		
		if(pBar != NULL)
		{
			pBar->SetText(GetProgressInfo());			
			pBar->StepIt();							
		}
	}			
	if(curValue >= maxValue - 2) FreeBar();			
}

CString C_Progress::GetProgressInfo()
{
	static CString str;
	str.Format("%s ... %d%% %s",strInfo,nPercentValue,strInfoOther);
	return str;
}

void C_Progress::FreeBar()
{
	if(pBar != NULL)
	{
		delete pBar;
		pBar = NULL;
	}
}
