// _DlgInputBox.cpp : implementation file-
//

#include "stdafx.h"
#include "..\SdkExtDll_include\SdkExt.h"
#include "_DlgInputBox.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#undef THIS_FILE
static char THIS_FILE[] = __FILE__;
#endif

/////////////////////////////////////////////////////////////////////////////
// C_DlgInputBox dialog


C_DlgInputBox::C_DlgInputBox(CWnd* pParent /*=NULL*/)
	: CDialog(C_DlgInputBox::IDD, pParent)
{
	//{{AFX_DATA_INIT(C_DlgInputBox)
	m_strInput = _T("");
	//}}AFX_DATA_INIT
}


void C_DlgInputBox::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	//{{AFX_DATA_MAP(C_DlgInputBox)
	DDX_Text(pDX, IDC_EDIT_INPUT, m_strInput);
	//}}AFX_DATA_MAP
}


BEGIN_MESSAGE_MAP(C_DlgInputBox, CDialog)
	//{{AFX_MSG_MAP(C_DlgInputBox)
	ON_WM_SHOWWINDOW()
	//}}AFX_MSG_MAP
END_MESSAGE_MAP()

/////////////////////////////////////////////////////////////////////////////
// C_DlgInputBox message handlers

BOOL C_DlgInputBox::OnInitDialog() 
{
	CDialog::OnInitDialog();
	
	// TODO: Add extra initialization here
//	m_strWindowText = "";
//	m_strPrompt= "";
//	m_strInput="";
	
	return TRUE;  // return TRUE unless you set the focus to a control
	              // EXCEPTION: OCX Property Pages should return FALSE
}

//void C_DlgInputBox::SetInput(CString str)
//{
//	m_strInput = str;
//}

CString C_DlgInputBox::GetInput()
{
//	GetDlgItemText(IDC_EDIT_INPUT,m_strInput);
	return m_strInput;
}

//void C_DlgInputBox::SetPrompt(CString str)
//{
//	m_strPrompt = str;
//}
//

//void C_DlgInputBox::SetWindowCaption(CString str)
//{
//	m_strWindowText = str;
//}
//

void C_DlgInputBox::OnShowWindow(BOOL bShow, UINT nStatus) 
{
	CDialog::OnShowWindow(bShow, nStatus);
	
	SetWindowText(m_strWindowText);
	SetDlgItemText(IDC_LBL_PROMPT,m_strPrompt);	
	SetDlgItemText(IDC_EDIT_INPUT,m_strInput);	
}
