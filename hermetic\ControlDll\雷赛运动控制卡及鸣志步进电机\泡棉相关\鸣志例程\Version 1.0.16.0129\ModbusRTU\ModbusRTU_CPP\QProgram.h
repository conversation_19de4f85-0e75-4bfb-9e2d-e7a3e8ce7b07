#pragma once


// CQProgram dialog

class CQProgram : public CDialog
{
	DECLARE_DYNAMIC(CQProgram)

public:
	CQProgram(CWnd* pParent = NULL);   // standard constructor
	virtual ~CQProgram();

// Dialog Data
	enum { IDD = IDD_DIALOG_QPROGRAM };

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support
	int m_txt_QProgram_Segment;

	DECLARE_MESSAGE_MAP()
public:
	void SetButtonEnable(int nID, BOOL bEnable);
	void SetQProgramSegment(int nSegemtn);
	int GetQProgramSegment();
	afx_msg void OnClickedBtnQprogramStart();
	afx_msg void OnClickedBtnQprogramStop();
	virtual void OnOK();
	virtual void OnCancel();
};
