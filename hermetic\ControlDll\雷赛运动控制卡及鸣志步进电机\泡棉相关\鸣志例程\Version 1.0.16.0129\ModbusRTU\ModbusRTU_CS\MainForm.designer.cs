﻿namespace ModbusRTU_CS
{
	partial class MainForm
	{
		/// <summary>
		/// Required designer variable.
		/// </summary>
		private System.ComponentModel.IContainer components = null;

		/// <summary>
		/// Clean up any resources being used.
		/// </summary>
		/// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
		protected override void Dispose(bool disposing)
		{
			if (disposing && (components != null))
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		#region Windows Form Designer generated code

		/// <summary>
		/// Required method for Designer support - do not modify
		/// the contents of this method with the code editor.
		/// </summary>
		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(MainForm));
			this.btn_WriteSingleReg = new System.Windows.Forms.Button();
			this.grp_CommandHistory = new System.Windows.Forms.GroupBox();
			this.chk_ShowReceived = new System.Windows.Forms.CheckBox();
			this.chk_ShowSend = new System.Windows.Forms.CheckBox();
			this.btn_ClearCommandHistory = new System.Windows.Forms.Button();
			this.txt_CommandHistory = new System.Windows.Forms.TextBox();
			this.lbl_SingleRegNo = new System.Windows.Forms.Label();
			this.nud_RegNo = new System.Windows.Forms.NumericUpDown();
			this.grp_SingleRegister = new System.Windows.Forms.GroupBox();
			this.btn_ReadSingleReg = new System.Windows.Forms.Button();
			this.txt_SingleRegValue = new System.Windows.Forms.TextBox();
			this.lbl_SingleRegValue = new System.Windows.Forms.Label();
			this.lbl_MultiRegCount = new System.Windows.Forms.Label();
			this.nud_MultiRegCount = new System.Windows.Forms.NumericUpDown();
			this.lbl_QProgramSegment = new System.Windows.Forms.Label();
			this.nud_QProgramSegment = new System.Windows.Forms.NumericUpDown();
			this.lbl_JogAccelUnit = new System.Windows.Forms.Label();
			this.nud_JogAccel = new System.Windows.Forms.NumericUpDown();
			this.lbl_JogSpeedUnit = new System.Windows.Forms.Label();
			this.nud_JogSpeed = new System.Windows.Forms.NumericUpDown();
			this.lbl_JogAccel = new System.Windows.Forms.Label();
			this.btn_CCWJog = new System.Windows.Forms.Button();
			this.btn_CWJog = new System.Windows.Forms.Button();
			this.lbl_JogSpeed = new System.Windows.Forms.Label();
			this.grp_Register = new System.Windows.Forms.GroupBox();
			this.grp_MultiRegister = new System.Windows.Forms.GroupBox();
			this.btn_WriteMultiReg = new System.Windows.Forms.Button();
			this.nud_MultiRegNo = new System.Windows.Forms.NumericUpDown();
			this.lbl_MultiRegNo = new System.Windows.Forms.Label();
			this.txt_MultiRegValue = new System.Windows.Forms.TextBox();
			this.lbl_MultiRegValue = new System.Windows.Forms.Label();
			this.btn_ReadMultiReg = new System.Windows.Forms.Button();
			this.grp_Monitor = new System.Windows.Forms.GroupBox();
			this.label1 = new System.Windows.Forms.Label();
			this.nud_Interval = new System.Windows.Forms.NumericUpDown();
			this.lbl_Interval = new System.Windows.Forms.Label();
			this.chk_Monitor = new System.Windows.Forms.CheckBox();
			this.txt_CommandPosition = new System.Windows.Forms.TextBox();
			this.txt_EncoderPosition = new System.Windows.Forms.TextBox();
			this.lbl_CommandPos = new System.Windows.Forms.Label();
			this.lbl_EncoderPos = new System.Windows.Forms.Label();
			this.txt_TargetSpeed = new System.Windows.Forms.TextBox();
			this.lbl_TargetSpeed = new System.Windows.Forms.Label();
			this.txt_ActualSpeed = new System.Windows.Forms.TextBox();
			this.lbl_ActualSpeed = new System.Windows.Forms.Label();
			this.txt_AlarmCode = new System.Windows.Forms.TextBox();
			this.lbl_AlarmCode = new System.Windows.Forms.Label();
			this.lbl_DriveStatus = new System.Windows.Forms.Label();
			this.txt_DriveStatus = new System.Windows.Forms.TextBox();
			this.grp_AbsMove = new System.Windows.Forms.GroupBox();
			this.lbl_AbsMovePosUnit = new System.Windows.Forms.Label();
			this.btn_AbsMoveStop = new System.Windows.Forms.Button();
			this.btn_AbsMoveStart = new System.Windows.Forms.Button();
			this.nud_AbsMovePos = new System.Windows.Forms.NumericUpDown();
			this.lbl_AbsMovePos = new System.Windows.Forms.Label();
			this.nud_PositionModeDeceleration = new System.Windows.Forms.NumericUpDown();
			this.lbl_PosModeDecel = new System.Windows.Forms.Label();
			this.nud_PositionModeAcceleration = new System.Windows.Forms.NumericUpDown();
			this.nud_PositionModeVelocity = new System.Windows.Forms.NumericUpDown();
			this.lbl_PosModeAccel = new System.Windows.Forms.Label();
			this.lbl_PosModeVelocity = new System.Windows.Forms.Label();
			this.tc_Motion = new System.Windows.Forms.TabControl();
			this.tp_PosMode = new System.Windows.Forms.TabPage();
			this.lbl_PosModeAccelUnit = new System.Windows.Forms.Label();
			this.lbl_PosModeDecelUnit = new System.Windows.Forms.Label();
			this.lbl_PosModeVelocityUnit = new System.Windows.Forms.Label();
			this.grp_RelMove = new System.Windows.Forms.GroupBox();
			this.lbl_RelMovePosUnit = new System.Windows.Forms.Label();
			this.cmb_RelMoveDir = new System.Windows.Forms.ComboBox();
			this.lbl_RelMoveDir = new System.Windows.Forms.Label();
			this.btn_RelMoveStop = new System.Windows.Forms.Button();
			this.btn_RelMoveStart = new System.Windows.Forms.Button();
			this.nud_ReMovePos = new System.Windows.Forms.NumericUpDown();
			this.lbl_RelMovePos = new System.Windows.Forms.Label();
			this.tp_QProgram = new System.Windows.Forms.TabPage();
			this.btn_StopQProgram = new System.Windows.Forms.Button();
			this.btn_ExecuteQProgram = new System.Windows.Forms.Button();
			this.tp_Jog = new System.Windows.Forms.TabPage();
			this.lbl_JogDecelUnit = new System.Windows.Forms.Label();
			this.nud_JogDecel = new System.Windows.Forms.NumericUpDown();
			this.lbl_JogDecel = new System.Windows.Forms.Label();
			this.pnl_ToolBar = new System.Windows.Forms.Panel();
			this.nud_NodeID = new System.Windows.Forms.NumericUpDown();
			this.lbl_NodeID = new System.Windows.Forms.Label();
			this.pic_Sep2 = new System.Windows.Forms.PictureBox();
			this.pic_Sep1 = new System.Windows.Forms.PictureBox();
			this.btn_Close = new System.Windows.Forms.Button();
			this.btn_Open = new System.Windows.Forms.Button();
			this.btn_Disable = new System.Windows.Forms.Button();
			this.cmb_BaudRate = new System.Windows.Forms.ComboBox();
			this.pic_MOONS = new System.Windows.Forms.PictureBox();
			this.lbl_BaudRate = new System.Windows.Forms.Label();
			this.btn_Stop = new System.Windows.Forms.Button();
			this.lbl_COMPort = new System.Windows.Forms.Label();
			this.cmb_SerialPort = new System.Windows.Forms.ComboBox();
			this.btn_Enable = new System.Windows.Forms.Button();
			this.btn_AlarmReset = new System.Windows.Forms.Button();
			this.grp_CommandHistory.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_RegNo)).BeginInit();
			this.grp_SingleRegister.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_MultiRegCount)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_QProgramSegment)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogAccel)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogSpeed)).BeginInit();
			this.grp_Register.SuspendLayout();
			this.grp_MultiRegister.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_MultiRegNo)).BeginInit();
			this.grp_Monitor.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_Interval)).BeginInit();
			this.grp_AbsMove.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_AbsMovePos)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeDeceleration)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeAcceleration)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeVelocity)).BeginInit();
			this.tc_Motion.SuspendLayout();
			this.tp_PosMode.SuspendLayout();
			this.grp_RelMove.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_ReMovePos)).BeginInit();
			this.tp_QProgram.SuspendLayout();
			this.tp_Jog.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogDecel)).BeginInit();
			this.pnl_ToolBar.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_NodeID)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_Sep2)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_Sep1)).BeginInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_MOONS)).BeginInit();
			this.SuspendLayout();
			// 
			// btn_WriteSingleReg
			// 
			this.btn_WriteSingleReg.Enabled = false;
			this.btn_WriteSingleReg.Location = new System.Drawing.Point(133, 45);
			this.btn_WriteSingleReg.Name = "btn_WriteSingleReg";
			this.btn_WriteSingleReg.Size = new System.Drawing.Size(75, 25);
			this.btn_WriteSingleReg.TabIndex = 8;
			this.btn_WriteSingleReg.Text = "Write";
			this.btn_WriteSingleReg.UseVisualStyleBackColor = true;
			this.btn_WriteSingleReg.Click += new System.EventHandler(this.btn_WriteSingleReg_Click);
			// 
			// grp_CommandHistory
			// 
			this.grp_CommandHistory.Controls.Add(this.chk_ShowReceived);
			this.grp_CommandHistory.Controls.Add(this.chk_ShowSend);
			this.grp_CommandHistory.Controls.Add(this.btn_ClearCommandHistory);
			this.grp_CommandHistory.Controls.Add(this.txt_CommandHistory);
			this.grp_CommandHistory.Location = new System.Drawing.Point(318, 181);
			this.grp_CommandHistory.Name = "grp_CommandHistory";
			this.grp_CommandHistory.Size = new System.Drawing.Size(624, 413);
			this.grp_CommandHistory.TabIndex = 7;
			this.grp_CommandHistory.TabStop = false;
			this.grp_CommandHistory.Text = "Command History";
			// 
			// chk_ShowReceived
			// 
			this.chk_ShowReceived.AutoSize = true;
			this.chk_ShowReceived.Checked = true;
			this.chk_ShowReceived.CheckState = System.Windows.Forms.CheckState.Checked;
			this.chk_ShowReceived.Location = new System.Drawing.Point(129, 20);
			this.chk_ShowReceived.Name = "chk_ShowReceived";
			this.chk_ShowReceived.Size = new System.Drawing.Size(110, 18);
			this.chk_ShowReceived.TabIndex = 20;
			this.chk_ShowReceived.Text = "Show Received";
			this.chk_ShowReceived.UseVisualStyleBackColor = true;
			this.chk_ShowReceived.CheckedChanged += new System.EventHandler(this.chk_ShowReceived_CheckedChanged);
			// 
			// chk_ShowSend
			// 
			this.chk_ShowSend.AutoSize = true;
			this.chk_ShowSend.Checked = true;
			this.chk_ShowSend.CheckState = System.Windows.Forms.CheckState.Checked;
			this.chk_ShowSend.Location = new System.Drawing.Point(15, 20);
			this.chk_ShowSend.Name = "chk_ShowSend";
			this.chk_ShowSend.Size = new System.Drawing.Size(87, 18);
			this.chk_ShowSend.TabIndex = 19;
			this.chk_ShowSend.Text = "Show Sent";
			this.chk_ShowSend.UseVisualStyleBackColor = true;
			this.chk_ShowSend.CheckedChanged += new System.EventHandler(this.chk_ShowSend_CheckedChanged);
			// 
			// btn_ClearCommandHistory
			// 
			this.btn_ClearCommandHistory.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
			this.btn_ClearCommandHistory.Location = new System.Drawing.Point(543, 17);
			this.btn_ClearCommandHistory.Name = "btn_ClearCommandHistory";
			this.btn_ClearCommandHistory.Size = new System.Drawing.Size(75, 23);
			this.btn_ClearCommandHistory.TabIndex = 18;
			this.btn_ClearCommandHistory.Text = "Clear";
			this.btn_ClearCommandHistory.UseVisualStyleBackColor = true;
			this.btn_ClearCommandHistory.Click += new System.EventHandler(this.btn_ClearCommandHistory_Click);
			// 
			// txt_CommandHistory
			// 
			this.txt_CommandHistory.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
						| System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_CommandHistory.Location = new System.Drawing.Point(6, 46);
			this.txt_CommandHistory.Multiline = true;
			this.txt_CommandHistory.Name = "txt_CommandHistory";
			this.txt_CommandHistory.ReadOnly = true;
			this.txt_CommandHistory.ScrollBars = System.Windows.Forms.ScrollBars.Both;
			this.txt_CommandHistory.Size = new System.Drawing.Size(612, 361);
			this.txt_CommandHistory.TabIndex = 0;
			// 
			// lbl_SingleRegNo
			// 
			this.lbl_SingleRegNo.AutoSize = true;
			this.lbl_SingleRegNo.Location = new System.Drawing.Point(6, 22);
			this.lbl_SingleRegNo.Name = "lbl_SingleRegNo";
			this.lbl_SingleRegNo.Size = new System.Drawing.Size(32, 14);
			this.lbl_SingleRegNo.TabIndex = 11;
			this.lbl_SingleRegNo.Text = "Reg.";
			// 
			// nud_RegNo
			// 
			this.nud_RegNo.Location = new System.Drawing.Point(67, 20);
			this.nud_RegNo.Maximum = new decimal(new int[] {
            40256,
            0,
            0,
            0});
			this.nud_RegNo.Minimum = new decimal(new int[] {
            40001,
            0,
            0,
            0});
			this.nud_RegNo.Name = "nud_RegNo";
			this.nud_RegNo.Size = new System.Drawing.Size(60, 22);
			this.nud_RegNo.TabIndex = 12;
			this.nud_RegNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_RegNo.Value = new decimal(new int[] {
            40001,
            0,
            0,
            0});
			// 
			// grp_SingleRegister
			// 
			this.grp_SingleRegister.Controls.Add(this.btn_ReadSingleReg);
			this.grp_SingleRegister.Controls.Add(this.nud_RegNo);
			this.grp_SingleRegister.Controls.Add(this.lbl_SingleRegNo);
			this.grp_SingleRegister.Controls.Add(this.txt_SingleRegValue);
			this.grp_SingleRegister.Controls.Add(this.lbl_SingleRegValue);
			this.grp_SingleRegister.Controls.Add(this.btn_WriteSingleReg);
			this.grp_SingleRegister.Location = new System.Drawing.Point(6, 20);
			this.grp_SingleRegister.Name = "grp_SingleRegister";
			this.grp_SingleRegister.Size = new System.Drawing.Size(214, 75);
			this.grp_SingleRegister.TabIndex = 13;
			this.grp_SingleRegister.TabStop = false;
			this.grp_SingleRegister.Text = "Single Register";
			// 
			// btn_ReadSingleReg
			// 
			this.btn_ReadSingleReg.Enabled = false;
			this.btn_ReadSingleReg.Location = new System.Drawing.Point(133, 18);
			this.btn_ReadSingleReg.Name = "btn_ReadSingleReg";
			this.btn_ReadSingleReg.Size = new System.Drawing.Size(75, 25);
			this.btn_ReadSingleReg.TabIndex = 17;
			this.btn_ReadSingleReg.Text = "Read";
			this.btn_ReadSingleReg.UseVisualStyleBackColor = true;
			this.btn_ReadSingleReg.Click += new System.EventHandler(this.btn_ReadSingleReg_Click);
			// 
			// txt_SingleRegValue
			// 
			this.txt_SingleRegValue.Location = new System.Drawing.Point(67, 46);
			this.txt_SingleRegValue.Name = "txt_SingleRegValue";
			this.txt_SingleRegValue.Size = new System.Drawing.Size(60, 22);
			this.txt_SingleRegValue.TabIndex = 14;
			this.txt_SingleRegValue.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// lbl_SingleRegValue
			// 
			this.lbl_SingleRegValue.AutoSize = true;
			this.lbl_SingleRegValue.Location = new System.Drawing.Point(6, 50);
			this.lbl_SingleRegValue.Name = "lbl_SingleRegValue";
			this.lbl_SingleRegValue.Size = new System.Drawing.Size(55, 14);
			this.lbl_SingleRegValue.TabIndex = 13;
			this.lbl_SingleRegValue.Text = "Value(H)";
			// 
			// lbl_MultiRegCount
			// 
			this.lbl_MultiRegCount.AutoSize = true;
			this.lbl_MultiRegCount.Location = new System.Drawing.Point(131, 22);
			this.lbl_MultiRegCount.Name = "lbl_MultiRegCount";
			this.lbl_MultiRegCount.Size = new System.Drawing.Size(40, 14);
			this.lbl_MultiRegCount.TabIndex = 15;
			this.lbl_MultiRegCount.Text = "Count";
			// 
			// nud_MultiRegCount
			// 
			this.nud_MultiRegCount.Location = new System.Drawing.Point(177, 20);
			this.nud_MultiRegCount.Maximum = new decimal(new int[] {
            10,
            0,
            0,
            0});
			this.nud_MultiRegCount.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.nud_MultiRegCount.Name = "nud_MultiRegCount";
			this.nud_MultiRegCount.Size = new System.Drawing.Size(44, 22);
			this.nud_MultiRegCount.TabIndex = 16;
			this.nud_MultiRegCount.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_MultiRegCount.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
			// 
			// lbl_QProgramSegment
			// 
			this.lbl_QProgramSegment.AutoSize = true;
			this.lbl_QProgramSegment.Location = new System.Drawing.Point(6, 15);
			this.lbl_QProgramSegment.Name = "lbl_QProgramSegment";
			this.lbl_QProgramSegment.Size = new System.Drawing.Size(57, 14);
			this.lbl_QProgramSegment.TabIndex = 15;
			this.lbl_QProgramSegment.Text = "Segment";
			// 
			// nud_QProgramSegment
			// 
			this.nud_QProgramSegment.Location = new System.Drawing.Point(69, 11);
			this.nud_QProgramSegment.Maximum = new decimal(new int[] {
            12,
            0,
            0,
            0});
			this.nud_QProgramSegment.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.nud_QProgramSegment.Name = "nud_QProgramSegment";
			this.nud_QProgramSegment.Size = new System.Drawing.Size(110, 22);
			this.nud_QProgramSegment.TabIndex = 16;
			this.nud_QProgramSegment.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_QProgramSegment.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
			// 
			// lbl_JogAccelUnit
			// 
			this.lbl_JogAccelUnit.AutoSize = true;
			this.lbl_JogAccelUnit.Location = new System.Drawing.Point(251, 37);
			this.lbl_JogAccelUnit.Name = "lbl_JogAccelUnit";
			this.lbl_JogAccelUnit.Size = new System.Drawing.Size(33, 14);
			this.lbl_JogAccelUnit.TabIndex = 13;
			this.lbl_JogAccelUnit.Text = "rps/s";
			// 
			// nud_JogAccel
			// 
			this.nud_JogAccel.DecimalPlaces = 3;
			this.nud_JogAccel.Location = new System.Drawing.Point(131, 35);
			this.nud_JogAccel.Maximum = new decimal(new int[] {
            5461167,
            0,
            0,
            196608});
			this.nud_JogAccel.Minimum = new decimal(new int[] {
            167,
            0,
            0,
            196608});
			this.nud_JogAccel.Name = "nud_JogAccel";
			this.nud_JogAccel.Size = new System.Drawing.Size(114, 22);
			this.nud_JogAccel.TabIndex = 12;
			this.nud_JogAccel.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_JogAccel.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
			// 
			// lbl_JogSpeedUnit
			// 
			this.lbl_JogSpeedUnit.AutoSize = true;
			this.lbl_JogSpeedUnit.Location = new System.Drawing.Point(251, 13);
			this.lbl_JogSpeedUnit.Name = "lbl_JogSpeedUnit";
			this.lbl_JogSpeedUnit.Size = new System.Drawing.Size(23, 14);
			this.lbl_JogSpeedUnit.TabIndex = 11;
			this.lbl_JogSpeedUnit.Text = "rps";
			// 
			// nud_JogSpeed
			// 
			this.nud_JogSpeed.DecimalPlaces = 2;
			this.nud_JogSpeed.Location = new System.Drawing.Point(131, 8);
			this.nud_JogSpeed.Minimum = new decimal(new int[] {
            25,
            0,
            0,
            196608});
			this.nud_JogSpeed.Name = "nud_JogSpeed";
			this.nud_JogSpeed.Size = new System.Drawing.Size(114, 22);
			this.nud_JogSpeed.TabIndex = 10;
			this.nud_JogSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_JogSpeed.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
			// 
			// lbl_JogAccel
			// 
			this.lbl_JogAccel.AutoSize = true;
			this.lbl_JogAccel.Location = new System.Drawing.Point(6, 37);
			this.lbl_JogAccel.Name = "lbl_JogAccel";
			this.lbl_JogAccel.Size = new System.Drawing.Size(119, 14);
			this.lbl_JogAccel.TabIndex = 9;
			this.lbl_JogAccel.Text = "Acceleration(40047)";
			// 
			// btn_CCWJog
			// 
			this.btn_CCWJog.Enabled = false;
			this.btn_CCWJog.Location = new System.Drawing.Point(143, 92);
			this.btn_CCWJog.Name = "btn_CCWJog";
			this.btn_CCWJog.Size = new System.Drawing.Size(75, 49);
			this.btn_CCWJog.TabIndex = 8;
			this.btn_CCWJog.Text = "CCW Jog";
			this.btn_CCWJog.UseVisualStyleBackColor = true;
			this.btn_CCWJog.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btn_CCWJog_MouseDown);
			this.btn_CCWJog.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btn_CCWJog_MouseUp);
			// 
			// btn_CWJog
			// 
			this.btn_CWJog.Enabled = false;
			this.btn_CWJog.Location = new System.Drawing.Point(48, 92);
			this.btn_CWJog.Name = "btn_CWJog";
			this.btn_CWJog.Size = new System.Drawing.Size(75, 49);
			this.btn_CWJog.TabIndex = 7;
			this.btn_CWJog.Text = "CW Jog";
			this.btn_CWJog.UseVisualStyleBackColor = true;
			this.btn_CWJog.MouseDown += new System.Windows.Forms.MouseEventHandler(this.btn_CWJog_MouseDown);
			this.btn_CWJog.MouseUp += new System.Windows.Forms.MouseEventHandler(this.btn_CWJog_MouseUp);
			// 
			// lbl_JogSpeed
			// 
			this.lbl_JogSpeed.AutoSize = true;
			this.lbl_JogSpeed.Location = new System.Drawing.Point(6, 13);
			this.lbl_JogSpeed.Name = "lbl_JogSpeed";
			this.lbl_JogSpeed.Size = new System.Drawing.Size(110, 14);
			this.lbl_JogSpeed.TabIndex = 6;
			this.lbl_JogSpeed.Text = "Jog Speed(40049)";
			// 
			// grp_Register
			// 
			this.grp_Register.Controls.Add(this.grp_MultiRegister);
			this.grp_Register.Controls.Add(this.grp_SingleRegister);
			this.grp_Register.Location = new System.Drawing.Point(318, 71);
			this.grp_Register.Name = "grp_Register";
			this.grp_Register.Size = new System.Drawing.Size(624, 102);
			this.grp_Register.TabIndex = 30;
			this.grp_Register.TabStop = false;
			this.grp_Register.Text = "Register";
			// 
			// grp_MultiRegister
			// 
			this.grp_MultiRegister.Controls.Add(this.btn_WriteMultiReg);
			this.grp_MultiRegister.Controls.Add(this.nud_MultiRegNo);
			this.grp_MultiRegister.Controls.Add(this.lbl_MultiRegNo);
			this.grp_MultiRegister.Controls.Add(this.txt_MultiRegValue);
			this.grp_MultiRegister.Controls.Add(this.lbl_MultiRegCount);
			this.grp_MultiRegister.Controls.Add(this.lbl_MultiRegValue);
			this.grp_MultiRegister.Controls.Add(this.nud_MultiRegCount);
			this.grp_MultiRegister.Controls.Add(this.btn_ReadMultiReg);
			this.grp_MultiRegister.Location = new System.Drawing.Point(226, 20);
			this.grp_MultiRegister.Name = "grp_MultiRegister";
			this.grp_MultiRegister.Size = new System.Drawing.Size(388, 75);
			this.grp_MultiRegister.TabIndex = 14;
			this.grp_MultiRegister.TabStop = false;
			this.grp_MultiRegister.Text = "Multi Register";
			// 
			// btn_WriteMultiReg
			// 
			this.btn_WriteMultiReg.Enabled = false;
			this.btn_WriteMultiReg.Location = new System.Drawing.Point(307, 19);
			this.btn_WriteMultiReg.Name = "btn_WriteMultiReg";
			this.btn_WriteMultiReg.Size = new System.Drawing.Size(75, 23);
			this.btn_WriteMultiReg.TabIndex = 19;
			this.btn_WriteMultiReg.Text = "Write";
			this.btn_WriteMultiReg.UseVisualStyleBackColor = true;
			this.btn_WriteMultiReg.Click += new System.EventHandler(this.btn_WriteMultiReg_Click);
			// 
			// nud_MultiRegNo
			// 
			this.nud_MultiRegNo.Location = new System.Drawing.Point(65, 20);
			this.nud_MultiRegNo.Maximum = new decimal(new int[] {
            40256,
            0,
            0,
            0});
			this.nud_MultiRegNo.Minimum = new decimal(new int[] {
            40001,
            0,
            0,
            0});
			this.nud_MultiRegNo.Name = "nud_MultiRegNo";
			this.nud_MultiRegNo.Size = new System.Drawing.Size(60, 22);
			this.nud_MultiRegNo.TabIndex = 18;
			this.nud_MultiRegNo.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_MultiRegNo.Value = new decimal(new int[] {
            40001,
            0,
            0,
            0});
			// 
			// lbl_MultiRegNo
			// 
			this.lbl_MultiRegNo.AutoSize = true;
			this.lbl_MultiRegNo.Location = new System.Drawing.Point(6, 22);
			this.lbl_MultiRegNo.Name = "lbl_MultiRegNo";
			this.lbl_MultiRegNo.Size = new System.Drawing.Size(32, 14);
			this.lbl_MultiRegNo.TabIndex = 17;
			this.lbl_MultiRegNo.Text = "Reg.";
			// 
			// txt_MultiRegValue
			// 
			this.txt_MultiRegValue.Location = new System.Drawing.Point(65, 47);
			this.txt_MultiRegValue.Name = "txt_MultiRegValue";
			this.txt_MultiRegValue.Size = new System.Drawing.Size(317, 22);
			this.txt_MultiRegValue.TabIndex = 14;
			// 
			// lbl_MultiRegValue
			// 
			this.lbl_MultiRegValue.AutoSize = true;
			this.lbl_MultiRegValue.Location = new System.Drawing.Point(6, 53);
			this.lbl_MultiRegValue.Name = "lbl_MultiRegValue";
			this.lbl_MultiRegValue.Size = new System.Drawing.Size(55, 14);
			this.lbl_MultiRegValue.TabIndex = 13;
			this.lbl_MultiRegValue.Text = "Value(H)";
			// 
			// btn_ReadMultiReg
			// 
			this.btn_ReadMultiReg.Enabled = false;
			this.btn_ReadMultiReg.Location = new System.Drawing.Point(227, 19);
			this.btn_ReadMultiReg.Name = "btn_ReadMultiReg";
			this.btn_ReadMultiReg.Size = new System.Drawing.Size(75, 23);
			this.btn_ReadMultiReg.TabIndex = 8;
			this.btn_ReadMultiReg.Text = "Read";
			this.btn_ReadMultiReg.UseVisualStyleBackColor = true;
			this.btn_ReadMultiReg.Click += new System.EventHandler(this.btn_ReadMultiReg_Click);
			// 
			// grp_Monitor
			// 
			this.grp_Monitor.Controls.Add(this.label1);
			this.grp_Monitor.Controls.Add(this.nud_Interval);
			this.grp_Monitor.Controls.Add(this.lbl_Interval);
			this.grp_Monitor.Controls.Add(this.chk_Monitor);
			this.grp_Monitor.Controls.Add(this.txt_CommandPosition);
			this.grp_Monitor.Controls.Add(this.txt_EncoderPosition);
			this.grp_Monitor.Controls.Add(this.lbl_CommandPos);
			this.grp_Monitor.Controls.Add(this.lbl_EncoderPos);
			this.grp_Monitor.Controls.Add(this.txt_TargetSpeed);
			this.grp_Monitor.Controls.Add(this.lbl_TargetSpeed);
			this.grp_Monitor.Controls.Add(this.txt_ActualSpeed);
			this.grp_Monitor.Controls.Add(this.lbl_ActualSpeed);
			this.grp_Monitor.Controls.Add(this.txt_AlarmCode);
			this.grp_Monitor.Controls.Add(this.lbl_AlarmCode);
			this.grp_Monitor.Controls.Add(this.lbl_DriveStatus);
			this.grp_Monitor.Controls.Add(this.txt_DriveStatus);
			this.grp_Monitor.Location = new System.Drawing.Point(12, 387);
			this.grp_Monitor.Name = "grp_Monitor";
			this.grp_Monitor.Size = new System.Drawing.Size(300, 210);
			this.grp_Monitor.TabIndex = 31;
			this.grp_Monitor.TabStop = false;
			this.grp_Monitor.Text = "Monitor";
			// 
			// label1
			// 
			this.label1.AutoSize = true;
			this.label1.ForeColor = System.Drawing.Color.DodgerBlue;
			this.label1.Location = new System.Drawing.Point(268, 19);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(22, 14);
			this.label1.TabIndex = 41;
			this.label1.Text = "ms";
			// 
			// nud_Interval
			// 
			this.nud_Interval.Location = new System.Drawing.Point(198, 16);
			this.nud_Interval.Maximum = new decimal(new int[] {
            60000,
            0,
            0,
            0});
			this.nud_Interval.Minimum = new decimal(new int[] {
            50,
            0,
            0,
            0});
			this.nud_Interval.Name = "nud_Interval";
			this.nud_Interval.Size = new System.Drawing.Size(68, 22);
			this.nud_Interval.TabIndex = 40;
			this.nud_Interval.Value = new decimal(new int[] {
            300,
            0,
            0,
            0});
			this.nud_Interval.ValueChanged += new System.EventHandler(this.nud_Interval_ValueChanged);
			// 
			// lbl_Interval
			// 
			this.lbl_Interval.AutoSize = true;
			this.lbl_Interval.Location = new System.Drawing.Point(144, 18);
			this.lbl_Interval.Name = "lbl_Interval";
			this.lbl_Interval.Size = new System.Drawing.Size(48, 14);
			this.lbl_Interval.TabIndex = 39;
			this.lbl_Interval.Text = "Interval";
			// 
			// chk_Monitor
			// 
			this.chk_Monitor.AutoSize = true;
			this.chk_Monitor.Location = new System.Drawing.Point(10, 21);
			this.chk_Monitor.Name = "chk_Monitor";
			this.chk_Monitor.Size = new System.Drawing.Size(66, 16);
			this.chk_Monitor.TabIndex = 38;
			this.chk_Monitor.Text = "Monitor";
			this.chk_Monitor.UseVisualStyleBackColor = true;
			this.chk_Monitor.CheckedChanged += new System.EventHandler(this.chk_Monitor_CheckedChanged);
			// 
			// txt_CommandPosition
			// 
			this.txt_CommandPosition.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_CommandPosition.Location = new System.Drawing.Point(167, 179);
			this.txt_CommandPosition.Name = "txt_CommandPosition";
			this.txt_CommandPosition.ReadOnly = true;
			this.txt_CommandPosition.Size = new System.Drawing.Size(127, 22);
			this.txt_CommandPosition.TabIndex = 37;
			this.txt_CommandPosition.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// txt_EncoderPosition
			// 
			this.txt_EncoderPosition.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_EncoderPosition.Location = new System.Drawing.Point(167, 152);
			this.txt_EncoderPosition.Name = "txt_EncoderPosition";
			this.txt_EncoderPosition.ReadOnly = true;
			this.txt_EncoderPosition.Size = new System.Drawing.Size(127, 22);
			this.txt_EncoderPosition.TabIndex = 36;
			this.txt_EncoderPosition.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// lbl_CommandPos
			// 
			this.lbl_CommandPos.AutoSize = true;
			this.lbl_CommandPos.Location = new System.Drawing.Point(6, 182);
			this.lbl_CommandPos.Name = "lbl_CommandPos";
			this.lbl_CommandPos.Size = new System.Drawing.Size(148, 14);
			this.lbl_CommandPos.TabIndex = 35;
			this.lbl_CommandPos.Text = "Command Pos.(40007..8)";
			// 
			// lbl_EncoderPos
			// 
			this.lbl_EncoderPos.AutoSize = true;
			this.lbl_EncoderPos.Location = new System.Drawing.Point(6, 155);
			this.lbl_EncoderPos.Name = "lbl_EncoderPos";
			this.lbl_EncoderPos.Size = new System.Drawing.Size(139, 14);
			this.lbl_EncoderPos.TabIndex = 34;
			this.lbl_EncoderPos.Text = "Encoder Pos.(40005..6)";
			// 
			// txt_TargetSpeed
			// 
			this.txt_TargetSpeed.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_TargetSpeed.Location = new System.Drawing.Point(167, 125);
			this.txt_TargetSpeed.Name = "txt_TargetSpeed";
			this.txt_TargetSpeed.ReadOnly = true;
			this.txt_TargetSpeed.Size = new System.Drawing.Size(127, 22);
			this.txt_TargetSpeed.TabIndex = 33;
			this.txt_TargetSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// lbl_TargetSpeed
			// 
			this.lbl_TargetSpeed.AutoSize = true;
			this.lbl_TargetSpeed.Location = new System.Drawing.Point(6, 129);
			this.lbl_TargetSpeed.Name = "lbl_TargetSpeed";
			this.lbl_TargetSpeed.Size = new System.Drawing.Size(128, 14);
			this.lbl_TargetSpeed.TabIndex = 32;
			this.lbl_TargetSpeed.Text = "Target Speed(40012)";
			// 
			// txt_ActualSpeed
			// 
			this.txt_ActualSpeed.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_ActualSpeed.Location = new System.Drawing.Point(167, 98);
			this.txt_ActualSpeed.Name = "txt_ActualSpeed";
			this.txt_ActualSpeed.ReadOnly = true;
			this.txt_ActualSpeed.Size = new System.Drawing.Size(127, 22);
			this.txt_ActualSpeed.TabIndex = 31;
			this.txt_ActualSpeed.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// lbl_ActualSpeed
			// 
			this.lbl_ActualSpeed.AutoSize = true;
			this.lbl_ActualSpeed.Location = new System.Drawing.Point(6, 101);
			this.lbl_ActualSpeed.Name = "lbl_ActualSpeed";
			this.lbl_ActualSpeed.Size = new System.Drawing.Size(125, 14);
			this.lbl_ActualSpeed.TabIndex = 30;
			this.lbl_ActualSpeed.Text = "Actual Speed(40011)";
			// 
			// txt_AlarmCode
			// 
			this.txt_AlarmCode.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_AlarmCode.Location = new System.Drawing.Point(167, 71);
			this.txt_AlarmCode.Name = "txt_AlarmCode";
			this.txt_AlarmCode.ReadOnly = true;
			this.txt_AlarmCode.Size = new System.Drawing.Size(127, 22);
			this.txt_AlarmCode.TabIndex = 29;
			this.txt_AlarmCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// lbl_AlarmCode
			// 
			this.lbl_AlarmCode.AutoSize = true;
			this.lbl_AlarmCode.Location = new System.Drawing.Point(6, 74);
			this.lbl_AlarmCode.Name = "lbl_AlarmCode";
			this.lbl_AlarmCode.Size = new System.Drawing.Size(114, 14);
			this.lbl_AlarmCode.TabIndex = 28;
			this.lbl_AlarmCode.Text = "Alarm Code(40001)";
			// 
			// lbl_DriveStatus
			// 
			this.lbl_DriveStatus.AutoSize = true;
			this.lbl_DriveStatus.Location = new System.Drawing.Point(6, 47);
			this.lbl_DriveStatus.Name = "lbl_DriveStatus";
			this.lbl_DriveStatus.Size = new System.Drawing.Size(118, 14);
			this.lbl_DriveStatus.TabIndex = 11;
			this.lbl_DriveStatus.Text = "Drive Status(40002)";
			// 
			// txt_DriveStatus
			// 
			this.txt_DriveStatus.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.txt_DriveStatus.Location = new System.Drawing.Point(167, 44);
			this.txt_DriveStatus.Name = "txt_DriveStatus";
			this.txt_DriveStatus.ReadOnly = true;
			this.txt_DriveStatus.Size = new System.Drawing.Size(127, 22);
			this.txt_DriveStatus.TabIndex = 27;
			this.txt_DriveStatus.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			// 
			// grp_AbsMove
			// 
			this.grp_AbsMove.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.grp_AbsMove.Controls.Add(this.lbl_AbsMovePosUnit);
			this.grp_AbsMove.Controls.Add(this.btn_AbsMoveStop);
			this.grp_AbsMove.Controls.Add(this.btn_AbsMoveStart);
			this.grp_AbsMove.Controls.Add(this.nud_AbsMovePos);
			this.grp_AbsMove.Controls.Add(this.lbl_AbsMovePos);
			this.grp_AbsMove.Location = new System.Drawing.Point(6, 87);
			this.grp_AbsMove.Name = "grp_AbsMove";
			this.grp_AbsMove.Size = new System.Drawing.Size(280, 78);
			this.grp_AbsMove.TabIndex = 19;
			this.grp_AbsMove.TabStop = false;
			this.grp_AbsMove.Text = "Absolute Move";
			// 
			// lbl_AbsMovePosUnit
			// 
			this.lbl_AbsMovePosUnit.AutoSize = true;
			this.lbl_AbsMovePosUnit.Location = new System.Drawing.Point(239, 25);
			this.lbl_AbsMovePosUnit.Name = "lbl_AbsMovePosUnit";
			this.lbl_AbsMovePosUnit.Size = new System.Drawing.Size(38, 14);
			this.lbl_AbsMovePosUnit.TabIndex = 23;
			this.lbl_AbsMovePosUnit.Text = "Steps";
			// 
			// btn_AbsMoveStop
			// 
			this.btn_AbsMoveStop.Enabled = false;
			this.btn_AbsMoveStop.Location = new System.Drawing.Point(137, 47);
			this.btn_AbsMoveStop.Name = "btn_AbsMoveStop";
			this.btn_AbsMoveStop.Size = new System.Drawing.Size(100, 23);
			this.btn_AbsMoveStop.TabIndex = 22;
			this.btn_AbsMoveStop.Text = "Stop";
			this.btn_AbsMoveStop.UseVisualStyleBackColor = true;
			this.btn_AbsMoveStop.Click += new System.EventHandler(this.btn_AbsMoveStop_Click);
			// 
			// btn_AbsMoveStart
			// 
			this.btn_AbsMoveStart.Enabled = false;
			this.btn_AbsMoveStart.Location = new System.Drawing.Point(31, 47);
			this.btn_AbsMoveStart.Name = "btn_AbsMoveStart";
			this.btn_AbsMoveStart.Size = new System.Drawing.Size(100, 23);
			this.btn_AbsMoveStart.TabIndex = 21;
			this.btn_AbsMoveStart.Text = "Start";
			this.btn_AbsMoveStart.UseVisualStyleBackColor = true;
			this.btn_AbsMoveStart.Click += new System.EventHandler(this.btn_AbsMoveStart_Click);
			// 
			// nud_AbsMovePos
			// 
			this.nud_AbsMovePos.Location = new System.Drawing.Point(137, 20);
			this.nud_AbsMovePos.Maximum = new decimal(new int[] {
            2147483647,
            0,
            0,
            0});
			this.nud_AbsMovePos.Minimum = new decimal(new int[] {
            2147483647,
            0,
            0,
            -2147483648});
			this.nud_AbsMovePos.Name = "nud_AbsMovePos";
			this.nud_AbsMovePos.Size = new System.Drawing.Size(102, 22);
			this.nud_AbsMovePos.TabIndex = 20;
			this.nud_AbsMovePos.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_AbsMovePos.Value = new decimal(new int[] {
            20000,
            0,
            0,
            0});
			// 
			// lbl_AbsMovePos
			// 
			this.lbl_AbsMovePos.AutoSize = true;
			this.lbl_AbsMovePos.Location = new System.Drawing.Point(6, 24);
			this.lbl_AbsMovePos.Name = "lbl_AbsMovePos";
			this.lbl_AbsMovePos.Size = new System.Drawing.Size(123, 14);
			this.lbl_AbsMovePos.TabIndex = 19;
			this.lbl_AbsMovePos.Text = "Position(400031..32)";
			// 
			// nud_PositionModeDeceleration
			// 
			this.nud_PositionModeDeceleration.DecimalPlaces = 3;
			this.nud_PositionModeDeceleration.Location = new System.Drawing.Point(131, 62);
			this.nud_PositionModeDeceleration.Maximum = new decimal(new int[] {
            5461167,
            0,
            0,
            196608});
			this.nud_PositionModeDeceleration.Minimum = new decimal(new int[] {
            167,
            0,
            0,
            196608});
			this.nud_PositionModeDeceleration.Name = "nud_PositionModeDeceleration";
			this.nud_PositionModeDeceleration.Size = new System.Drawing.Size(114, 22);
			this.nud_PositionModeDeceleration.TabIndex = 18;
			this.nud_PositionModeDeceleration.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_PositionModeDeceleration.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
			// 
			// lbl_PosModeDecel
			// 
			this.lbl_PosModeDecel.AutoSize = true;
			this.lbl_PosModeDecel.Location = new System.Drawing.Point(6, 65);
			this.lbl_PosModeDecel.Name = "lbl_PosModeDecel";
			this.lbl_PosModeDecel.Size = new System.Drawing.Size(120, 14);
			this.lbl_PosModeDecel.TabIndex = 17;
			this.lbl_PosModeDecel.Text = "Deceleration(40029)";
			// 
			// nud_PositionModeAcceleration
			// 
			this.nud_PositionModeAcceleration.DecimalPlaces = 3;
			this.nud_PositionModeAcceleration.Location = new System.Drawing.Point(131, 35);
			this.nud_PositionModeAcceleration.Maximum = new decimal(new int[] {
            5461167,
            0,
            0,
            196608});
			this.nud_PositionModeAcceleration.Minimum = new decimal(new int[] {
            167,
            0,
            0,
            196608});
			this.nud_PositionModeAcceleration.Name = "nud_PositionModeAcceleration";
			this.nud_PositionModeAcceleration.Size = new System.Drawing.Size(114, 22);
			this.nud_PositionModeAcceleration.TabIndex = 16;
			this.nud_PositionModeAcceleration.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_PositionModeAcceleration.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
			// 
			// nud_PositionModeVelocity
			// 
			this.nud_PositionModeVelocity.DecimalPlaces = 2;
			this.nud_PositionModeVelocity.Location = new System.Drawing.Point(131, 8);
			this.nud_PositionModeVelocity.Minimum = new decimal(new int[] {
            25,
            0,
            0,
            196608});
			this.nud_PositionModeVelocity.Name = "nud_PositionModeVelocity";
			this.nud_PositionModeVelocity.Size = new System.Drawing.Size(114, 22);
			this.nud_PositionModeVelocity.TabIndex = 15;
			this.nud_PositionModeVelocity.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_PositionModeVelocity.Value = new decimal(new int[] {
            10,
            0,
            0,
            0});
			// 
			// lbl_PosModeAccel
			// 
			this.lbl_PosModeAccel.AutoSize = true;
			this.lbl_PosModeAccel.Location = new System.Drawing.Point(6, 37);
			this.lbl_PosModeAccel.Name = "lbl_PosModeAccel";
			this.lbl_PosModeAccel.Size = new System.Drawing.Size(119, 14);
			this.lbl_PosModeAccel.TabIndex = 14;
			this.lbl_PosModeAccel.Text = "Acceleration(40028)";
			// 
			// lbl_PosModeVelocity
			// 
			this.lbl_PosModeVelocity.AutoSize = true;
			this.lbl_PosModeVelocity.Location = new System.Drawing.Point(6, 13);
			this.lbl_PosModeVelocity.Name = "lbl_PosModeVelocity";
			this.lbl_PosModeVelocity.Size = new System.Drawing.Size(95, 14);
			this.lbl_PosModeVelocity.TabIndex = 13;
			this.lbl_PosModeVelocity.Text = "Velocity(40030)";
			// 
			// tc_Motion
			// 
			this.tc_Motion.Controls.Add(this.tp_PosMode);
			this.tc_Motion.Controls.Add(this.tp_QProgram);
			this.tc_Motion.Controls.Add(this.tp_Jog);
			this.tc_Motion.Location = new System.Drawing.Point(12, 71);
			this.tc_Motion.Name = "tc_Motion";
			this.tc_Motion.SelectedIndex = 0;
			this.tc_Motion.Size = new System.Drawing.Size(300, 310);
			this.tc_Motion.TabIndex = 34;
			// 
			// tp_PosMode
			// 
			this.tp_PosMode.Controls.Add(this.lbl_PosModeAccelUnit);
			this.tp_PosMode.Controls.Add(this.lbl_PosModeDecelUnit);
			this.tp_PosMode.Controls.Add(this.lbl_PosModeVelocityUnit);
			this.tp_PosMode.Controls.Add(this.grp_RelMove);
			this.tp_PosMode.Controls.Add(this.grp_AbsMove);
			this.tp_PosMode.Controls.Add(this.lbl_PosModeVelocity);
			this.tp_PosMode.Controls.Add(this.nud_PositionModeDeceleration);
			this.tp_PosMode.Controls.Add(this.lbl_PosModeAccel);
			this.tp_PosMode.Controls.Add(this.lbl_PosModeDecel);
			this.tp_PosMode.Controls.Add(this.nud_PositionModeVelocity);
			this.tp_PosMode.Controls.Add(this.nud_PositionModeAcceleration);
			this.tp_PosMode.Location = new System.Drawing.Point(4, 23);
			this.tp_PosMode.Name = "tp_PosMode";
			this.tp_PosMode.Padding = new System.Windows.Forms.Padding(3);
			this.tp_PosMode.Size = new System.Drawing.Size(292, 283);
			this.tp_PosMode.TabIndex = 0;
			this.tp_PosMode.Text = "Position Mode";
			this.tp_PosMode.UseVisualStyleBackColor = true;
			// 
			// lbl_PosModeAccelUnit
			// 
			this.lbl_PosModeAccelUnit.AutoSize = true;
			this.lbl_PosModeAccelUnit.Location = new System.Drawing.Point(251, 37);
			this.lbl_PosModeAccelUnit.Name = "lbl_PosModeAccelUnit";
			this.lbl_PosModeAccelUnit.Size = new System.Drawing.Size(33, 14);
			this.lbl_PosModeAccelUnit.TabIndex = 23;
			this.lbl_PosModeAccelUnit.Text = "rps/s";
			// 
			// lbl_PosModeDecelUnit
			// 
			this.lbl_PosModeDecelUnit.AutoSize = true;
			this.lbl_PosModeDecelUnit.Location = new System.Drawing.Point(251, 65);
			this.lbl_PosModeDecelUnit.Name = "lbl_PosModeDecelUnit";
			this.lbl_PosModeDecelUnit.Size = new System.Drawing.Size(33, 14);
			this.lbl_PosModeDecelUnit.TabIndex = 22;
			this.lbl_PosModeDecelUnit.Text = "rps/s";
			// 
			// lbl_PosModeVelocityUnit
			// 
			this.lbl_PosModeVelocityUnit.AutoSize = true;
			this.lbl_PosModeVelocityUnit.Location = new System.Drawing.Point(251, 13);
			this.lbl_PosModeVelocityUnit.Name = "lbl_PosModeVelocityUnit";
			this.lbl_PosModeVelocityUnit.Size = new System.Drawing.Size(23, 14);
			this.lbl_PosModeVelocityUnit.TabIndex = 21;
			this.lbl_PosModeVelocityUnit.Text = "rps";
			// 
			// grp_RelMove
			// 
			this.grp_RelMove.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left)
						| System.Windows.Forms.AnchorStyles.Right)));
			this.grp_RelMove.Controls.Add(this.lbl_RelMovePosUnit);
			this.grp_RelMove.Controls.Add(this.cmb_RelMoveDir);
			this.grp_RelMove.Controls.Add(this.lbl_RelMoveDir);
			this.grp_RelMove.Controls.Add(this.btn_RelMoveStop);
			this.grp_RelMove.Controls.Add(this.btn_RelMoveStart);
			this.grp_RelMove.Controls.Add(this.nud_ReMovePos);
			this.grp_RelMove.Controls.Add(this.lbl_RelMovePos);
			this.grp_RelMove.Location = new System.Drawing.Point(6, 171);
			this.grp_RelMove.Name = "grp_RelMove";
			this.grp_RelMove.Size = new System.Drawing.Size(280, 103);
			this.grp_RelMove.TabIndex = 20;
			this.grp_RelMove.TabStop = false;
			this.grp_RelMove.Text = "Relative Move";
			// 
			// lbl_RelMovePosUnit
			// 
			this.lbl_RelMovePosUnit.AutoSize = true;
			this.lbl_RelMovePosUnit.Location = new System.Drawing.Point(239, 24);
			this.lbl_RelMovePosUnit.Name = "lbl_RelMovePosUnit";
			this.lbl_RelMovePosUnit.Size = new System.Drawing.Size(38, 14);
			this.lbl_RelMovePosUnit.TabIndex = 25;
			this.lbl_RelMovePosUnit.Text = "Steps";
			// 
			// cmb_RelMoveDir
			// 
			this.cmb_RelMoveDir.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cmb_RelMoveDir.FormattingEnabled = true;
			this.cmb_RelMoveDir.Items.AddRange(new object[] {
            "CW",
            "CCW"});
			this.cmb_RelMoveDir.Location = new System.Drawing.Point(137, 47);
			this.cmb_RelMoveDir.Name = "cmb_RelMoveDir";
			this.cmb_RelMoveDir.Size = new System.Drawing.Size(102, 22);
			this.cmb_RelMoveDir.TabIndex = 24;
			// 
			// lbl_RelMoveDir
			// 
			this.lbl_RelMoveDir.AutoSize = true;
			this.lbl_RelMoveDir.Location = new System.Drawing.Point(6, 50);
			this.lbl_RelMoveDir.Name = "lbl_RelMoveDir";
			this.lbl_RelMoveDir.Size = new System.Drawing.Size(55, 14);
			this.lbl_RelMoveDir.TabIndex = 23;
			this.lbl_RelMoveDir.Text = "Direction";
			// 
			// btn_RelMoveStop
			// 
			this.btn_RelMoveStop.Enabled = false;
			this.btn_RelMoveStop.Location = new System.Drawing.Point(137, 73);
			this.btn_RelMoveStop.Name = "btn_RelMoveStop";
			this.btn_RelMoveStop.Size = new System.Drawing.Size(100, 23);
			this.btn_RelMoveStop.TabIndex = 22;
			this.btn_RelMoveStop.Text = "Stop";
			this.btn_RelMoveStop.UseVisualStyleBackColor = true;
			this.btn_RelMoveStop.Click += new System.EventHandler(this.btn_RelMoveStop_Click);
			// 
			// btn_RelMoveStart
			// 
			this.btn_RelMoveStart.Enabled = false;
			this.btn_RelMoveStart.Location = new System.Drawing.Point(31, 73);
			this.btn_RelMoveStart.Name = "btn_RelMoveStart";
			this.btn_RelMoveStart.Size = new System.Drawing.Size(100, 23);
			this.btn_RelMoveStart.TabIndex = 21;
			this.btn_RelMoveStart.Text = "Start";
			this.btn_RelMoveStart.UseVisualStyleBackColor = true;
			this.btn_RelMoveStart.Click += new System.EventHandler(this.btn_RelMoveStart_Click);
			// 
			// nud_ReMovePos
			// 
			this.nud_ReMovePos.Location = new System.Drawing.Point(137, 20);
			this.nud_ReMovePos.Maximum = new decimal(new int[] {
            2147483647,
            0,
            0,
            0});
			this.nud_ReMovePos.Name = "nud_ReMovePos";
			this.nud_ReMovePos.Size = new System.Drawing.Size(102, 22);
			this.nud_ReMovePos.TabIndex = 20;
			this.nud_ReMovePos.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_ReMovePos.Value = new decimal(new int[] {
            20000,
            0,
            0,
            0});
			// 
			// lbl_RelMovePos
			// 
			this.lbl_RelMovePos.AutoSize = true;
			this.lbl_RelMovePos.Location = new System.Drawing.Point(6, 20);
			this.lbl_RelMovePos.Name = "lbl_RelMovePos";
			this.lbl_RelMovePos.Size = new System.Drawing.Size(116, 14);
			this.lbl_RelMovePos.TabIndex = 19;
			this.lbl_RelMovePos.Text = "Position(40031..32)";
			// 
			// tp_QProgram
			// 
			this.tp_QProgram.Controls.Add(this.btn_StopQProgram);
			this.tp_QProgram.Controls.Add(this.btn_ExecuteQProgram);
			this.tp_QProgram.Controls.Add(this.lbl_QProgramSegment);
			this.tp_QProgram.Controls.Add(this.nud_QProgramSegment);
			this.tp_QProgram.Location = new System.Drawing.Point(4, 21);
			this.tp_QProgram.Name = "tp_QProgram";
			this.tp_QProgram.Padding = new System.Windows.Forms.Padding(3);
			this.tp_QProgram.Size = new System.Drawing.Size(292, 285);
			this.tp_QProgram.TabIndex = 1;
			this.tp_QProgram.Text = "Q Program";
			this.tp_QProgram.UseVisualStyleBackColor = true;
			// 
			// btn_StopQProgram
			// 
			this.btn_StopQProgram.Enabled = false;
			this.btn_StopQProgram.Location = new System.Drawing.Point(137, 44);
			this.btn_StopQProgram.Name = "btn_StopQProgram";
			this.btn_StopQProgram.Size = new System.Drawing.Size(75, 25);
			this.btn_StopQProgram.TabIndex = 19;
			this.btn_StopQProgram.Text = "Stop";
			this.btn_StopQProgram.UseVisualStyleBackColor = true;
			this.btn_StopQProgram.Click += new System.EventHandler(this.btn_StopQProgram_Click);
			// 
			// btn_ExecuteQProgram
			// 
			this.btn_ExecuteQProgram.Enabled = false;
			this.btn_ExecuteQProgram.Location = new System.Drawing.Point(55, 44);
			this.btn_ExecuteQProgram.Name = "btn_ExecuteQProgram";
			this.btn_ExecuteQProgram.Size = new System.Drawing.Size(75, 25);
			this.btn_ExecuteQProgram.TabIndex = 18;
			this.btn_ExecuteQProgram.Text = "Execute";
			this.btn_ExecuteQProgram.UseVisualStyleBackColor = true;
			this.btn_ExecuteQProgram.Click += new System.EventHandler(this.btn_ExecuteQProgram_Click);
			// 
			// tp_Jog
			// 
			this.tp_Jog.Controls.Add(this.lbl_JogDecelUnit);
			this.tp_Jog.Controls.Add(this.nud_JogDecel);
			this.tp_Jog.Controls.Add(this.lbl_JogDecel);
			this.tp_Jog.Controls.Add(this.lbl_JogAccelUnit);
			this.tp_Jog.Controls.Add(this.nud_JogSpeed);
			this.tp_Jog.Controls.Add(this.nud_JogAccel);
			this.tp_Jog.Controls.Add(this.lbl_JogSpeed);
			this.tp_Jog.Controls.Add(this.lbl_JogSpeedUnit);
			this.tp_Jog.Controls.Add(this.btn_CWJog);
			this.tp_Jog.Controls.Add(this.btn_CCWJog);
			this.tp_Jog.Controls.Add(this.lbl_JogAccel);
			this.tp_Jog.Location = new System.Drawing.Point(4, 23);
			this.tp_Jog.Name = "tp_Jog";
			this.tp_Jog.Padding = new System.Windows.Forms.Padding(3);
			this.tp_Jog.Size = new System.Drawing.Size(292, 283);
			this.tp_Jog.TabIndex = 2;
			this.tp_Jog.Text = "Jog";
			this.tp_Jog.UseVisualStyleBackColor = true;
			// 
			// lbl_JogDecelUnit
			// 
			this.lbl_JogDecelUnit.AutoSize = true;
			this.lbl_JogDecelUnit.Location = new System.Drawing.Point(251, 65);
			this.lbl_JogDecelUnit.Name = "lbl_JogDecelUnit";
			this.lbl_JogDecelUnit.Size = new System.Drawing.Size(33, 14);
			this.lbl_JogDecelUnit.TabIndex = 16;
			this.lbl_JogDecelUnit.Text = "rps/s";
			// 
			// nud_JogDecel
			// 
			this.nud_JogDecel.DecimalPlaces = 3;
			this.nud_JogDecel.Location = new System.Drawing.Point(131, 62);
			this.nud_JogDecel.Maximum = new decimal(new int[] {
            5461167,
            0,
            0,
            196608});
			this.nud_JogDecel.Minimum = new decimal(new int[] {
            167,
            0,
            0,
            196608});
			this.nud_JogDecel.Name = "nud_JogDecel";
			this.nud_JogDecel.Size = new System.Drawing.Size(114, 22);
			this.nud_JogDecel.TabIndex = 15;
			this.nud_JogDecel.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_JogDecel.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
			// 
			// lbl_JogDecel
			// 
			this.lbl_JogDecel.AutoSize = true;
			this.lbl_JogDecel.Location = new System.Drawing.Point(6, 65);
			this.lbl_JogDecel.Name = "lbl_JogDecel";
			this.lbl_JogDecel.Size = new System.Drawing.Size(120, 14);
			this.lbl_JogDecel.TabIndex = 14;
			this.lbl_JogDecel.Text = "Deceleration(40048)";
			// 
			// pnl_ToolBar
			// 
			this.pnl_ToolBar.Controls.Add(this.nud_NodeID);
			this.pnl_ToolBar.Controls.Add(this.lbl_NodeID);
			this.pnl_ToolBar.Controls.Add(this.pic_Sep2);
			this.pnl_ToolBar.Controls.Add(this.pic_Sep1);
			this.pnl_ToolBar.Controls.Add(this.btn_Close);
			this.pnl_ToolBar.Controls.Add(this.btn_Open);
			this.pnl_ToolBar.Controls.Add(this.btn_Disable);
			this.pnl_ToolBar.Controls.Add(this.cmb_BaudRate);
			this.pnl_ToolBar.Controls.Add(this.pic_MOONS);
			this.pnl_ToolBar.Controls.Add(this.lbl_BaudRate);
			this.pnl_ToolBar.Controls.Add(this.btn_Stop);
			this.pnl_ToolBar.Controls.Add(this.lbl_COMPort);
			this.pnl_ToolBar.Controls.Add(this.cmb_SerialPort);
			this.pnl_ToolBar.Controls.Add(this.btn_Enable);
			this.pnl_ToolBar.Controls.Add(this.btn_AlarmReset);
			this.pnl_ToolBar.Dock = System.Windows.Forms.DockStyle.Top;
			this.pnl_ToolBar.Location = new System.Drawing.Point(0, 0);
			this.pnl_ToolBar.Name = "pnl_ToolBar";
			this.pnl_ToolBar.Size = new System.Drawing.Size(952, 65);
			this.pnl_ToolBar.TabIndex = 35;
			// 
			// nud_NodeID
			// 
			this.nud_NodeID.Location = new System.Drawing.Point(234, 35);
			this.nud_NodeID.Maximum = new decimal(new int[] {
            32,
            0,
            0,
            0});
			this.nud_NodeID.Minimum = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.nud_NodeID.Name = "nud_NodeID";
			this.nud_NodeID.Size = new System.Drawing.Size(80, 22);
			this.nud_NodeID.TabIndex = 35;
			this.nud_NodeID.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
			this.nud_NodeID.Value = new decimal(new int[] {
            1,
            0,
            0,
            0});
			this.nud_NodeID.ValueChanged += new System.EventHandler(this.nud_NodeID_ValueChanged);
			// 
			// lbl_NodeID
			// 
			this.lbl_NodeID.AutoSize = true;
			this.lbl_NodeID.BackColor = System.Drawing.Color.Transparent;
			this.lbl_NodeID.Location = new System.Drawing.Point(169, 39);
			this.lbl_NodeID.Name = "lbl_NodeID";
			this.lbl_NodeID.Size = new System.Drawing.Size(52, 14);
			this.lbl_NodeID.TabIndex = 34;
			this.lbl_NodeID.Text = "Node ID";
			// 
			// pic_Sep2
			// 
			this.pic_Sep2.Location = new System.Drawing.Point(489, 0);
			this.pic_Sep2.Name = "pic_Sep2";
			this.pic_Sep2.Size = new System.Drawing.Size(4, 70);
			this.pic_Sep2.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
			this.pic_Sep2.TabIndex = 33;
			this.pic_Sep2.TabStop = false;
			// 
			// pic_Sep1
			// 
			this.pic_Sep1.Location = new System.Drawing.Point(153, 0);
			this.pic_Sep1.Name = "pic_Sep1";
			this.pic_Sep1.Size = new System.Drawing.Size(4, 70);
			this.pic_Sep1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
			this.pic_Sep1.TabIndex = 32;
			this.pic_Sep1.TabStop = false;
			// 
			// btn_Close
			// 
			this.btn_Close.Enabled = false;
			this.btn_Close.Location = new System.Drawing.Point(401, 34);
			this.btn_Close.Name = "btn_Close";
			this.btn_Close.Size = new System.Drawing.Size(75, 23);
			this.btn_Close.TabIndex = 14;
			this.btn_Close.Text = "Close";
			this.btn_Close.UseVisualStyleBackColor = true;
			this.btn_Close.Click += new System.EventHandler(this.btn_Close_Click);
			// 
			// btn_Open
			// 
			this.btn_Open.Location = new System.Drawing.Point(320, 33);
			this.btn_Open.Name = "btn_Open";
			this.btn_Open.Size = new System.Drawing.Size(75, 23);
			this.btn_Open.TabIndex = 13;
			this.btn_Open.Text = "Open";
			this.btn_Open.UseVisualStyleBackColor = true;
			this.btn_Open.Click += new System.EventHandler(this.btn_Open_Click);
			// 
			// btn_Disable
			// 
			this.btn_Disable.Enabled = false;
			this.btn_Disable.Location = new System.Drawing.Point(598, 9);
			this.btn_Disable.Name = "btn_Disable";
			this.btn_Disable.Size = new System.Drawing.Size(90, 49);
			this.btn_Disable.TabIndex = 31;
			this.btn_Disable.Text = "Disable";
			this.btn_Disable.UseVisualStyleBackColor = true;
			this.btn_Disable.Click += new System.EventHandler(this.btn_Disable_Click);
			// 
			// cmb_BaudRate
			// 
			this.cmb_BaudRate.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cmb_BaudRate.FormattingEnabled = true;
			this.cmb_BaudRate.Items.AddRange(new object[] {
            "9600",
            "19200",
            "38400",
            "57600",
            "115200"});
			this.cmb_BaudRate.Location = new System.Drawing.Point(385, 6);
			this.cmb_BaudRate.Name = "cmb_BaudRate";
			this.cmb_BaudRate.Size = new System.Drawing.Size(91, 22);
			this.cmb_BaudRate.TabIndex = 12;
			// 
			// pic_MOONS
			// 
			this.pic_MOONS.Image = global::ModbusRTU_CS.Properties.Resources.MOONS;
			this.pic_MOONS.Location = new System.Drawing.Point(13, 7);
			this.pic_MOONS.Name = "pic_MOONS";
			this.pic_MOONS.Size = new System.Drawing.Size(130, 50);
			this.pic_MOONS.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
			this.pic_MOONS.TabIndex = 30;
			this.pic_MOONS.TabStop = false;
			// 
			// lbl_BaudRate
			// 
			this.lbl_BaudRate.AutoSize = true;
			this.lbl_BaudRate.BackColor = System.Drawing.Color.Transparent;
			this.lbl_BaudRate.Location = new System.Drawing.Point(320, 10);
			this.lbl_BaudRate.Name = "lbl_BaudRate";
			this.lbl_BaudRate.Size = new System.Drawing.Size(63, 14);
			this.lbl_BaudRate.TabIndex = 11;
			this.lbl_BaudRate.Text = "Baud Rate";
			// 
			// btn_Stop
			// 
			this.btn_Stop.BackColor = System.Drawing.Color.Red;
			this.btn_Stop.Enabled = false;
			this.btn_Stop.Font = new System.Drawing.Font("Tahoma", 15.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.btn_Stop.ForeColor = System.Drawing.Color.Yellow;
			this.btn_Stop.Location = new System.Drawing.Point(790, 9);
			this.btn_Stop.Name = "btn_Stop";
			this.btn_Stop.Size = new System.Drawing.Size(90, 49);
			this.btn_Stop.TabIndex = 17;
			this.btn_Stop.Text = "Stop";
			this.btn_Stop.UseVisualStyleBackColor = false;
			this.btn_Stop.Click += new System.EventHandler(this.btn_Stop_Click);
			// 
			// lbl_COMPort
			// 
			this.lbl_COMPort.AutoSize = true;
			this.lbl_COMPort.BackColor = System.Drawing.Color.Transparent;
			this.lbl_COMPort.Location = new System.Drawing.Point(169, 13);
			this.lbl_COMPort.Name = "lbl_COMPort";
			this.lbl_COMPort.Size = new System.Drawing.Size(59, 14);
			this.lbl_COMPort.TabIndex = 10;
			this.lbl_COMPort.Text = "COM Port";
			// 
			// cmb_SerialPort
			// 
			this.cmb_SerialPort.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.cmb_SerialPort.FormattingEnabled = true;
			this.cmb_SerialPort.Location = new System.Drawing.Point(234, 9);
			this.cmb_SerialPort.Name = "cmb_SerialPort";
			this.cmb_SerialPort.Size = new System.Drawing.Size(80, 22);
			this.cmb_SerialPort.TabIndex = 9;
			// 
			// btn_Enable
			// 
			this.btn_Enable.Enabled = false;
			this.btn_Enable.Location = new System.Drawing.Point(502, 9);
			this.btn_Enable.Name = "btn_Enable";
			this.btn_Enable.Size = new System.Drawing.Size(90, 49);
			this.btn_Enable.TabIndex = 29;
			this.btn_Enable.Text = "Enable";
			this.btn_Enable.UseVisualStyleBackColor = true;
			this.btn_Enable.Click += new System.EventHandler(this.btn_Enable_Click);
			// 
			// btn_AlarmReset
			// 
			this.btn_AlarmReset.Enabled = false;
			this.btn_AlarmReset.Location = new System.Drawing.Point(694, 9);
			this.btn_AlarmReset.Name = "btn_AlarmReset";
			this.btn_AlarmReset.Size = new System.Drawing.Size(90, 49);
			this.btn_AlarmReset.TabIndex = 28;
			this.btn_AlarmReset.Text = "Alarm Reset";
			this.btn_AlarmReset.UseVisualStyleBackColor = true;
			this.btn_AlarmReset.Click += new System.EventHandler(this.btn_AlarmReset_Click);
			// 
			// MainForm
			// 
			this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
			this.ClientSize = new System.Drawing.Size(952, 606);
			this.Controls.Add(this.pnl_ToolBar);
			this.Controls.Add(this.tc_Motion);
			this.Controls.Add(this.grp_Monitor);
			this.Controls.Add(this.grp_Register);
			this.Controls.Add(this.grp_CommandHistory);
			this.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
			this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
			this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
			this.MaximizeBox = false;
			this.Name = "MainForm";
			this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "ModbusRTU C# Sample";
			this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.ModbusRTUForm_FormClosing);
			this.Load += new System.EventHandler(this.ModbusRTUForm_Load);
			this.grp_CommandHistory.ResumeLayout(false);
			this.grp_CommandHistory.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_RegNo)).EndInit();
			this.grp_SingleRegister.ResumeLayout(false);
			this.grp_SingleRegister.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_MultiRegCount)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_QProgramSegment)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogAccel)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogSpeed)).EndInit();
			this.grp_Register.ResumeLayout(false);
			this.grp_MultiRegister.ResumeLayout(false);
			this.grp_MultiRegister.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_MultiRegNo)).EndInit();
			this.grp_Monitor.ResumeLayout(false);
			this.grp_Monitor.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_Interval)).EndInit();
			this.grp_AbsMove.ResumeLayout(false);
			this.grp_AbsMove.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_AbsMovePos)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeDeceleration)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeAcceleration)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.nud_PositionModeVelocity)).EndInit();
			this.tc_Motion.ResumeLayout(false);
			this.tp_PosMode.ResumeLayout(false);
			this.tp_PosMode.PerformLayout();
			this.grp_RelMove.ResumeLayout(false);
			this.grp_RelMove.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_ReMovePos)).EndInit();
			this.tp_QProgram.ResumeLayout(false);
			this.tp_QProgram.PerformLayout();
			this.tp_Jog.ResumeLayout(false);
			this.tp_Jog.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_JogDecel)).EndInit();
			this.pnl_ToolBar.ResumeLayout(false);
			this.pnl_ToolBar.PerformLayout();
			((System.ComponentModel.ISupportInitialize)(this.nud_NodeID)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_Sep2)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_Sep1)).EndInit();
			((System.ComponentModel.ISupportInitialize)(this.pic_MOONS)).EndInit();
			this.ResumeLayout(false);

		}

		#endregion

		private System.Windows.Forms.Button btn_WriteSingleReg;
		private System.Windows.Forms.GroupBox grp_CommandHistory;
		private System.Windows.Forms.TextBox txt_CommandHistory;
		private System.Windows.Forms.ComboBox cmb_SerialPort;
		private System.Windows.Forms.Label lbl_COMPort;
		private System.Windows.Forms.Label lbl_SingleRegNo;
		private System.Windows.Forms.NumericUpDown nud_RegNo;
		private System.Windows.Forms.GroupBox grp_SingleRegister;
		private System.Windows.Forms.TextBox txt_SingleRegValue;
		private System.Windows.Forms.Label lbl_SingleRegValue;
		private System.Windows.Forms.ComboBox cmb_BaudRate;
		private System.Windows.Forms.Label lbl_BaudRate;
		private System.Windows.Forms.Button btn_Close;
		private System.Windows.Forms.Button btn_Open;
		private System.Windows.Forms.Label lbl_MultiRegCount;
		private System.Windows.Forms.NumericUpDown nud_MultiRegCount;
		private System.Windows.Forms.Button btn_ReadSingleReg;
		private System.Windows.Forms.Label lbl_QProgramSegment;
		private System.Windows.Forms.NumericUpDown nud_QProgramSegment;
		private System.Windows.Forms.Button btn_Stop;
		private System.Windows.Forms.Button btn_ClearCommandHistory;
		private System.Windows.Forms.Label lbl_JogAccel;
		private System.Windows.Forms.Button btn_CCWJog;
		private System.Windows.Forms.Button btn_CWJog;
		private System.Windows.Forms.Label lbl_JogSpeed;
		private System.Windows.Forms.Label lbl_JogAccelUnit;
		private System.Windows.Forms.NumericUpDown nud_JogAccel;
		private System.Windows.Forms.Label lbl_JogSpeedUnit;
		private System.Windows.Forms.NumericUpDown nud_JogSpeed;
		private System.Windows.Forms.Button btn_AlarmReset;
		private System.Windows.Forms.Button btn_Enable;
		private System.Windows.Forms.GroupBox grp_Register;
		private System.Windows.Forms.GroupBox grp_MultiRegister;
		private System.Windows.Forms.TextBox txt_MultiRegValue;
		private System.Windows.Forms.Label lbl_MultiRegValue;
		private System.Windows.Forms.Button btn_ReadMultiReg;
		private System.Windows.Forms.NumericUpDown nud_MultiRegNo;
		private System.Windows.Forms.Label lbl_MultiRegNo;
		private System.Windows.Forms.Button btn_WriteMultiReg;
		private System.Windows.Forms.GroupBox grp_Monitor;
		private System.Windows.Forms.TextBox txt_CommandPosition;
		private System.Windows.Forms.TextBox txt_EncoderPosition;
		private System.Windows.Forms.Label lbl_CommandPos;
		private System.Windows.Forms.Label lbl_EncoderPos;
		private System.Windows.Forms.TextBox txt_TargetSpeed;
		private System.Windows.Forms.Label lbl_TargetSpeed;
		private System.Windows.Forms.TextBox txt_ActualSpeed;
		private System.Windows.Forms.Label lbl_ActualSpeed;
		private System.Windows.Forms.TextBox txt_AlarmCode;
		private System.Windows.Forms.Label lbl_AlarmCode;
		private System.Windows.Forms.Label lbl_DriveStatus;
		private System.Windows.Forms.TextBox txt_DriveStatus;
		private System.Windows.Forms.GroupBox grp_AbsMove;
		private System.Windows.Forms.NumericUpDown nud_PositionModeDeceleration;
		private System.Windows.Forms.Label lbl_PosModeDecel;
		private System.Windows.Forms.NumericUpDown nud_PositionModeAcceleration;
		private System.Windows.Forms.NumericUpDown nud_PositionModeVelocity;
		private System.Windows.Forms.Label lbl_PosModeAccel;
		private System.Windows.Forms.Label lbl_PosModeVelocity;
		private System.Windows.Forms.TabControl tc_Motion;
		private System.Windows.Forms.TabPage tp_PosMode;
		private System.Windows.Forms.TabPage tp_QProgram;
		private System.Windows.Forms.TabPage tp_Jog;
		private System.Windows.Forms.Panel pnl_ToolBar;
		private System.Windows.Forms.PictureBox pic_MOONS;
		private System.Windows.Forms.Button btn_Disable;
		private System.Windows.Forms.Button btn_AbsMoveStop;
		private System.Windows.Forms.Button btn_AbsMoveStart;
		private System.Windows.Forms.NumericUpDown nud_AbsMovePos;
		private System.Windows.Forms.Label lbl_AbsMovePos;
		private System.Windows.Forms.GroupBox grp_RelMove;
		private System.Windows.Forms.ComboBox cmb_RelMoveDir;
		private System.Windows.Forms.Label lbl_RelMoveDir;
		private System.Windows.Forms.Button btn_RelMoveStop;
		private System.Windows.Forms.Button btn_RelMoveStart;
		private System.Windows.Forms.NumericUpDown nud_ReMovePos;
		private System.Windows.Forms.Label lbl_RelMovePos;
		private System.Windows.Forms.Label lbl_PosModeAccelUnit;
		private System.Windows.Forms.Label lbl_PosModeDecelUnit;
		private System.Windows.Forms.Label lbl_PosModeVelocityUnit;
		private System.Windows.Forms.Label lbl_AbsMovePosUnit;
		private System.Windows.Forms.Label lbl_RelMovePosUnit;
		private System.Windows.Forms.Label lbl_JogDecelUnit;
		private System.Windows.Forms.NumericUpDown nud_JogDecel;
		private System.Windows.Forms.Label lbl_JogDecel;
		private System.Windows.Forms.Button btn_ExecuteQProgram;
		private System.Windows.Forms.PictureBox pic_Sep1;
		private System.Windows.Forms.PictureBox pic_Sep2;
		private System.Windows.Forms.NumericUpDown nud_NodeID;
		private System.Windows.Forms.Label lbl_NodeID;
		private System.Windows.Forms.Button btn_StopQProgram;
        private System.Windows.Forms.CheckBox chk_Monitor;
        private System.Windows.Forms.Label lbl_Interval;
        private System.Windows.Forms.NumericUpDown nud_Interval;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.CheckBox chk_ShowReceived;
        private System.Windows.Forms.CheckBox chk_ShowSend;
	}
}