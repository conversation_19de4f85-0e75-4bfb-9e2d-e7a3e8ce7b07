#if !defined(AFX__IMAGEVIEW_H__D4268855_FD23_4A86_8EF2_F189F8B69277__INCLUDED_)
#define AFX__IMAGEVIEW_H__D4268855_FD23_4A86_8EF2_F189F8B69277__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000
// _ImageView.h : header file
//

/////////////////////////////////////////////////////////////////////////////
// C_ImageView view

#ifdef SDK_WITH_VISION
#include "..\\VisionDll\\_MILApp.h"
#include "..\\VisionDll\\_Video.h"
#endif

class C_ImageView : public CScrollView
{
public:
#ifdef SDK_WITH_VISION
	C_MILApp *mil;
	C_Video *vdo;
#endif
protected:
	C_ImageView();           // protected constructor used by dynamic creation
	DECLARE_DYNCREATE(C_ImageView)

// Attributes
public:
	virtual void OnInitialUpdate();     // first time after construct
// Operations
public:
	long MilDisplay,MilBuffer;
	double nFactor;
	long nSizeX,nSizeY;	
	CPoint SetScrollPosition(CPoint pt, BOOL bScrollToPosition);
	CWnd *pLblPicture;

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(C_ImageView)
	protected:
	virtual void OnDraw(CDC* pDC);      // overridden to draw this view
	//}}AFX_VIRTUAL

// Implementation
protected:
	virtual ~C_ImageView();
#ifdef _DEBUG
	virtual void AssertValid() const;
	virtual void Dump(CDumpContext& dc) const;
#endif

	// Generated message map functions
	//{{AFX_MSG(C_ImageView)
	afx_msg void OnRButtonDown(UINT nFlags, CPoint point);
	afx_msg void OnMenuImgZoom100();
	afx_msg void OnMenuImgZoomIn();
	afx_msg void OnMenuImgZoomOut();
	afx_msg void OnSize(UINT nType, int cx, int cy);
	afx_msg void OnMenuImgSave();
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};

/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX__IMAGEVIEW_H__D4268855_FD23_4A86_8EF2_F189F8B69277__INCLUDED_)
