﻿生成启动时间为 2025/7/21 3:40:37。
     1>项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\hermeticFrame\hermeticFrame.vcxproj”在节点 2 上(build 个目标)。
     1>InitializeBuildStatus:
         正在创建“.\..\..\temp\Hermetic\Debug\hermeticFrame.unsuccessfulbuild”，因为已指定“AlwaysCreate”。
       ClCompile:
         所有输出均为最新。
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\CL.exe /c /ZI /nologo /W3 /WX- /Od /Oy- /D WIN32 /D _WINDOWS /D _DEBUG /D _MBCS /D _AFXDLL /Gm /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Yu"StdAfx.h" /Fp".\..\..\temp\Hermetic\Debug\hermeticFrame.pch" /Fo".\..\..\temp\Hermetic\Debug\\" /Fd".\..\..\temp\Hermetic\Debug\vc100.pdb" /Gd /TP /analyze- /errorReport:prompt ProFlow.cpp
         ProFlow.cpp
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(1): warning C4627: “#include "ProFlow.h"”: 在查找预编译头使用时跳过
                 将指令添加到“StdAfx.h”或重新生成预编译头
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\oppo_mes_dll\resource.h(6): warning C4005: “IDC_EDIT_IP”: 宏重定义
                 d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\resource.h(126) : 参见“IDC_EDIT_IP”的前一个定义
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\oppo_mes_dll\resource.h(12): warning C4005: “IDC_EDIT_OPER”: 宏重定义
                 d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\resource.h(140) : 参见“IDC_EDIT_OPER”的前一个定义
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\hermeticframe.h(211): warning C4482: 使用了非标准扩展: 限定名中使用了枚举“e_Station”
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\hermeticframe.h(211): warning C4482: 使用了非标准扩展: 限定名中使用了枚举“e_Station”
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(1354): warning C4101: “bHM_OK”: 未引用的局部变量
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(2784): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(2789): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(2803): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(2940): warning C4800: “BOOL”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3219): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3220): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3221): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3222): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3223): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3224): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3225): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3226): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3227): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3233): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3235): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3237): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3238): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3239): warning C4800: “int”: 将值强制为布尔值“true”或“false”(性能警告)
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3529): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(3898): warning C4018: “<”: 有符号/无符号不匹配
     1>d:\asus\desktop\科瑞2\hermetic\hermetic\hermeticframe\proflow.cpp(4189): warning C4018: “<”: 有符号/无符号不匹配
         所有输出均为最新。
       ResourceCompile:
         所有输出均为最新。
       ManifestResourceCompile:
         所有输出均为最新。
       Link:
         C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\bin\link.exe /ERRORREPORT:PROMPT /OUT:".\..\exe\hermeticFrame.exe" /INCREMENTAL /NOLOGO /MANIFEST /ManifestFile:".\..\..\temp\Hermetic\Debug\hermeticFrame.exe.intermediate.manifest" /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /DEBUG /PDB:"D:\ASUS\Desktop\科瑞2\hermetic\hermetic\exe\hermeticFrame.pdb" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\..\exe\hermeticFrame.lib" /MACHINE:X86 .\..\..\temp\Hermetic\Debug\hermeticFrame.res
         .\..\..\temp\Hermetic\Debug\hermeticFrame.exe.embed.manifest.res
         .\..\..\temp\Hermetic\Debug\ChartAxis.obj
         .\..\..\temp\Hermetic\Debug\ChartAxisLabel.obj
         .\..\..\temp\Hermetic\Debug\ChartBarSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartCandlestickSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartCrossHairCursor.obj
         .\..\..\temp\Hermetic\Debug\ChartCtrl.obj
         .\..\..\temp\Hermetic\Debug\ChartCursor.obj
         .\..\..\temp\Hermetic\Debug\ChartDateTimeAxis.obj
         .\..\..\temp\Hermetic\Debug\ChartDragLineCursor.obj
         .\..\..\temp\Hermetic\Debug\ChartFont.obj
         .\..\..\temp\Hermetic\Debug\ChartGanttSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartGradient.obj
         .\..\..\temp\Hermetic\Debug\ChartGrid.obj
         .\..\..\temp\Hermetic\Debug\ChartLegend.obj
         .\..\..\temp\Hermetic\Debug\ChartLineSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartLogarithmicAxis.obj
         .\..\..\temp\Hermetic\Debug\ChartPointsSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartScrollBar.obj
         .\..\..\temp\Hermetic\Debug\ChartSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartStandardAxis.obj
         .\..\..\temp\Hermetic\Debug\ChartSurfaceSerie.obj
         .\..\..\temp\Hermetic\Debug\ChartTitle.obj
         .\..\..\temp\Hermetic\Debug\ChartXYSerie.obj
         .\..\..\temp\Hermetic\Debug\jsoncpp.obj
         .\..\..\temp\Hermetic\Debug\AppGeneral.obj
         .\..\..\temp\Hermetic\Debug\AutoCal.obj
         .\..\..\temp\Hermetic\Debug\AxisState.obj
         .\..\..\temp\Hermetic\Debug\BCMenu.obj
         .\..\..\temp\Hermetic\Debug\BtnST.obj
         .\..\..\temp\Hermetic\Debug\CeXDib.obj
         .\..\..\temp\Hermetic\Debug\CountDlg.obj
         .\..\..\temp\Hermetic\Debug\DlgPageResult.obj
         .\..\..\temp\Hermetic\Debug\DlgPar.obj
         .\..\..\temp\Hermetic\Debug\Dlg_MES_SMT.obj
         .\..\..\temp\Hermetic\Debug\F28Light.obj
         .\..\..\temp\Hermetic\Debug\FileOperator.obj
         .\..\..\temp\Hermetic\Debug\FlowCOM.obj
         .\..\..\temp\Hermetic\Debug\FlowLeakDetector.obj
         .\..\..\temp\Hermetic\Debug\FlowMotion.obj
         .\..\..\temp\Hermetic\Debug\GradientStatic.obj
         .\..\..\temp\Hermetic\Debug\HeaderCtrlCl.obj
         .\..\..\temp\Hermetic\Debug\hermeticFrame.obj
         .\..\..\temp\Hermetic\Debug\hermeticFrameDlg.obj
         .\..\..\temp\Hermetic\Debug\Language.obj
         .\..\..\temp\Hermetic\Debug\Language_Dlg_Main.obj
         .\..\..\temp\Hermetic\Debug\Language_Dlg_Para.obj
         .\..\..\temp\Hermetic\Debug\Language_Flow.obj
         .\..\..\temp\Hermetic\Debug\Lang_Motion.obj
         .\..\..\temp\Hermetic\Debug\ListCtrlCl.obj
         .\..\..\temp\Hermetic\Debug\LogRec.obj
         .\..\..\temp\Hermetic\Debug\markup.obj
         .\..\..\temp\Hermetic\Debug\MesLogin.obj
         .\..\..\temp\Hermetic\Debug\MixedLineConfirmDlg.obj
         .\..\..\temp\Hermetic\Debug\MixedLineManager.obj
         .\..\..\temp\Hermetic\Debug\NameBaseDef.obj
         .\..\..\temp\Hermetic\Debug\OPPO_MES.obj
         .\..\..\temp\Hermetic\Debug\ProFlow.obj
         .\..\..\temp\Hermetic\Debug\RegApp.obj
         .\..\..\temp\Hermetic\Debug\ReportCtrl.obj
         .\..\..\temp\Hermetic\Debug\RobotTCP.obj
         .\..\..\temp\Hermetic\Debug\SerialCom.obj
         .\..\..\temp\Hermetic\Debug\SerialPort.obj
         .\..\..\temp\Hermetic\Debug\ShadeButtonST.obj
         .\..\..\temp\Hermetic\Debug\stdafx.obj
         .\..\..\temp\Hermetic\Debug\SysCfg.obj
         .\..\..\temp\Hermetic\Debug\TCPSocket.obj
         .\..\..\temp\Hermetic\Debug\TotalATEQDlg.obj
         .\..\..\temp\Hermetic\Debug\VCF28LightControlDemoDlg.obj
         .\..\..\temp\Hermetic\Debug\_ListCtrl.obj
       Manifest:
         C:\Program Files (x86)\Microsoft SDKs\Windows\v7.0A\bin\mt.exe /nologo /verbose /out:".\..\..\temp\Hermetic\Debug\hermeticFrame.exe.embed.manifest" /manifest .\..\..\temp\Hermetic\Debug\hermeticFrame.exe.intermediate.manifest "C:\Program Files (x86)\Microsoft Visual Studio 10.0\VC\Include\Manifest\dpiaware.manifest"
         所有输出均为最新。
       LinkEmbedManifest:
         所有输出均为最新。
         hermeticFrame.vcxproj -> D:\ASUS\Desktop\科瑞2\hermetic\hermetic\hermeticFrame\.\..\exe\hermeticFrame.exe
       FinalizeBuildStatus:
         正在删除文件“.\..\..\temp\Hermetic\Debug\hermeticFrame.unsuccessfulbuild”。
         正在对“.\..\..\temp\Hermetic\Debug\hermeticFrame.lastbuildstate”执行 Touch 任务。
     1>已完成生成项目“D:\ASUS\Desktop\科瑞2\hermetic\hermetic\hermeticFrame\hermeticFrame.vcxproj”(build 个目标)的操作。

生成成功。

已用时间 00:00:01.66
