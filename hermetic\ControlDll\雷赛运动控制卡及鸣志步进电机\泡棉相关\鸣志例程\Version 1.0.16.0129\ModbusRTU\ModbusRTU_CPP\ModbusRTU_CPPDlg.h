
// ModbusRTU_CPPDlg.h : header file
//

#pragma once

#include "PngStatic.h"
#include "PosMode.h"
#include "QProgram.h"
#include "Jog.h"

// CModbusRTU_CPPDlg dialog
class CModbusRTU_CPPDlg : public CDialogEx
{
// Construction
public:
	CModbusRTU_CPPDlg(CWnd* pParent = NULL);	// standard constructor

// Dialog Data
	enum { IDD = IDD_MODBUSRTU_CPP_DIALOG };

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);	// DDX/DDV support


// Implementation
protected:
	HICON m_hIcon;
	int m_Port;
	int m_BaudRate;
	BYTE m_NodeID;

	//static void CALLBACK OnDataSend();

	// Generated message map functions
	virtual BOOL OnInitDialog();
	void SetActionEnable();
	afx_msg void GetComm();
	afx_msg void AddBandRate();
	afx_msg void OnSysCommand(UINT nID, LPARAM lParam);
	afx_msg void OnPaint();
	afx_msg HCURSOR OnQueryDragIcon();
	
	DECLARE_MESSAGE_MAP()

	afx_msg LRESULT OnButtonClick(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnButtonMouseDown(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnButtonMouseUp(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnDataSend(WPARAM wParam, LPARAM lParam);
	afx_msg LRESULT OnDataReceived(WPARAM wParam, LPARAM lParam);
public:
	CTabCtrl m_tab_Motion;
	CPngStatic m_Logo;
	CComboBox cmb_BaudRate;
	CComboBox cmb_ComPort;
	CComboBox cmb_NodeID;
protected:
	CPosMode m_PosMode;
	CQProgram m_QProgram;
	CJog m_Jog;
public:
	afx_msg void OnSelchangeTabMotion(NMHDR *pNMHDR, LRESULT *pResult);	
	afx_msg void OnBnClickedBtnOpen();	
	afx_msg void OnBnClickedBtnClose();
	afx_msg void OnBnClickedBtnDisable();
	afx_msg void OnBnClickedBtnEnable();
	CString m_txt_CmdHis;
	afx_msg void OnBnClickedButton5();
	BOOL m_bShowSend;
	BOOL m_bShowReceived;
	BOOL m_bMonitor;
	afx_msg void OnTimer(UINT_PTR nIDEvent);
	virtual BOOL DestroyWindow();
	CString m_sDriveStatus;
	CString m_sAlarmCode;
	CString m_sActualSpeed;
	CString m_sTargetSpeed;
	CString m_sEncoderPos;
	CString m_sCommandPos;
	int m_nInterval;
	afx_msg void OnClickedChkMonitor();
	CEdit m_txt_Interval;
	afx_msg void OnBnClickedBtnAlarmreset();
	afx_msg void OnBnClickedBtnStop();
	virtual void OnOK();
	virtual void OnCancel();
	afx_msg void OnBnClickedMfcbtnStop();
	int m_nSIngleRegNo;
	int m_nMultiRegNo;
	int m_nMultiRegCount;
	CString m_sMultiRegValue;
	afx_msg void OnBnClickedBtnReadsinglereg();
	afx_msg void OnBnClickedBtnWritesinglereg();
	afx_msg void OnBnClickedBtnReadmultireg();
	afx_msg void OnBnClickedBtnWritemultireg();
	CString m_sSingleRegValue;
	afx_msg void OnBnClickedChkShowsend();
};
