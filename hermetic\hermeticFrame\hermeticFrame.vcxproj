﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A2C3E72F-8FDD-44B3-8A16-************}</ProjectGuid>
    <RootNamespace>hermeticFrame</RootNamespace>
    <Keyword>MFCProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>.\..\exe\</OutDir>
    <IntDir>.\..\..\temp\Hermetic\Debug\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level3</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0804</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\14.ico" />
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\17.ico" />
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\hermeticFrame.ico" />
    <None Include="..\..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\Run.ico" />
    <None Include="..\..\..\..\..\..\stdioProject\hermeticFrame\hermeticFrame\res\15.ico" />
    <None Include="..\ChartCtrl\ChartBalloonLabel.inl" />
    <None Include="..\ChartCtrl\ChartLabel.inl" />
    <None Include="..\ChartCtrl\ChartPointsArray.inl" />
    <None Include="..\ChartCtrl\ChartSerieBase.inl" />
    <None Include="ReadMe.txt" />
    <None Include="res\1.ico" />
    <None Include="res\11.bmp" />
    <None Include="res\11.ico" />
    <None Include="res\12.ico" />
    <None Include="res\13.ico" />
    <None Include="res\14.bmp" />
    <None Include="res\14.ico" />
    <None Include="res\15.bmp" />
    <None Include="res\15.ico" />
    <None Include="res\16.bmp" />
    <None Include="res\16.ico" />
    <None Include="res\17.bmp" />
    <None Include="res\17.ico" />
    <None Include="res\18.ico" />
    <None Include="res\19.ico" />
    <None Include="res\2.bmp" />
    <None Include="res\2.ico" />
    <None Include="res\20.ico" />
    <None Include="res\21.ico" />
    <None Include="res\22.ico" />
    <None Include="res\3.bmp" />
    <None Include="res\3.ico" />
    <None Include="res\4.bmp" />
    <None Include="res\4.ico" />
    <None Include="res\5.bmp" />
    <None Include="res\5.ico" />
    <None Include="res\6.bmp" />
    <None Include="res\6.ico" />
    <None Include="res\7.bmp" />
    <None Include="res\7.ico" />
    <None Include="res\8.bmp" />
    <None Include="res\8.ico" />
    <None Include="res\9.bmp" />
    <None Include="res\9.ico" />
    <None Include="res\bitmap1.bmp" />
    <None Include="res\bitmap1.ico" />
    <None Include="res\bitmap3.bmp" />
    <None Include="res\bitmap3.ico" />
    <None Include="res\Config.bmp" />
    <None Include="res\Config.ico" />
    <None Include="res\Control.ico" />
    <None Include="res\Doc.bmp" />
    <None Include="res\Doc.ico" />
    <None Include="res\Exit.bmp" />
    <None Include="res\Exit.ico" />
    <None Include="res\GUIDoc.ico" />
    <None Include="res\hermetic.ico" />
    <None Include="res\hermeticFrame.ico" />
    <None Include="res\hermeticFrame.rc2" />
    <None Include="res\ico00001.ico" />
    <None Include="res\icon1.ico" />
    <None Include="res\icon2.ico" />
    <None Include="res\LedOff.ico" />
    <None Include="res\LedOn.ico" />
    <None Include="res\Login.bmp" />
    <None Include="res\Login.ico" />
    <None Include="res\Manger.bmp" />
    <None Include="res\Manger.ico" />
    <None Include="res\MotorOff.ico" />
    <None Include="res\MotorOn.ico" />
    <None Include="res\notrecode.ico" />
    <None Include="res\recode.ico" />
    <None Include="res\reset.bmp" />
    <None Include="res\reset.ico" />
    <None Include="res\Run.ico" />
    <None Include="res\Set.bmp" />
    <None Include="res\Set.ico" />
    <None Include="res\Start.bmp" />
    <None Include="res\Start.ico" />
    <None Include="res\Stop (2).ico" />
    <None Include="res\Stop.bmp" />
    <None Include="res\Stop.ico" />
    <None Include="res\Toolbar.bmp" />
    <None Include="res\Toolbar.ico" />
    <None Include="res\toolbar1.bmp" />
    <None Include="res\Toolbar256.bmp" />
    <None Include="res\Toolbar256.ico" />
    <None Include="res\toolbar256disable.bmp" />
    <None Include="res\toolbar256disable.ico" />
    <None Include="res\toolbar256Hot.bmp" />
    <None Include="res\toolbar256Hot.ico" />
    <None Include="res\UnLogin.bmp" />
    <None Include="res\UnLogin.ico" />
    <None Include="res\unuse.bmp" />
    <None Include="res\unuse.ico" />
    <None Include="res\消息.bmp" />
    <None Include="res\消息.ico" />
    <None Include="res\消息16.bmp" />
    <None Include="res\消息16.ico" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\ChartCtrl\ChartAxis.h" />
    <ClInclude Include="..\ChartCtrl\ChartAxisLabel.h" />
    <ClInclude Include="..\ChartCtrl\ChartBalloonLabel.h" />
    <ClInclude Include="..\ChartCtrl\ChartBarSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartCandlestickSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartCrossHairCursor.h" />
    <ClInclude Include="..\ChartCtrl\ChartCtrl.h" />
    <ClInclude Include="..\ChartCtrl\ChartCursor.h" />
    <ClInclude Include="..\ChartCtrl\ChartDateTimeAxis.h" />
    <ClInclude Include="..\ChartCtrl\ChartDragLineCursor.h" />
    <ClInclude Include="..\ChartCtrl\ChartFont.h" />
    <ClInclude Include="..\ChartCtrl\ChartGanttSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartGradient.h" />
    <ClInclude Include="..\ChartCtrl\ChartGrid.h" />
    <ClInclude Include="..\ChartCtrl\ChartLabel.h" />
    <ClInclude Include="..\ChartCtrl\ChartLegend.h" />
    <ClInclude Include="..\ChartCtrl\ChartLineSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartLogarithmicAxis.h" />
    <ClInclude Include="..\ChartCtrl\ChartMouseListener.h" />
    <ClInclude Include="..\ChartCtrl\ChartPointsArray.h" />
    <ClInclude Include="..\ChartCtrl\ChartPointsSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartScrollBar.h" />
    <ClInclude Include="..\ChartCtrl\ChartSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartSerieBase.h" />
    <ClInclude Include="..\ChartCtrl\ChartSeriesMouseListener.h" />
    <ClInclude Include="..\ChartCtrl\ChartStandardAxis.h" />
    <ClInclude Include="..\ChartCtrl\ChartString.h" />
    <ClInclude Include="..\ChartCtrl\ChartSurfaceSerie.h" />
    <ClInclude Include="..\ChartCtrl\ChartTitle.h" />
    <ClInclude Include="..\ChartCtrl\ChartXYSerie.h" />
    <ClInclude Include="..\ChartCtrl\PointsOrdering.h" />
    <ClInclude Include="..\F28LightControl_Eth\Include\F28LightControl_ETH.h" />
    <ClInclude Include="AppGeneral.h" />
    <ClInclude Include="AutoCal.h" />
    <ClInclude Include="AxisState.h" />
    <ClInclude Include="BCMenu.h" />
    <ClInclude Include="BtnST.h" />
    <ClInclude Include="CeXDib.h" />
    <ClInclude Include="comm.h" />
    <ClInclude Include="CountDlg.h" />
    <ClInclude Include="DlgPageResult.h" />
    <ClInclude Include="DlgPar.h" />
    <ClInclude Include="Dlg_MES_SMT.h" />
    <ClInclude Include="F28Light.h" />
    <ClInclude Include="FileOperator.h" />
    <ClInclude Include="FlowCOM.h" />
    <ClInclude Include="FlowLeakDetector.h" />
    <ClInclude Include="FlowMotion.h" />
    <ClInclude Include="FlowUniversalDefine.h" />
    <ClInclude Include="GradientStatic.h" />
    <ClInclude Include="HeaderCtrlCl.h" />
    <ClInclude Include="hermeticFrame.h" />
    <ClInclude Include="hermeticFrameDlg.h" />
    <ClInclude Include="Language.h" />
    <ClInclude Include="Language_Dlg_Main.h" />
    <ClInclude Include="Language_Dlg_Para.h" />
    <ClInclude Include="Language_Flow.h" />
    <ClInclude Include="Language_Universal.h" />
    <ClInclude Include="Lang_Motion.h" />
    <ClInclude Include="ListCtrlCl.h" />
    <ClInclude Include="LogRec.h" />
    <ClInclude Include="LTDMC.h" />
    <ClInclude Include="MacroMotion.h" />
    <ClInclude Include="markup.h" />
    <ClInclude Include="MesLogin.h" />
    <ClInclude Include="MixedLineConfirmDlg.h" />
    <ClInclude Include="MixedLineManager.h" />
    <ClInclude Include="NameBaseDef.h" />
    <ClInclude Include="OPPO_MES.h" />
    <ClInclude Include="ProFlow.h" />
    <ClInclude Include="RegApp.h" />
    <ClInclude Include="ReportCtrl.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="RobotTCP.h" />
    <ClInclude Include="SENTENCE.h" />
    <ClInclude Include="SerialCom.h" />
    <ClInclude Include="SerialPort.h" />
    <ClInclude Include="ShadeButtonST.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="SysCfg.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="TCPSocket.h" />
    <ClInclude Include="TotalATEQDlg.h" />
    <ClInclude Include="VCF28LightControlDemoDlg.h" />
    <ClInclude Include="_ListCtrl.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\ChartCtrl\ChartAxis.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartAxisLabel.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartBarSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartCandlestickSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartCrossHairCursor.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartCtrl.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartCursor.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartDateTimeAxis.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartDragLineCursor.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartFont.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartGanttSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartGradient.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartGrid.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartLegend.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartLineSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartLogarithmicAxis.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartPointsSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartScrollBar.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartStandardAxis.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartSurfaceSerie.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartTitle.cpp" />
    <ClCompile Include="..\ChartCtrl\ChartXYSerie.cpp" />
    <ClCompile Include="..\CommonClass\json\jsoncpp.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">NotUsing</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="AppGeneral.cpp" />
    <ClCompile Include="AutoCal.cpp" />
    <ClCompile Include="AxisState.cpp" />
    <ClCompile Include="BCMenu.cpp" />
    <ClCompile Include="BtnST.cpp" />
    <ClCompile Include="CeXDib.cpp" />
    <ClCompile Include="CountDlg.cpp" />
    <ClCompile Include="DlgPageResult.cpp" />
    <ClCompile Include="DlgPar.cpp" />
    <ClCompile Include="Dlg_MES_SMT.cpp" />
    <ClCompile Include="F28Light.cpp" />
    <ClCompile Include="FileOperator.cpp" />
    <ClCompile Include="FlowCOM.cpp" />
    <ClCompile Include="FlowLeakDetector.cpp" />
    <ClCompile Include="FlowMotion.cpp" />
    <ClCompile Include="GradientStatic.cpp" />
    <ClCompile Include="HeaderCtrlCl.cpp" />
    <ClCompile Include="hermeticFrame.cpp" />
    <ClCompile Include="hermeticFrameDlg.cpp" />
    <ClCompile Include="Language.cpp" />
    <ClCompile Include="Language_Dlg_Main.cpp" />
    <ClCompile Include="Language_Dlg_Para.cpp" />
    <ClCompile Include="Language_Flow.cpp" />
    <ClCompile Include="Lang_Motion.cpp" />
    <ClCompile Include="ListCtrlCl.cpp" />
    <ClCompile Include="LogRec.cpp" />
    <ClCompile Include="markup.cpp" />
    <ClCompile Include="MesLogin.cpp" />
    <ClCompile Include="MixedLineConfirmDlg.cpp" />
    <ClCompile Include="MixedLineManager.cpp" />
    <ClCompile Include="NameBaseDef.cpp" />
    <ClCompile Include="OPPO_MES.cpp" />
    <ClCompile Include="ProFlow.cpp" />
    <ClCompile Include="RegApp.cpp" />
    <ClCompile Include="ReportCtrl.cpp" />
    <ClCompile Include="RobotTCP.cpp" />
    <ClCompile Include="SerialCom.cpp" />
    <ClCompile Include="SerialPort.cpp" />
    <ClCompile Include="ShadeButtonST.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="SysCfg.cpp" />
    <ClCompile Include="TCPSocket.cpp" />
    <ClCompile Include="TotalATEQDlg.cpp" />
    <ClCompile Include="VCF28LightControlDemoDlg.cpp" />
    <ClCompile Include="_ListCtrl.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="hermeticFrame.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties RESOURCE_FILE="hermeticFrame.rc" />
    </VisualStudio>
  </ProjectExtensions>
</Project>