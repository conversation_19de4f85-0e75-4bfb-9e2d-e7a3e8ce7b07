// CompactDatabase.h: interface for the CCompactDatabase class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_COMPACTDATABASE_H__EE18FF0B_B470_48A8_9209_75DCF50DE3A7__INCLUDED_)
#define AFX_COMPACTDATABASE_H__EE18FF0B_B470_48A8_9209_75DCF50DE3A7__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000


class OLEINITIALIZE
{    
public:   
	BOOL m_bOleInit;
	OLEINITIALIZE();  
	~OLEINITIALIZE();
};


#if _USRDLL
#define _COMPACTDATABASE_DLL __declspec(dllexport) 
#else
#define _COMPACTDATABASE_DLL __declspec(dllimport) 
#endif

class _COMPACTDATABASE_DLL CCompactDatabase  
 {
	
public:  
	CCompactDatabase();
	virtual ~CCompactDatabase();
	HRESULT Compact(LPCTSTR src, LPCTSTR pwd);
};

#endif // !defined(AFX_COMPACTDATABASE_H__EE18FF0B_B470_48A8_9209_75DCF50DE3A7__INCLUDED_)
