// ControlDll.h : main header file for the CONTROLDLL DLL
//

#if !defined(AFX_CONTROLDLL_H__035F1AEE_6EE1_47EC_AE76_B53EC46575FD__INCLUDED_)
#define AFX_CONTROLDLL_H__035F1AEE_6EE1_47EC_AE76_B53EC46575FD__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#ifndef __AFXWIN_H__
	#error include 'stdafx.h' before including this file for PCH
#endif

#include "resource.h"		// main symbols

/////////////////////////////////////////////////////////////////////////////
// CControlDllApp
// See ControlDll.cpp for the implementation of this class
//

class CControlDllApp : public CWinApp
{
public:
	CControlDllApp();

// Overrides
	// ClassWizard generated virtual function overrides
	//{{AFX_VIRTUAL(CControlDllApp)
	//}}AFX_VIRTUAL

	//{{AFX_MSG(CControlDllApp)
		// NOTE - the ClassWizard will add and remove member functions here.
		//    DO NOT EDIT what you see in these blocks of generated code !
	//}}AFX_MSG
	DECLARE_MESSAGE_MAP()
};


/////////////////////////////////////////////////////////////////////////////

//{{AFX_INSERT_LOCATION}}
// Microsoft Visual C++ will insert additional declarations immediately before the previous line.

#endif // !defined(AFX_CONTROLDLL_H__035F1AEE_6EE1_47EC_AE76_B53EC46575FD__INCLUDED_)
