#pragma once

#include "FlowUniversalDefine.h"
#include "SerialCom.h"

enum e_COM_Type
{
	eCOM_Scan=0,
	eCOM_Valve,
	eCOM_Pressure,
	eCOM_Type_Count,
};
struct tag_COM
{
	SerialCom	Type[eStation_Count][eCOM_Type_Count];
	int			nPort[eStation_Count][eCOM_Type_Count];
	int			nBaud[eCOM_Type_Count];
	int			nData[eCOM_Type_Count];
	int			nStop[eCOM_Type_Count];
	char		cParity[eCOM_Type_Count];
	CString		strName_Station[eStation_Count];
	CString		strName_Tail[eCOM_Type_Count];
	CString		strName_COM[eStation_Count][eCOM_Type_Count];

	tag_COM()
	{
		nBaud[eCOM_Scan]							= 115200;
		nData[eCOM_Scan]							= 8;
		nStop[eCOM_Scan]							= 1;
		cParity[eCOM_Scan]							= 'N';
		nBaud[eCOM_Valve]							= 9600;
		nData[eCOM_Valve]							= 8;
		nStop[eCOM_Valve]							= 1;
		cParity[eCOM_Valve]							= 'N';
		nBaud[eCOM_Pressure]						= 9600;
		nData[eCOM_Pressure]						= 8;
		nStop[eCOM_Pressure]						= 1;
		cParity[eCOM_Pressure]						= 'N';

		strName_Station[eStation_Up_Left]			= "Up-Left-";
		strName_Station[eStation_Up_Right]			= "Up-Right-";
		strName_Station[eStation_Down_Left]			= "Down-Left-";
		strName_Station[eStation_Down_Right]		= "Down-Right-";
		strName_Tail[eCOM_Scan]						= "Scan";
		strName_Tail[eCOM_Valve]					= "Valve";
		strName_Tail[eCOM_Pressure]					= "Pressure";
		for(int i=0; i<eStation_Count; i++)
		{
			for(int j=0; j<eCOM_Type_Count; j++)
			{
				strName_COM[i][j] = strName_Station[i] + strName_Tail[j];
			}
		}
	}
};

class CFlowCOM
{
public:
	CFlowCOM(void);
	~CFlowCOM(void);
};

