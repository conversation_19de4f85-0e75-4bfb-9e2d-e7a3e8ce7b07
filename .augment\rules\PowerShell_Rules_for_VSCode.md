# PowerShell 通用规则 - VSCode环境

## 核心规则：中文安全模式

### 🎯 唯一必记规则
```powershell
# 标准操作模板 - 适用于所有文件操作
$file = "目标文件路径"
$content = Get-Content $file -Encoding Default
# [在这里进行修改操作]
$content | Set-Content $file -Encoding Default
Write-Host "✅ 操作完成"
```

## 使用方法

### 触发方式
在与AI对话时，使用以下任一表达：
- "使用PowerShell规则"
- "用中文安全模式"
- "按照PowerShell规则处理"

### AI将自动应用
- ✅ 永远使用 `-Encoding Default`
- ✅ 先读取到变量，再统一写回
- ✅ 提供操作反馈和验证

## 常用扩展模式

### 🔍 搜索模式
```powershell
Get-Content "文件.cpp" -Encoding Default | Select-String "搜索内容" -Context 2
```

### 🔄 替换模式
```powershell
$content = Get-Content "文件.cpp" -Encoding Default
$content = $content -replace "旧内容", "新内容"
$content | Set-Content "文件.cpp" -Encoding Default
```

### 📊 验证模式
```powershell
Get-Content "文件.cpp" -Encoding Default | Select-String "验证内容" | Measure-Object
```

### 🛡️ 安全备份模式
```powershell
Copy-Item "源文件.cpp" "源文件.cpp.backup" -Force
$content = Get-Content "源文件.cpp" -Encoding Default
# [修改操作]
$content | Set-Content "源文件.cpp" -Encoding Default
```

## 实际应用示例

### 示例1：简单替换
**用户说：** "把cpp文件中的old_function替换成new_function，用PowerShell规则"

**AI执行：**
```powershell
$content = Get-Content "file.cpp" -Encoding Default
$content = $content -replace "old_function", "new_function"
$content | Set-Content "file.cpp" -Encoding Default
Write-Host "✅ 替换完成"
```

### 示例2：批量处理
**用户说：** "移除所有DDX绑定，用PowerShell规则"

**AI执行：**
```powershell
$content = Get-Content "Dialog.cpp" -Encoding Default
$content = $content | Where-Object { $_ -notmatch "DDX_Control.*_view" }
$content | Set-Content "Dialog.cpp" -Encoding Default
Write-Host "✅ DDX绑定移除完成"
```

## 核心优势

- 🔤 **解决中文编码问题** - 永不乱码
- 🛡️ **安全可靠** - 先读后写，避免文件损坏
- ⚡ **简单高效** - 一个规则解决99%问题
- 📊 **可验证** - 每次操作都有反馈

## 记忆要点

**只需记住一句话：**
> "提到PowerShell规则，AI就用中文安全模式处理文件"

---
*创建日期：2025-01-19*
*适用环境：VSCode + PowerShell + 中文项目*
