﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{ef4ae3c0-308c-493c-8014-dc2b419aba59}</UniqueIdentifier>
      <Extensions>cpp;c;cxx;rc;def;r;odl;idl;hpj;bat</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{06be547e-deb8-40d4-9f6b-6d956f81e7a9}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{c1d320ca-6770-4b5f-bcb2-bebb581be7e4}</UniqueIdentifier>
      <Extensions>ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe</Extensions>
    </Filter>
    <Filter Include="Lib">
      <UniqueIdentifier>{7a77caf5-055b-445a-ba15-6400a17850c3}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="_a_rect30.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_Dlg2ImageArith.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgBlobSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgDebug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgImage.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgInputBox.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgSetting.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_DlgThresholdSet.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="_ImageView.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SdkExt.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="SdkExtDll.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="StdAfx.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgControl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\ListCtrl\_ListCtrl.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgConditionOutput.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgConditionMotor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\ButtonST\BCMenu.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\ButtonST\BtnST.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\ButtonST\CeXDib.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\CommonClass\ButtonST\ShadeButtonST.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="DlgHomeType.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="Lang_Dlg_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="RegApp.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="SdkExtDll.rc">
      <Filter>Source Files</Filter>
    </ResourceCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="_a_rect30.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_Dlg2ImageArith.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgBlobSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgDebug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgImage.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgInputBox.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgSetting.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_DlgThresholdSet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="_ImageView.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Resource.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="StdAfx.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\SdkExtDll_include\SdkExt.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgControl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonClass\ListCtrl\_ListCtrl.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgConditionOutput.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgConditionMotor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonClass\ButtonST\BCMenu.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonClass\ButtonST\BtnST.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonClass\ButtonST\CeXDib.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="..\CommonClass\ButtonST\ShadeButtonST.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="DlgHomeType.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lang_Dlg_Debug.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="Lang_Def.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="RegApp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="SdkExtDll.def">
      <Filter>Source Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\folder open.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\folder.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\Hand.cur">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\icon_mcl.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\icon_mop.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\LedOff.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\LedOn.ico">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\SdkExtDll.rc2">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarEdit.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarEditDisabled.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarEditHot.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarSys.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarSysDisabled.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarSysHot.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarUser.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarUserDisabled.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="res\ToolbarUserHot.bmp">
      <Filter>Resource Files</Filter>
    </CustomBuild>
    <CustomBuild Include="ReadMe.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="res\Control.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\MotorOn.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\MotorOff.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\icon_led.ico">
      <Filter>Resource Files</Filter>
    </None>
    <None Include="res\ico00001.ico">
      <Filter>Resource Files</Filter>
    </None>
  </ItemGroup>
</Project>