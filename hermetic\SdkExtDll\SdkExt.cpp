// SdkExt.cpp: implementation of the CSdkExt class.
//
//////////////////////////////////////////////////////////////////////

#include "stdafx.h"
#include "..\SdkExtDll_include\SdkExt.h"
#include "_DlgDebug.h"
#include "_DlgInputBox.h"
#include "DlgControl.h"
//#include "DlgConditionOutput.h"

#ifdef _DEBUG
#undef THIS_FILE
static char THIS_FILE[]=__FILE__;
#define new DEBUG_NEW
#endif

//////////////////////////////////////////////////////////////////////
// Construction/Destruction
//////////////////////////////////////////////////////////////////////

#ifdef SDK_WITH_VISION
#include "D:\\SdkRootPathConst.h"
#pragma comment(lib, SDK_LIB_ROOT_PATH "Tool\\MIL\\LIB\\mil.lib")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILBLOB.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILGEN.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILCOR.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILHOST.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\milim.lib")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILMEAS.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILMET2.LIB")
//#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILMET2D.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILMET.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILOCR.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILPAT.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILPUL.LIB")
#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILTIFF.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILVCAP.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILVGA.LIB")

#pragma comment(lib, SDK_LIB_ROOT_PATH "TOOL\\MIL\\LIB\\MILVHOOK.LIB")
#pragma comment(lib, "..\\exe\\MilEx.lib")
#pragma comment(lib, "..\\exe\\VisionDll.lib")
#endif

#ifdef SDK_WITH_HDREGESTER
	#ifdef _DEBUG
	#pragma comment(lib, "..\\Exe\\HDRegesterd.lib")
	#else
	#pragma comment(lib, "..\\Exe\\HDRegester.lib")
	#endif
#endif
#pragma comment(lib, "..\\Exe\\UserManager.lib")

#pragma comment(lib, "..\\exe\\ControlDll.lib")
//#pragma comment(lib, "..\\exe\\InfomationDll.lib")
//#pragma comment(lib, "..\\exe\\Database.lib")
//#pragma comment(lib, "..\\exe\\CommomLib.lib")

C_Information *info;
C_InfoReadWrite *infoRW;
C_ControlMultiThread *ctrl;

#ifdef SDK_WITH_VISION
	C_MILApp *mil;
	C_Video *vdo;
#endif

C_DlgDebug *pDlgDebug;//2012.10.21
CDlgControl *m_pDlgControl;
CUserManager *m_User;
HWND g_hWndParent;

CSdkExt::CSdkExt()
{
	pDlgDebug = NULL;
	m_pDlgControl = NULL;
	info = NULL;
	infoRW = NULL;
	ctrl = NULL;
#ifdef SDK_WITH_VISION
	mil = NULL;
	vdo = NULL;
#endif
	m_User = NULL;
}

CSdkExt::~CSdkExt()
{
	Destroy();
}

void CSdkExt::initSDK(HWND hWndParent, C_Information *pInfo, C_InfoReadWrite *pInfoRW, 
					  C_ControlMultiThread *pCtrl,
#ifdef SDK_WITH_VISION
					  C_Video *pVdo, C_MILApp *pMil, 
#endif
					  CUserManager *pUser)
{
	g_hWndParent = hWndParent;
	
	info = pInfo;
	infoRW = pInfoRW;
	ctrl = pCtrl;
#ifdef SDK_WITH_VISION
	mil = pMil;
	vdo = pVdo;
#endif
	m_User = pUser;	
}

void CSdkExt::Destroy()
{
	if(pDlgDebug) delete pDlgDebug;
	pDlgDebug = NULL;

	if(m_pDlgControl != NULL)
	{
		delete m_pDlgControl;
		m_pDlgControl = NULL;
	}
}


void CSdkExt::DisplayDebug(BOOL bShow)
{
	if(info == NULL) return;
		
	// 	if(pp)
	// 	{
	// 		pp->ShowWindow(SW_SHOW);
	// 		pp->SetActiveWindow();
	// 	}
	// 	else
	// 	{
	// 		pp = new C_DlgInputBox;
	// 		pp->Create(IDD_DLG_INPUTBOX, NULL);
	// 		pp->CenterWindow();
	// 		pp->ShowWindow(SW_SHOW);
	// 	}
	
	
	if(pDlgDebug)
	{
		delete pDlgDebug;
		pDlgDebug = NULL;
	}
	if(pDlgDebug)
	{
		pDlgDebug->ShowWindow(SW_SHOW);
		pDlgDebug->SetActiveWindow();
	}
	else
	{
		CWnd *pWndParent = CWnd::FromHandle(g_hWndParent);
		pDlgDebug = new C_DlgDebug(pWndParent);
		pDlgDebug->Create(IDD_DLG_DEBUG, NULL);
		pDlgDebug->CenterWindow();
		pDlgDebug->ShowWindow(SW_SHOW);
	}
}

void CSdkExt::DisplayDebugDlg()
{

	//CDlgConditionOutput dlg(info, "");
	//dlg.DoModal();
	//return;

	CWnd *pWndParent = CWnd::FromHandle(g_hWndParent);
	if(m_pDlgControl == NULL)
	{

		m_pDlgControl = new CDlgControl(info, infoRW, ctrl, pWndParent);
		m_pDlgControl->Create(IDD_DLG_CONTROL, NULL);
		m_pDlgControl->CenterWindow();
	}

	m_pDlgControl->ShowWindow(SW_SHOW);
}