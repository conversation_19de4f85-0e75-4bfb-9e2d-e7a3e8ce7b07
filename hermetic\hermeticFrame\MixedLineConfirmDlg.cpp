#include "stdafx.h"
#include "hermeticFrame.h"
#include "MixedLineConfirmDlg.h"

// CMixedLineConfirmDlg 对话框

IMPLEMENT_DYNAMIC(CMixedLineConfirmDlg, CDialog)

CMixedLineConfirmDlg::CMixedLineConfirmDlg(CWnd* pParent /*=NULL*/)
	: CDialog(CMixedLineConfirmDlg::IDD, pParent)
{
}

CMixedLineConfirmDlg::~CMixedLineConfirmDlg()
{
}

void CMixedLineConfirmDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialog::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_STATIC_CURRENT_MODEL, m_staticCurrentModel);
	DDX_Control(pDX, IDC_STATIC_TARGET_MODEL, m_staticTargetModel);
	DDX_Control(pDX, IDC_STATIC_SERIES_MODEL, m_staticSeriesModel);
	DDX_Control(pDX, IDC_STATIC_PCBA_CODE, m_staticPcbaCode);
}

BEGIN_MESSAGE_MAP(CMixedLineConfirmDlg, CDialog)
	ON_BN_CLICKED(IDC_BTN_CONFIRM_SWITCH, &CMixedLineConfirmDlg::OnBnClickedBtnConfirmSwitch)
	ON_BN_CLICKED(IDC_BTN_CANCEL_SWITCH, &CMixedLineConfirmDlg::OnBnClickedBtnCancelSwitch)
END_MESSAGE_MAP()

// CMixedLineConfirmDlg 消息处理程序

BOOL CMixedLineConfirmDlg::OnInitDialog()
{
	CDialog::OnInitDialog();

	// 设置对话框标题
	SetWindowText(_T("混线切换确认"));

	// 设置显示信息
	CString strInfo;
	
	strInfo.Format(_T("当前机型: %s"), m_strCurrentModel);
	m_staticCurrentModel.SetWindowText(strInfo);
	
	strInfo.Format(_T("目标机型: %s"), m_strTargetModel);
	m_staticTargetModel.SetWindowText(strInfo);
	
	strInfo.Format(_T("系列机型: %s"), m_strSeriesModel);
	m_staticSeriesModel.SetWindowText(strInfo);
	
	strInfo.Format(_T("PCBA号: %s"), m_strPcbaCode);
	m_staticPcbaCode.SetWindowText(strInfo);

	// 设置按钮文字
	GetDlgItem(IDC_BTN_CONFIRM_SWITCH)->SetWindowText(_T("确认切换"));
	GetDlgItem(IDC_BTN_CANCEL_SWITCH)->SetWindowText(_T("取消"));

	// 居中显示对话框
	CenterWindow();

	return TRUE;
}

void CMixedLineConfirmDlg::SetMixedLineInfo(const CString& strCurrentModel, 
											const CString& strTargetModel,
											const CString& strSeriesModel,
											const CString& strPcbaCode)
{
	m_strCurrentModel = strCurrentModel;
	m_strTargetModel = strTargetModel;
	m_strSeriesModel = strSeriesModel;
	m_strPcbaCode = strPcbaCode;
}

void CMixedLineConfirmDlg::SetSystemStatus(bool bAllStationsIdle, const CString& strStatusMsg)
{
	m_bAllStationsIdle = bAllStationsIdle;
	m_strStatusMessage = strStatusMsg;
	
	// 如果对话框已经创建，更新显示
	if (GetSafeHwnd())
	{
		UpdateStatusDisplay();
	}
}

void CMixedLineConfirmDlg::UpdateWaitingStatus(const CString& strWaitMsg)
{
	m_strWaitMessage = strWaitMsg;
	
	// 如果对话框已经创建，更新显示
	if (GetSafeHwnd())
	{
		UpdateStatusDisplay();
	}
}

void CMixedLineConfirmDlg::UpdateStatusDisplay()
{
	// 更新状态显示（如果有状态控件的话）
	// 这里可以根据实际需要添加状态显示逻辑
	CString strTitle = "混线切换确认";
	if (!m_bAllStationsIdle)
	{
		strTitle += " - 等待系统空闲";
	}
	SetWindowText(strTitle);
}

void CMixedLineConfirmDlg::OnBnClickedBtnConfirmSwitch()
{
	// 用户确认切换
	EndDialog(IDOK);
}

void CMixedLineConfirmDlg::OnBnClickedBtnCancelSwitch()
{
	// 用户取消切换
	EndDialog(IDCANCEL);
} 