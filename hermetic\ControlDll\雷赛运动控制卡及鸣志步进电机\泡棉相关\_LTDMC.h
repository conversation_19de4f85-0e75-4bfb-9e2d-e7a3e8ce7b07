#pragma once

#include "ModbusRTUHelper.h"

class CStepMotor
{
public:
	CStepMotor(void);
	~CStepMotor(void);
public:
	ModbusRTUHelper *pStep;
	int nPosition[8];
public:
	bool InitMotor(int nPort, int nBaudRate = 9600);
	bool Home(int addr, double dRes, int nElapse, bool bIsRotating, double dAcc = 100, double dDec = 100, double dVel = 10);
	bool Move(int addr, double relDis, bool bIsRotating = false, double dAcc = 100, double dDec = 100, double dVel = 10);
	bool MoveTo(int addr, double absDis, bool bIsRotating = false, double dAcc = 100, double dDec = 100, double dVel = 10);
};

