
// ModbusRTU_CPPDlg.cpp : implementation file
//

#include "stdafx.h"
#include "ModbusRTU_CPP.h"
#include "ModbusRTU_CPPDlg.h"
#include "afxdialogex.h"
#include "ModbusRTUHelper.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif
				
ModbusRTUHelper m_ModbusRTUHelper;

// CAboutDlg dialog used for App About

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// Dialog Data
	enum { IDD = IDD_ABOUTBOX };

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support


// Implementation
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(CAboutDlg::IDD)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CModbusRTU_CPPDlg dialog




CModbusRTU_CPPDlg::CModbusRTU_CPPDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(CModbusRTU_CPPDlg::IDD, pParent)
{
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
	m_txt_CmdHis = _T("");
	this->m_bShowSend = true;
	this->m_bShowReceived = true;
	this->m_bMonitor = false;
	m_sDriveStatus = _T("");
	m_sAlarmCode = _T("");
	m_sActualSpeed = _T("");
	m_sTargetSpeed = _T("");
	m_sEncoderPos = _T("");
	m_sCommandPos = _T("");
	m_nInterval = 500;
	m_nSIngleRegNo = 40001;
	m_nMultiRegNo = 40001;
	m_nMultiRegCount = 10;
	m_sMultiRegValue = _T("");
	m_sSingleRegValue = _T("");
}


void CModbusRTU_CPPDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_TAB_MOTION, m_tab_Motion);
	DDX_Control(pDX, IDC_MOONSLOGO, m_Logo);
	//m_Logo.SetMouseOnPngPic(IDB_LOGO);
	//m_Logo.SetClickDownPngPic(IDB_LOGO);
	DDX_Control(pDX, IDC_CMB_BAUDRATE, cmb_BaudRate);
	DDX_Control(pDX, IDC_CMB_NODEID, cmb_NodeID);
	DDX_Control(pDX, IDC_CMB_COMPORT, cmb_ComPort);
	//  DDX_Control(pDX, IDC_EDIT_CMDHIS, m_txt_CmdHis);
	DDX_Text(pDX, IDC_EDIT_CMDHIS, m_txt_CmdHis);
	DDX_Check(pDX, IDC_CHK_SHOWSEND, m_bShowSend);
	DDX_Check(pDX, IDC_CHK_SHOWRECEIVED, m_bShowReceived);
	DDX_Check(pDX, IDC_CHK_MONITOR, m_bMonitor);
	DDX_Text(pDX, IDC_EDIT_MONITOR_DRIVESTATUS, m_sDriveStatus);
	DDX_Text(pDX, IDC_EDIT_MONITOR_ALARMCODE, m_sAlarmCode);
	DDX_Text(pDX, IDC_EDIT_MONITOR_ACTUALSPEED, m_sActualSpeed);
	DDX_Text(pDX, IDC_EDIT_MONITOR_TARGETSPEED, m_sTargetSpeed);
	DDX_Text(pDX, IDC_EDIT_MONITOR_ENCODERPOS, m_sEncoderPos);
	DDX_Text(pDX, IDC_EDIT_MONITOR_COMMANDPOS, m_sCommandPos);
	DDX_Text(pDX, IDC_EDIT_INTERVAL, m_nInterval);
	DDV_MinMaxInt(pDX, m_nInterval, 200, 60000);
	DDX_Control(pDX, IDC_EDIT_INTERVAL, m_txt_Interval);
	DDX_Text(pDX, IDC_EDIT_SINGLEREGNO, m_nSIngleRegNo);
	DDX_Text(pDX, IDC_EDIT_MULTIREGNO, m_nMultiRegNo);
	DDX_Text(pDX, IDC_EDIT_MULTIREGCOUNT, m_nMultiRegCount);
	DDX_Text(pDX, IDC_EDIT_MULTIREGVALUE, m_sMultiRegValue);
	DDX_Text(pDX, IDC_EDIT_SINGLEREGVALUE, m_sSingleRegValue);
}

BEGIN_MESSAGE_MAP(CModbusRTU_CPPDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_NOTIFY(TCN_SELCHANGE, IDC_TAB_MOTION, &CModbusRTU_CPPDlg::OnSelchangeTabMotion)
	ON_BN_CLICKED(IDC_BTN_OPEN, &CModbusRTU_CPPDlg::OnBnClickedBtnOpen)
	ON_BN_CLICKED(IDC_BTN_CLOSE, &CModbusRTU_CPPDlg::OnBnClickedBtnClose)
	ON_BN_CLICKED(IDC_BTN_DISABLE, &CModbusRTU_CPPDlg::OnBnClickedBtnDisable)
	ON_BN_CLICKED(IDC_BTN_ENABLE, &CModbusRTU_CPPDlg::OnBnClickedBtnEnable)
	ON_MESSAGE(WM_DATASEND_MESSAGE, &CModbusRTU_CPPDlg::OnDataSend)
	ON_MESSAGE(WM_DATARECEIVED_MESSAGE, &CModbusRTU_CPPDlg::OnDataReceived)
	ON_MESSAGE(WM_BUTTONCLICK_MESSAGE, &CModbusRTU_CPPDlg::OnButtonClick)
	ON_MESSAGE(WM_BUTTONMOUSEDOWN_MESSAGE, &CModbusRTU_CPPDlg::OnButtonMouseDown)
	ON_MESSAGE(WM_BUTTONMOUSEUP_MESSAGE, &CModbusRTU_CPPDlg::OnButtonMouseUp)
	ON_BN_CLICKED(IDC_BUTTON5, &CModbusRTU_CPPDlg::OnBnClickedButton5)
	ON_WM_TIMER()
	ON_BN_CLICKED(IDC_CHK_MONITOR, &CModbusRTU_CPPDlg::OnClickedChkMonitor)
	ON_BN_CLICKED(IDC_BTN_ALARMRESET, &CModbusRTU_CPPDlg::OnBnClickedBtnAlarmreset)
	ON_BN_CLICKED(IDC_BTN_STOP, &CModbusRTU_CPPDlg::OnBnClickedBtnStop)
	ON_BN_CLICKED(IDC_MFCBTN_STOP, &CModbusRTU_CPPDlg::OnBnClickedMfcbtnStop)
	ON_BN_CLICKED(IDC_BTN_READSINGLEREG, &CModbusRTU_CPPDlg::OnBnClickedBtnReadsinglereg)
	ON_BN_CLICKED(IDC_BTN_WRITESINGLEREG, &CModbusRTU_CPPDlg::OnBnClickedBtnWritesinglereg)
	ON_BN_CLICKED(IDC_BTN_READMULTIREG, &CModbusRTU_CPPDlg::OnBnClickedBtnReadmultireg)
	ON_BN_CLICKED(IDC_BTN_WRITEMULTIREG, &CModbusRTU_CPPDlg::OnBnClickedBtnWritemultireg)
END_MESSAGE_MAP()


void CALLBACK OnDataSend()
{
	//CModbusRTU_CPPDlg::UpdateData(TRUE);
	return;
}

// CModbusRTU_CPPDlg message handlers

BOOL CModbusRTU_CPPDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// Add "About..." menu item to system menu.

	GetComm();

	AddBandRate();

	cmb_BaudRate.SetCurSel(0);

	// IDM_ABOUTBOX must be in the system command range.
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}

	// Set the icon for this dialog.  The framework does this automatically
	//  when the application's main window is not a dialog
	SetIcon(m_hIcon, TRUE);			// Set big icon
	SetIcon(m_hIcon, FALSE);		// Set small icon
	
	m_Logo.SetInitPngPic(IDB_LOGO);

	// TODO: Add extra initialization here

	m_Logo.SetWindowPos(NULL, 15, 10, 150, 50, SWP_NOSIZE);

	for (int i  = 1; i < 32; i++)
	{
		CString str;
		str.Format("%d", i);
		cmb_NodeID.AddString(str);
	}
	cmb_NodeID.SetCurSel(0);

	m_tab_Motion.InsertItem(0, "Position Mode");
	m_tab_Motion.InsertItem(1, "Q Program");
	m_tab_Motion.InsertItem(2, "Jog");

	m_PosMode.Create(IDD_DIALOG_POSMODE, GetDlgItem(IDC_TAB_MOTION));
	m_QProgram.Create(IDD_DIALOG_QPROGRAM, GetDlgItem(IDC_TAB_MOTION));
	m_Jog.Create(IDD_DIALOG_JOG, GetDlgItem(IDC_TAB_MOTION));

	CRect rect;
	m_tab_Motion.GetClientRect(&rect);
	rect.top += 25;
	rect.bottom -= 10;
	rect.left += 1;
	rect.right -= 2;

	m_PosMode.MoveWindow(&rect);
	m_QProgram.MoveWindow(&rect);
	m_Jog.MoveWindow(&rect);

	m_PosMode.ShowWindow(1);
	m_QProgram.ShowWindow(0);
	m_Jog.ShowWindow(0);

	m_tab_Motion.SetCurSel(0);

	SetActionEnable();

	SetTimer(WM_TIMER_MONITOR, m_nInterval, NULL);

	CFont font;
	VERIFY(font.CreateFont(
	   12,                        // nHeight
	   0,                         // nWidth
	   0,                         // nEscapement
	   0,                         // nOrientation
	   FW_NORMAL,                 // nWeight
	   FALSE,                     // bItalic
	   FALSE,                     // bUnderline
	   0,                         // cStrikeOut
	   DEFAULT_CHARSET,              // nCharSet
	   OUT_DEFAULT_PRECIS,        // nOutPrecision
	   CLIP_DEFAULT_PRECIS,       // nClipPrecision
	   DEFAULT_QUALITY,           // nQuality
	   DEFAULT_PITCH | FF_SWISS,  // nPitchAndFamily
	   _T("Arial")));                 // lpszFacename

	((CMFCButton*)GetDlgItem(IDC_MFCBTN_STOP))->SetFont(&font);

	((CMFCButton*)GetDlgItem(IDC_MFCBTN_STOP))->SetFaceColor(RGB(255, 0, 0));
	((CMFCButton*)GetDlgItem(IDC_MFCBTN_STOP))->SetTextColor(RGB(255, 255, 0));

	return TRUE;  // return TRUE  unless you set the focus to a control
}


void CModbusRTU_CPPDlg::GetComm()
{
	HANDLE hCom;
	int i,num,k;
	CString str;
	BOOL flag;
	cmb_ComPort.ResetContent();
	flag = false;
	num = 0;
	cmb_ComPort.AddString("");
	for (i = 1;i<=16;i++)
	{
		str.Format("\\\\.\\COM%d",i);
		hCom = CreateFile(str,0,0,0,OPEN_EXISTING,FILE_ATTRIBUTE_NORMAL,0);
		if(INVALID_HANDLE_VALUE != hCom)
		{
			CloseHandle(hCom);
			str = str.Mid(4);
			cmb_ComPort.AddString(str);
			if(flag == FALSE)
			{
				flag = TRUE;
				num = i;
			}
		}
	}
	i=  cmb_ComPort.GetCount();
	if(i <= 1)
	{
		cmb_ComPort.EnableWindow(FALSE);
	}
	else
	{
		k =  cmb_ComPort.GetCount();
		cmb_ComPort.SetCurSel(1);
	}
}

void CModbusRTU_CPPDlg::AddBandRate()
{
	cmb_BaudRate.AddString("9600");
	cmb_BaudRate.AddString("19200");
	cmb_BaudRate.AddString("38400");
	cmb_BaudRate.AddString("57600");
	cmb_BaudRate.AddString("115200");							
}

void CModbusRTU_CPPDlg::SetActionEnable()
{
	if (m_ModbusRTUHelper.IsOpen())
	{
		this->cmb_NodeID.EnableWindow(FALSE);
		this->cmb_BaudRate.EnableWindow(FALSE);
		this->cmb_ComPort.EnableWindow(FALSE);

		((CButton *)GetDlgItem(IDC_BTN_OPEN))->EnableWindow(FALSE);
		((CButton *)GetDlgItem(IDC_BTN_CLOSE))->EnableWindow(TRUE);

		((CButton *)GetDlgItem(IDC_BTN_ENABLE))->EnableWindow(TRUE);
		((CButton *)GetDlgItem(IDC_BTN_DISABLE))->EnableWindow(TRUE);
		((CButton *)GetDlgItem(IDC_BTN_ALARMRESET))->EnableWindow(TRUE);
		((CButton *)GetDlgItem(IDC_MFCBTN_STOP))->EnableWindow(TRUE);

		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_ABSMOVE_START, TRUE);
		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_ABSMOVE_STOP, TRUE);

		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_RELMOVE_START, TRUE);
		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_RELMOVE_STOP, TRUE);

		this->m_QProgram.SetButtonEnable(IDC_BTN_QPROGRAM_START, TRUE);
		this->m_QProgram.SetButtonEnable(IDC_BTN_QPROGRAM_STOP, TRUE);

		this->m_Jog.SetButtonEnable(IDC_BTN_JOG_CWJOG, TRUE);
		this->m_Jog.SetButtonEnable(IDC_BTN_JOG_CCWJOG, TRUE);
	}
	else
	{
		this->cmb_NodeID.EnableWindow(TRUE);
		this->cmb_BaudRate.EnableWindow(TRUE);
		this->cmb_ComPort.EnableWindow(TRUE);

		((CButton *)GetDlgItem(IDC_BTN_OPEN))->EnableWindow(TRUE);
		((CButton *)GetDlgItem(IDC_BTN_CLOSE))->EnableWindow(FALSE);
		
		((CButton *)GetDlgItem(IDC_BTN_ENABLE))->EnableWindow(FALSE);
		((CButton *)GetDlgItem(IDC_BTN_DISABLE))->EnableWindow(FALSE);
		((CButton *)GetDlgItem(IDC_BTN_ALARMRESET))->EnableWindow(FALSE);
		((CButton *)GetDlgItem(IDC_MFCBTN_STOP))->EnableWindow(FALSE);

		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_ABSMOVE_START, FALSE);
		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_ABSMOVE_STOP, FALSE);

		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_RELMOVE_START, FALSE);
		this->m_PosMode.SetButtonEnable(IDC_BTN_POSMODE_RELMOVE_STOP, FALSE);

		this->m_QProgram.SetButtonEnable(IDC_BTN_QPROGRAM_START, FALSE);
		this->m_QProgram.SetButtonEnable(IDC_BTN_QPROGRAM_STOP, FALSE);
		
		this->m_Jog.SetButtonEnable(IDC_BTN_JOG_CWJOG, FALSE);
		this->m_Jog.SetButtonEnable(IDC_BTN_JOG_CCWJOG, FALSE);
	}
}

void CModbusRTU_CPPDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// If you add a minimize button to your dialog, you will need the code below
//  to draw the icon.  For MFC applications using the document/view model,
//  this is automatically done for you by the framework.

void CModbusRTU_CPPDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // device context for painting

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// Center icon in client rectangle
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// Draw the icon
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

// The system calls this function to obtain the cursor to display while the user drags
//  the minimized window.
HCURSOR CModbusRTU_CPPDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}



void CModbusRTU_CPPDlg::OnSelchangeTabMotion(NMHDR *pNMHDR, LRESULT *pResult)
{
	int CurSel = m_tab_Motion.GetCurSel();
	switch(CurSel)
	{
	case 0:
		m_PosMode.ShowWindow(TRUE);
		m_QProgram.ShowWindow(FALSE);
		m_Jog.ShowWindow(FALSE);
		break;
	case 1:
		m_PosMode.ShowWindow(FALSE);
		m_QProgram.ShowWindow(TRUE);
		m_Jog.ShowWindow(FALSE);
		break;
	case 2:
		m_PosMode.ShowWindow(FALSE);
		m_QProgram.ShowWindow(FALSE);
		m_Jog.ShowWindow(TRUE);
		break;
	default:
		;
	}

	*pResult = 0;
}


void CModbusRTU_CPPDlg::OnBnClickedBtnOpen()
{
	this->cmb_NodeID.EnableWindow(FALSE);
	this->cmb_BaudRate.EnableWindow(FALSE);
	this->cmb_ComPort.EnableWindow(FALSE);

	CString str;
	int index = 0;
	index = cmb_ComPort.GetCurSel();
	cmb_ComPort.GetLBText(index, str);
	str.Replace(_T("COM"), _T(""));
	m_Port = atoi(str);

	m_BaudRate = cmb_BaudRate.GetCurSel();

	index = cmb_BaudRate.GetCurSel();
	cmb_BaudRate.GetLBText(index, str);
	m_BaudRate = atoi(str);

	index = cmb_NodeID.GetCurSel();
	cmb_NodeID.GetLBText(index, str);
	m_NodeID = atoi(str);


	int ret = m_ModbusRTUHelper.OpenPort(this->m_Port, this->m_BaudRate, TRUE);
	if (ret != 0)
	{
		AfxMessageBox(_T("Fail to Open Serial Port"));
	}

	SetActionEnable();
}


void CModbusRTU_CPPDlg::OnBnClickedBtnClose()
{
	m_ModbusRTUHelper.ClosePort();
	SetActionEnable();
}


void CModbusRTU_CPPDlg::OnBnClickedBtnEnable()
{
	int ret = m_ModbusRTUHelper.MotorEnable(this->m_NodeID);
}


void CModbusRTU_CPPDlg::OnBnClickedBtnDisable()
{
	int ret = m_ModbusRTUHelper.MotorDisable(this->m_NodeID);
}

void CModbusRTU_CPPDlg::OnBnClickedBtnAlarmreset()
{
	int ret = m_ModbusRTUHelper.AlarmReset(this->m_NodeID);
}


void CModbusRTU_CPPDlg::OnBnClickedBtnStop()
{
	int ret = m_ModbusRTUHelper.StopMoveAndKillBuffer(this->m_NodeID);
}


void CModbusRTU_CPPDlg::OnBnClickedMfcbtnStop()
{
	int ret = m_ModbusRTUHelper.StopMoveAndKillBuffer(this->m_NodeID);
}

afx_msg LRESULT CModbusRTU_CPPDlg::OnButtonClick(WPARAM wParam, LPARAM lParam)
{
	int * id;
	id = (int *)lParam;
	
	double nAC;
	double nDE;
	double nVE;
	int nDI;

	nAC = this->m_PosMode.GetPosModeAccel();
	nDE = this->m_PosMode.GetPosModeDecel();
	nVE = this->m_PosMode.GetPosModeVelocity();

	switch (*id)
	{
	case IDC_BTN_POSMODE_ABSMOVE_START:
		nDI = this->m_PosMode.GetPosModeAbsPos();
		m_ModbusRTUHelper.SetPointtoPointMoveParam(this->m_NodeID, nAC, nDE, nVE, nDI, 100);
		m_ModbusRTUHelper.FeedtoPosition(this->m_NodeID, nDI, 60);
		break;
	case IDC_BTN_POSMODE_ABSMOVE_STOP:
		m_ModbusRTUHelper.ExecuteCommandWithOpcode(this->m_NodeID, 0xE1, 0, 0, 0, 0, 0, 60);
		break;
	case IDC_BTN_POSMODE_RELMOVE_START:
		nDI = this->m_PosMode.GetPosModeRelPos();
		BOOL dir;
		dir = this->m_PosMode.GetRelMoveDir();
		if (!dir)
		{
			nDI = -nDI;
		}
		m_ModbusRTUHelper.SetPointtoPointMoveParam(this->m_NodeID, nAC, nDE, nVE, nDI, 100);
		m_ModbusRTUHelper.FeedtoLength(this->m_NodeID, nDI, 60);
		break;
	case IDC_BTN_POSMODE_RELMOVE_STOP:
		m_ModbusRTUHelper.ExecuteCommandWithOpcode(this->m_NodeID, 0xE1, 0, 0, 0, 0, 0, 60);
		break;
	case IDC_BTN_QPROGRAM_START:
		int nSegment;
		nSegment = this->m_QProgram.GetQProgramSegment();
		m_ModbusRTUHelper.ExecuteCommandWithOpcode(this->m_NodeID, 0x78, nSegment, 0, 0, 0, 0);
		break;
	case IDC_BTN_QPROGRAM_STOP:
		m_ModbusRTUHelper.ExecuteCommandWithOpcode(this->m_NodeID, 0xE1, 0, 0, 0, 0, 0, 60);
		break;
	case IDC_BTN_JOG_CWJOG:
		m_ModbusRTUHelper.StartJogging(this->m_NodeID);
		break;
	case IDC_BTN_JOG_CCWJOG:
		m_ModbusRTUHelper.StopJogging(this->m_NodeID);
		break;
	}
	
	return 0;
}

afx_msg LRESULT CModbusRTU_CPPDlg::OnButtonMouseDown(WPARAM wParam, LPARAM lParam)
{
	int * id;
	id = (int *)lParam;	
		
	double dSpeed = m_Jog.GetJogSpeed();
	double dAccel = m_Jog.GetJogAccel();
	double dDecel = m_Jog.GetJogDecel();

	switch (*id)
	{

	case IDC_BTN_JOG_CWJOG:
		m_ModbusRTUHelper.SetJogMoveParam(this->m_NodeID, dAccel, dDecel, dSpeed);
		m_ModbusRTUHelper.StartJogging(this->m_NodeID);
		break;
	case IDC_BTN_JOG_CCWJOG:
		m_ModbusRTUHelper.SetJogMoveParam(this->m_NodeID, dAccel, dDecel, -dSpeed);
		m_ModbusRTUHelper.StartJogging(this->m_NodeID);
		break;
	}
	
	return 0;
}

afx_msg LRESULT CModbusRTU_CPPDlg::OnButtonMouseUp(WPARAM wParam, LPARAM lParam)
{
	int * id;
	id = (int *)lParam;	

	switch (*id)
	{
	case IDC_BTN_JOG_CWJOG:
		m_ModbusRTUHelper.StopJogging(this->m_NodeID);
		break;
	case IDC_BTN_JOG_CCWJOG:
		m_ModbusRTUHelper.StopJogging(this->m_NodeID);
		break;
	}
	
	return 0;
}

afx_msg LRESULT CModbusRTU_CPPDlg::OnDataSend(WPARAM wParam, LPARAM lParam)
{
	UpdateData(TRUE);
	if (!this->m_bShowSend)
	{
		return 0;
	}
	CommandStu commandStu;
	m_ModbusRTUHelper.GetLastCommandSend(commandStu);

	this->m_txt_CmdHis.Append(_T("-> "));


	for (int i = 0; i < commandStu.Count; i++)
	{
		this->m_txt_CmdHis.AppendFormat("%.2X ", commandStu.Values[i]);
	}
	this->m_txt_CmdHis.Append(_T("\r\n"));
	UpdateData(FALSE);

	return 0;
}

afx_msg LRESULT CModbusRTU_CPPDlg::OnDataReceived(WPARAM wParam, LPARAM lParam)
{
	UpdateData(TRUE);
	if (!this->m_bShowReceived)
	{
		return 0;
	}
	CommandStu commandStu;
	m_ModbusRTUHelper.GetLastCommandReceived(commandStu);

	this->m_txt_CmdHis.Append(_T("<- "));


	for (int i = 0; i < commandStu.Count; i++)
	{
		this->m_txt_CmdHis.AppendFormat("%.2X ", commandStu.Values[i]);
	}
	this->m_txt_CmdHis.Append(_T("\r\n"));
	UpdateData(FALSE);

	return 0;
}

void CModbusRTU_CPPDlg::OnBnClickedButton5()
{
	this->m_txt_CmdHis = "";
	UpdateData(FALSE);
}


void CModbusRTU_CPPDlg::OnTimer(UINT_PTR nIDEvent)
{
	int ret = 0;

	if (nIDEvent == WM_TIMER_MONITOR)
	{
		UpdateData(TRUE);
		if (this->m_bMonitor)
		{
			int nSC = 0;
			int nAL = 0;
			int nIV0 = 0;
			int nIV1 = 0;
			int nIE = 0;
			int nIP = 0;
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 1, &nSC);
			if (ret == 0)
			{
				m_sDriveStatus.Format("%d", nSC);
			}
			UpdateData(FALSE);
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 0, &nAL);
			if (ret == 0)
			{
				m_sAlarmCode.Format("%d", nAL);
			}
			UpdateData(FALSE);
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 10, &nIV0);
			if (ret == 0)
			{
				m_sActualSpeed.Format("%.3f  rps", nIV0 / 240.0);
			}
			UpdateData(FALSE);
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 11, &nIV1);
			if (ret == 0)
			{
				m_sTargetSpeed.Format("%.3f rps", nIV1 / 240.0);
			}
			UpdateData(FALSE);
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 4, &nIE);
			if (ret == 0)
			{
				m_sEncoderPos.Format("%d Steps", nIE);
			}
			UpdateData(FALSE);
			ret = m_ModbusRTUHelper.ReadSingleSCLRegister(this->m_NodeID, 6, &nIP);
			if (ret == 0)
			{
				m_sCommandPos.Format("%d Steps", nIP);
			}
			UpdateData(FALSE);
		}
	}
	CDialogEx::OnTimer(nIDEvent);
}


BOOL CModbusRTU_CPPDlg::DestroyWindow()
{
	KillTimer(WM_TIMER_MONITOR);
	return CDialogEx::DestroyWindow();
}


void CModbusRTU_CPPDlg::OnClickedChkMonitor()
{
	UpdateData(TRUE);
	//KillTimer(WM_TIMER_MONITOR);
	SetTimer(WM_TIMER_MONITOR, this->m_nInterval, NULL);
	m_txt_Interval.SetReadOnly(this->m_bMonitor);
}



void CModbusRTU_CPPDlg::OnOK()
{
	// TODO: Add your specialized code here and/or call the base class

	//CDialogEx::OnOK();
}


void CModbusRTU_CPPDlg::OnCancel()
{
	// TODO: Add your specialized code here and/or call the base class

	CDialogEx::OnCancel();
}

void CModbusRTU_CPPDlg::OnBnClickedBtnReadsinglereg()
{
	int value;
	int ret = m_ModbusRTUHelper.ReadSingleHoldingRegister(this->m_NodeID, this->m_nSIngleRegNo - 40001, &value);
	if (ret == 0)
	{
		this->m_sSingleRegValue.Format("%.4X", value);
		UpdateData(FALSE);
	}
}


void CModbusRTU_CPPDlg::OnBnClickedBtnWritesinglereg()
{
	int value = strtol(this->m_sSingleRegValue, NULL, 16);
	int ret = m_ModbusRTUHelper.WriteSingleHoldingRegister(this->m_NodeID, this->m_nSIngleRegNo - 40001, value);
}


void CModbusRTU_CPPDlg::OnBnClickedBtnReadmultireg()
{
	StructRegisterValue holderRegister;
	int ret = m_ModbusRTUHelper.ReadMultiHoldingRegisters(this->m_NodeID, this->m_nMultiRegNo - 40001, this->m_nMultiRegCount, holderRegister);
	if (ret == 0)
	{
		for (int i = 0; i < holderRegister.Count; i++)
		{
			this->m_sMultiRegValue.AppendFormat("%.4X ", holderRegister.Values[i]);
		}
		this->m_sMultiRegValue.Trim();
		UpdateData(FALSE);
	}
}


void CModbusRTU_CPPDlg::OnBnClickedBtnWritemultireg()
{
	CStringArray strArr;
	strArr.SetSize(0, 1);
	int index = 0;
	CString sTemp = this->m_sMultiRegValue;
	while (true)
	{
		index = sTemp.Find(_T(" "));
		if (index == -1)
		{
			strArr.Add(sTemp);
			break;
		}
		strArr.Add(sTemp.Left(index));
		sTemp = sTemp.Mid(index+1, sTemp.GetLength() - index);
	}
	int* valueList = new int[strArr.GetSize()];
	for (int i = 0; i < strArr.GetSize(); i++)
	{
		valueList[i] = strtol(strArr[i], NULL, 16);
	}
	int ret = m_ModbusRTUHelper.WriteMultiHoldingRegisters(this->m_NodeID, this->m_nMultiRegNo - 40001, this->m_nMultiRegCount, valueList);
	if (ret == 0)
	{
	}
}
