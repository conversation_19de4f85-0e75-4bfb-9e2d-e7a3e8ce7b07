{"version": "0.2.0", "configurations": [{"name": "Debug hermeticFrame", "type": "cppvsdbg", "request": "launch", "program": "${workspaceFolder}/hermetic/Release/hermeticFrame.exe", "args": [], "stopAtEntry": false, "cwd": "${workspaceFolder}/hermetic/Release", "environment": [], "console": "externalTerminal", "preLaunchTask": "Build hermeticFrame"}, {"name": "Attach to Process", "type": "cppvsdbg", "request": "attach", "processId": "${command:pickProcess}"}]}